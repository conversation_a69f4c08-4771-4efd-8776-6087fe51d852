/**
 * Terms and Conditions models based on ClubM API
 * Matches the API schemas for /api/app/terms endpoints
 */

import {z} from "zod";
import {PaginationRequest, PaginationResponse} from "./common.models";

// Term Status enum
export enum TermStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DRAFT = "draft"
}

// Term Type enum (matching API numeric values)
export enum TermType {
  TERMS_OF_USE = 0,
  PRIVACY_POLICY = 1,
  PAYMENT_TERMS = 2,
  GENERAL = 3
}

// Term Status enum (matching API numeric values)
export enum TermStatusNumeric {
  INACTIVE = 0,
  ACTIVE = 1,
  DRAFT = 2
}

// Term Version interface (for versions array in API response)
export interface TermVersion {
  id: number;
  termId: number;
  version: number;
  content: string;
  createdAt: string;
  createdBy: string;
}

// API Response interfaces
export interface Term {
  id: number;
  title: string;
  content: string;
  type: TermType;
  status: TermStatus;
  version: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// New Term interface matching the API response structure for events/products
export interface ApiTerm {
  id: number;
  type: number;
  name: string;
  lastVersion: number;
  status: number;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  versions: TermVersion[];
}

export interface TermAcceptance {
  id: string;
  termId: number;
  version: number;
  userId: number;
  acceptedAt: string;
  ipAddress?: string;
  userAgent?: string;
}

// API Request interfaces
export interface AcceptTermRequest {
  // Não precisa de body, apenas path params
}

// API Response interfaces
export interface AcceptTermResponse {
  id: string;
  termId: number;
  version: number;
  userId: number;
  acceptedAt: string;
}

export interface TermsListParams extends PaginationRequest {
  search?: string;
  type?: TermType;
  status?: TermStatus;
  createdAtStart?: string;
  createdAtEnd?: string;
}

export interface TermsListResponse extends PaginationResponse<Term> {}

export interface TermDetailsResponse {
  success: boolean;
  data: Term;
}

export interface TermAcceptanceResponse {
  success: boolean;
  data: TermAcceptance | null;
  alreadyAccepted?: boolean;
}

// Schemas de validação com Zod
export const AcceptTermSchema = z.object({
  // Não precisa de validação de body para aceitar termos
});

export const TermsListParamsSchema = z.object({
  search: z.string().optional(),
  type: z.nativeEnum(TermType).optional(),
  status: z.nativeEnum(TermStatus).optional(),
  createdAtStart: z.string().datetime().optional(),
  createdAtEnd: z.string().datetime().optional(),
  page: z.number().min(1).optional(),
  pageSize: z.number().min(1).max(100).optional()
});

// Utility functions
export const getActiveTerms = (terms: Term[]): Term[] => {
  return terms.filter(
    (term) => term.status === TermStatus.ACTIVE && term.isActive
  );
};

export const getTermsByType = (terms: Term[], type: TermType): Term[] => {
  return terms.filter((term) => term.type === type);
};

export const getLatestVersion = (terms: Term[]): Term | undefined => {
  return terms.reduce((latest, current) => {
    if (!latest || current.version > latest.version) {
      return current;
    }
    return latest;
  }, undefined as Term | undefined);
};

// Utility function to get the latest version from ApiTerm
export const getLatestVersionFromApiTerm = (
  apiTerm: ApiTerm
): TermVersion | undefined => {
  if (!apiTerm.versions || apiTerm.versions.length === 0) {
    return undefined;
  }

  return apiTerm.versions.reduce((latest, current) => {
    if (!latest || current.version > latest.version) {
      return current;
    }
    return latest;
  }, undefined as TermVersion | undefined);
};
