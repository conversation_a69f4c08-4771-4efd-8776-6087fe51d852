import { z } from "zod";

/**
 * Status de um objetivo
 */
export enum ObjectiveStatus {
  ACTIVE = "active",
  COMPLETED = "completed",
  PAUSED = "paused",
  CANCELLED = "cancelled"
}

/**
 * Tipo de objetivo
 */
export enum ObjectiveType {
  PERSONAL = "personal",
  PROFESSIONAL = "professional",
  FINANCIAL = "financial",
  HEALTH = "health",
  EDUCATION = "education",
  OTHER = "other"
}

/**
 * Categoria de objetivo
 */
export enum ObjectiveCategory {
  GENERAL = "general",
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  YEARLY = "yearly",
  MILESTONE = "milestone"
}

/**
 * Interface para um objetivo
 */
export interface Objective {
  id: string | number;
  title: string;
  description: string;
  progress: number; // 0 to 1 (0% to 100%)
  accomplished?: string;
  value?: number; // Valor em centavos
  createdAt: string;
  updatedAt?: string;
  isCompleted: boolean;
  status: ObjectiveStatus;
  type: ObjectiveType;
  category: ObjectiveCategory;
  targetDate?: string;
  completedAt?: string;
  userId: string | number;
}

/**
 * Request para criar um objetivo
 */
export interface CreateObjectiveRequest {
  title: string;
  description: string;
  value?: number;
  type?: ObjectiveType;
  category?: ObjectiveCategory;
  targetDate?: string;
}

/**
 * Request para atualizar um objetivo
 */
export interface UpdateObjectiveRequest {
  title?: string;
  description?: string;
  value?: number;
  type?: ObjectiveType;
  category?: ObjectiveCategory;
  status?: ObjectiveStatus;
  progress?: number;
  targetDate?: string;
}

/**
 * Parâmetros para buscar objetivos
 */
export interface ObjectivesParams {
  search?: string;
  page?: number;
  pageSize?: number;
  status?: ObjectiveStatus;
  type?: ObjectiveType;
  category?: ObjectiveCategory;
}

/**
 * Resposta da API para objetivos
 */
export interface ObjectivesResponse {
  data: Objective[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * Estatísticas de objetivos
 */
export interface ObjectiveStats {
  total: number;
  completed: number;
  active: number;
  paused: number;
  cancelled: number;
  completionRate: number; // 0 to 1
  averageProgress: number; // 0 to 1
}

// Schemas de validação com Zod
export const CreateObjectiveSchema = z.object({
  title: z
    .string()
    .min(1, "Título é obrigatório")
    .max(200, "Título muito longo"),
  description: z
    .string()
    .min(1, "Descrição é obrigatória")
    .max(1000, "Descrição muito longa"),
  value: z.number().min(0, "Valor não pode ser negativo").optional(),
  type: z.nativeEnum(ObjectiveType).optional(),
  category: z.nativeEnum(ObjectiveCategory).optional(),
  targetDate: z.string().datetime("Data alvo inválida").optional()
});

export const UpdateObjectiveSchema = z.object({
  title: z
    .string()
    .min(1, "Título é obrigatório")
    .max(200, "Título muito longo")
    .optional(),
  description: z
    .string()
    .min(1, "Descrição é obrigatória")
    .max(1000, "Descrição muito longa")
    .optional(),
  value: z.number().min(0, "Valor não pode ser negativo").optional(),
  type: z.nativeEnum(ObjectiveType).optional(),
  category: z.nativeEnum(ObjectiveCategory).optional(),
  status: z.nativeEnum(ObjectiveStatus).optional(),
  progress: z
    .number()
    .min(0, "Progresso não pode ser negativo")
    .max(1, "Progresso não pode ser maior que 100%")
    .optional(),
  targetDate: z.string().datetime("Data alvo inválida").optional()
});

export const ObjectivesParamsSchema = z.object({
  search: z.string().optional(),
  page: z.number().min(1, "Página deve ser maior que 0").optional(),
  pageSize: z
    .number()
    .min(1, "Tamanho da página deve ser maior que 0")
    .max(100, "Tamanho da página muito grande")
    .optional(),
  status: z.nativeEnum(ObjectiveStatus).optional(),
  type: z.nativeEnum(ObjectiveType).optional(),
  category: z.nativeEnum(ObjectiveCategory).optional()
});

// Tipos derivados dos schemas
export type CreateObjectiveData = z.infer<typeof CreateObjectiveSchema>;
export type UpdateObjectiveData = z.infer<typeof UpdateObjectiveSchema>;
export type ObjectivesParamsData = z.infer<typeof ObjectivesParamsSchema>;
