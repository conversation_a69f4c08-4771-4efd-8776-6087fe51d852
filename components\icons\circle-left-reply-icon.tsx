import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface CircleLeftReplyIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const CircleLeftReplyIcon: React.FC<CircleLeftReplyIconProps> = (props) => {
  const color = useMemo(() => props.replaceColor ?? "#DFE9F0", [props.replaceColor]);

  return (
    <Svg width={props.width ?? 12} height={props.height ?? 12} viewBox="0 0 12 12" fill="none" {...props}>
      <Path
        d="M3.06425 9.59481L0.192624 10.5682Q0 10.6335 -0.192624 10.5682Q-0.305654 10.5299 -0.395417 10.4513Q-0.48518 10.3726 -0.538012 10.2656Q-0.590843 10.1586 -0.598699 10.0395Q-0.606555 9.92041 -0.56824 9.80738L0.405194 6.93575Q0.0674236 6.19178 -0.0312782 5.37462Q-0.145633 4.42787 0.075084 3.50014Q0.295801 2.5724 0.824253 1.77858Q1.35271 0.984759 2.12344 0.423174Q2.89418 -0.138412 3.81174 -0.398207Q4.7293 -0.658003 5.68004 -0.583833Q6.63078 -0.509662 7.49695 -0.110712Q8.36312 0.288239 9.03744 0.962557Q9.71176 1.63688 10.1107 2.50305Q10.5097 3.36922 10.5838 4.31996Q10.658 5.2707 10.3982 6.18826Q10.1384 7.10582 9.57683 7.87656Q9.01524 8.64729 8.22142 9.17575Q7.4276 9.7042 6.49986 9.92492Q5.57213 10.1456 4.62538 10.0313Q3.80822 9.93258 3.06425 9.59481ZM4.76928 8.83994Q4.03548 8.75131 3.37784 8.41395Q3.26951 8.35838 3.14808 8.34943Q3.02666 8.34048 2.91136 8.37956L0.958426 9.04157L1.62044 7.08864Q1.65952 6.97334 1.65057 6.85192Q1.64162 6.73049 1.58605 6.62216Q1.2487 5.96452 1.16006 5.23073Q1.07143 4.49693 1.2425 3.77788Q1.41357 3.05882 1.82316 2.44356Q2.23274 1.82829 2.83011 1.39303Q3.42748 0.957763 4.13865 0.756404Q4.84982 0.555045 5.58671 0.612532Q6.3236 0.670019 6.99493 0.979232Q7.66627 1.28844 8.18891 1.81109Q8.71155 2.33373 9.02077 3.00507Q9.32998 3.67641 9.38747 4.41329Q9.44495 5.15018 9.2436 5.86135Q9.04224 6.57252 8.60697 7.16989Q8.1717 7.76726 7.55644 8.17685Q6.94118 8.58643 6.22212 8.7575Q5.50307 8.92857 4.76928 8.83994ZM3.78452 2.73638C4.01884 2.50207 4.39874 2.50207 4.63305 2.73638C4.86736 2.97069 4.86736 3.3506 4.63305 3.58491L4.07913 4.13883L6.31319 4.13865Q6.39871 4.13866 6.48111 4.14621Q6.78782 4.17435 7.05115 4.30721Q7.28315 4.42425 7.48147 4.62257Q7.96538 5.10649 7.96538 5.79085L7.96538 6.31695C7.96538 6.64832 7.69675 6.91695 7.36538 6.91695C7.03401 6.91695 6.76538 6.64832 6.76538 6.31695L6.76538 5.79085Q6.76538 5.60354 6.63294 5.4711Q6.58327 5.42143 6.52589 5.39039Q6.45241 5.35064 6.36627 5.34143Q6.34033 5.33866 6.31323 5.33865L4.07891 5.33883L4.63305 5.89297C4.86736 6.12728 4.86736 6.50718 4.63305 6.7415C4.39874 6.97581 4.01884 6.97581 3.78452 6.7415L2.20623 5.1632Q2.12184 5.07881 2.07617 4.96855Q2.03049 4.85829 2.03049 4.73894Q2.03049 4.61959 2.07617 4.50933Q2.12184 4.39907 2.20623 4.31468L3.78452 2.73638Z"
        fill={color as string}
        transform="translate(1 0.999953)"
      />
    </Svg>
  );
};

export default CircleLeftReplyIcon;

