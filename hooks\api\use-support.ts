/**
 * Hook de suporte
 * Integra POST /api/app/support seguindo padrão de React Query
 */

import {useMutation, UseMutationOptions} from "@tanstack/react-query";
import {BaseApiError} from "@/services/api/base/api-errors";
import SupportService, {
  CreateSupportTicketRequest,
  SupportTicketViewModel
} from "@/services/api/support/support.service";
import {ApiLogger} from "@/services/api/base/api-logger";

export const supportKeys = {
  all: ["support"] as const
};

export const useCreateSupportTicket = (
  options?: UseMutationOptions<
    SupportTicketViewModel,
    BaseApiError,
    CreateSupportTicketRequest
  >
) => {
  return useMutation({
    mutationFn: (data) => SupportService.createSupportTicket(data),
    onSuccess: (data, variables) => {
      ApiLogger.info("Ticket de suporte criado", {
        id: data?.id,
        type: variables.Type
      });
    },
    ...options
  });
};

