/**
 * Hook for managing opportunity terms
 */

import {useQuery} from "@tanstack/react-query";
import {OpportunitiesService} from "@/services/api/opportunities/opportunities.service";
import {ApiTerm, getLatestVersionFromApiTerm} from "@/models/api/terms.models";

/**
 * Hook para buscar termos de oportunidades
 */
export const useOpportunityTerms = (enabled: boolean = true) => {
  return useQuery({
    queryKey: ["opportunity-terms"],
    queryFn: () => OpportunitiesService.getOpportunityTerms(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
};

/**
 * Hook para obter o termo de oportunidade (API retorna um objeto único)
 */
export const useOpportunityTerm = (enabled: boolean = true) => {
  const {data: term, ...rest} = useOpportunityTerms(enabled);

  // A API retorna um objeto único, não um array
  return {
    data: term || null,
    ...rest
  };
};

/**
 * Utility function para obter a versão mais recente do termo de oportunidade
 */
export const getLatestOpportunityTermVersion = (term: ApiTerm | undefined) => {
  if (!term) {
    return null;
  }

  return getLatestVersionFromApiTerm(term);
};
