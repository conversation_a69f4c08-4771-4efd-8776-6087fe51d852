/**
 * Hook to fetch Regulation term
 */

import {useQuery} from "@tanstack/react-query";
import RegulationTermsService from "@/services/api/terms/regulation-terms.service";
import {ApiTerm} from "@/models/api/terms.models";

export const REGULATION_TERM_KEYS = {
  all: ["regulation-term"] as const,
  detail: () => [...REGULATION_TERM_KEYS.all, "detail"] as const
};

export const useRegulationTerm = (enabled: boolean = true) => {
  return useQuery<ApiTerm | null, Error>({
    queryKey: REGULATION_TERM_KEYS.detail(),
    queryFn: () => RegulationTermsService.getRegulationTerm(),
    enabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  });
};

