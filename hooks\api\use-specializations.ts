/**
 * Hooks para gerenciamento de especializações
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import SpecializationsService, {
  Specialization,
  CreateSpecializationRequest,
  SpecializationsListParams
} from "@/services/api/specializations/specializations.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";
import {PaginationResponse} from "@/models/api/common.models";

// Query keys para cache
export const specializationsKeys = {
  all: ["specializations"] as const,
  lists: () => [...specializationsKeys.all, "list"] as const,
  list: (params?: SpecializationsListParams) => [...specializationsKeys.lists(), params] as const
};

/**
 * Hook para buscar lista de especializações
 */
export const useSpecializations = (
  params?: SpecializationsListParams,
  options?: UseQueryOptions<PaginationResponse<Specialization>, BaseApiError>
) => {
  return useQuery({
    queryKey: specializationsKeys.list(params),
    queryFn: () => SpecializationsService.getSpecializations(params),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para criar nova especialização
 */
export const useCreateSpecialization = (
  options?: UseMutationOptions<Specialization, BaseApiError, CreateSpecializationRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => SpecializationsService.createSpecialization(data),
    onSuccess: (data) => {
      // Invalidar cache das listas de especializações
      queryClient.invalidateQueries({
        queryKey: specializationsKeys.lists()
      });

      ApiLogger.info("Especialização criada e cache atualizado", {
        id: data.id,
        name: data.name
      });
    },
    onError: (error) => {
      ApiLogger.error("Erro ao criar especialização", error);
    },
    ...options
  });
};
