import {z} from "zod";
import {isValidCpf} from "./cpf";

// Common validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const cleanPhone = phone.replace(/\D/g, "");
  return cleanPhone.length >= 10 && cleanPhone.length <= 11;
};

export const validateCEP = (cep: string): boolean => {
  const cleanCEP = cep.replace(/\D/g, "");
  return cleanCEP.length === 8;
};

export const validateBirthDate = (dateString: string): boolean => {
  if (!dateString) return false;

  // Accept formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD
  const ddmmyyyyRegex = /^(\d{2})[/-](\d{2})[/-](\d{4})$/;
  const yyyymmddRegex = /^(\d{4})-(\d{2})-(\d{2})$/;

  let date: Date;

  const ddmmMatch = ddmmyyyyRegex.exec(dateString);
  if (ddmmMatch) {
    // Parse DD/MM/YYYY or DD-MM-YYYY format
    const match = ddmmMatch;
    if (!match) return false;

    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10) - 1; // Month is 0-indexed in Date constructor
    const year = parseInt(match[3], 10);

    date = new Date(year, month, day);

    // Verify the date is valid (handles invalid dates like 31/02/2023)
    if (
      date.getDate() !== day ||
      date.getMonth() !== month ||
      date.getFullYear() !== year
    ) {
      return false;
    }
  } else if (yyyymmddRegex.test(dateString)) {
    // Parse YYYY-MM-DD format
    date = new Date(dateString);
    if (isNaN(date.getTime())) return false;
  } else {
    return false;
  }

  const now = new Date();
  const minAge = new Date(
    now.getFullYear() - 120,
    now.getMonth(),
    now.getDate()
  );
  const maxAge = new Date(
    now.getFullYear() - 18,
    now.getMonth(),
    now.getDate()
  );

  return date >= minAge && date <= maxAge;
};

export const validateURL = (url: string): boolean => {
  if (!url) return true; // Optional field
  try {
    new URL(url.startsWith("http") ? url : `https://${url}`);
    return true;
  } catch {
    return false;
  }
};

// Subscription Registration Schema
export const SubscriptionRegistrationSchema = z.object({
  selectedPlan: z
    .string()
    .min(1, "Você deve selecionar um plano para continuar")
});

// Personal Data Registration Schema
export const PersonalDataRegistrationSchema = z.object({
  fullName: z
    .string()
    .min(1, "Nome completo é obrigatório")
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome muito longo"),

  cpf: z
    .string()
    .min(1, "CPF é obrigatório")
    .refine((cpf) => isValidCpf(cpf), "CPF inválido"),

  birthDate: z
    .string()
    .min(1, "Data de nascimento é obrigatória")
    .refine((date) => validateBirthDate(date), "Data de nascimento inválida"),

  gender: z.string().min(1, "Sexo é obrigatório"),

  branch: z.string().min(1, "Filial é obrigatória")
});

// Professional Registration Schema
export const ProfessionalRegistrationSchema = z.object({
  profession: z
    .string()
    .min(1, "Profissão é obrigatória")
    .max(100, "Profissão muito longa"),

  company: z
    .string()
    .min(1, "Empresa é obrigatória")
    .max(100, "Nome da empresa muito longo"),

  position: z
    .string()
    .min(1, "Cargo é obrigatório")
    .max(100, "Cargo muito longo"),

  industry: z
    .string()
    .min(1, "Segmento é obrigatório")
    .max(100, "Segmento muito longo")
});

// Address Registration Schema
export const AddressRegistrationSchema = z.object({
  zipCode: z
    .string()
    .min(1, "CEP é obrigatório")
    .refine((cep) => validateCEP(cep), "CEP inválido"),

  address: z
    .string()
    .min(1, "Endereço é obrigatório")
    .max(200, "Endereço muito longo"),

  number: z
    .string()
    .min(1, "Número é obrigatório")
    .max(20, "Número muito longo"),

  complement: z
    .string()
    .max(100, "Complemento muito longo")
    .optional()
    .or(z.literal("")), // Optional field

  state: z
    .string()
    .min(1, "Estado é obrigatório")
    .max(50, "Estado muito longo"),

  neighborhood: z
    .string()
    .min(1, "Bairro é obrigatório")
    .max(100, "Bairro muito longo"),

  city: z.string().min(1, "Cidade é obrigatória").max(100, "Cidade muito longa")
});

// Contacts Social Registration Schema
export const ContactsSocialRegistrationSchema = z.object({
  email: z
    .string()
    .min(1, "E-mail é obrigatório")
    .refine((email) => validateEmail(email), "E-mail inválido"),

  phone: z
    .string()
    .min(1, "Telefone é obrigatório")
    .refine((phone) => validatePhone(phone), "Telefone inválido"),

  howDidYouKnow: z.string().min(1, "Como você conheceu o Club é obrigatório"),

  facebook: z
    .string()
    .optional()
    .refine((url) => !url || validateURL(url), "URL do Facebook inválida"),

  instagram: z
    .string()
    .optional()
    .refine((url) => !url || validateURL(url), "URL do Instagram inválida"),

  linkedin: z
    .string()
    .optional()
    .refine((url) => !url || validateURL(url), "URL do LinkedIn inválida"),

  website: z
    .string()
    .optional()
    .refine((url) => !url || validateURL(url), "URL do website inválida")
});

// Document Upload Terms Schema
export const DocumentUploadTermsSchema = z.object({
  acceptedTerms: z
    .boolean()
    .refine(
      (val) => val === true,
      "Você deve aceitar o regulamento para continuar"
    ),

  acceptedPrivacy: z
    .boolean()
    .refine(
      (val) => val === true,
      "Você deve aceitar a política de privacidade para continuar"
    ),

  acceptedAdesao: z
    .boolean()
    .refine(
      (val) => val === true,
      "Você deve aceitar o termo de adesão para continuar"
    ),

  documentUploaded: z.boolean().optional() // Document upload might be optional in some cases
});

// Payment Card Registration Schema
export const PaymentCardRegistrationSchema = z.object({
  adhesionCardName: z
    .string()
    .min(1, "Nome do titular é obrigatório")
    .max(100, "Nome muito longo")
    .refine(
      (val) => val.trim().split(/\s+/).filter(Boolean).length >= 2,
      "Informe nome e sobrenome (pelo menos 2 nomes)"
    ),

  adhesionCardNumber: z
    .string()
    .min(1, "Número do cartão é obrigatório")
    .refine((val) => {
      const digitsOnly = val.replace(/\D/g, "");
      return digitsOnly.length >= 13 && digitsOnly.length <= 16;
    }, "Número do cartão deve ter entre 13 e 16 dígitos"),

  adhesionInstallments: z.string().min(1, "Número de parcelas é obrigatório"),

  adhesionExpiryDate: z
    .string()
    .min(1, "Data de validade é obrigatória")
    .regex(/^(0[1-9]|1[0-2])\/\d{2}$/, "Data de validade inválida (MM/AA)"),

  adhesionCvv: z
    .string()
    .min(1, "CVV é obrigatório")
    .min(3, "CVV deve ter pelo menos 3 dígitos")
    .max(4, "CVV deve ter no máximo 4 dígitos"),

  membershipCardName: z
    .string()
    .min(1, "Nome do titular é obrigatório")
    .max(100, "Nome muito longo")
    .refine(
      (val) => val.trim().split(/\s+/).filter(Boolean).length >= 2,
      "Informe nome e sobrenome (pelo menos 2 nomes)"
    ),

  membershipCardNumber: z
    .string()
    .min(1, "Número do cartão é obrigatório")
    .refine((val) => {
      const digitsOnly = val.replace(/\D/g, "");
      return digitsOnly.length >= 13 && digitsOnly.length <= 16;
    }, "Número do cartão deve ter entre 13 e 16 dígitos"),

  membershipExpiryDate: z
    .string()
    .min(1, "Data de validade é obrigatória")
    .regex(/^(0[1-9]|1[0-2])\/\d{2}$/, "Data de validade inválida (MM/AA)"),

  membershipCvv: z
    .string()
    .min(1, "CVV é obrigatório")
    .min(3, "CVV deve ter pelo menos 3 dígitos")
    .max(4, "CVV deve ter no máximo 4 dígitos"),

  useSameCard: z.boolean().optional()
});

// Type exports
export type SubscriptionRegistrationData = z.infer<
  typeof SubscriptionRegistrationSchema
>;
export type PersonalDataRegistrationData = z.infer<
  typeof PersonalDataRegistrationSchema
>;
export type ProfessionalRegistrationData = z.infer<
  typeof ProfessionalRegistrationSchema
>;
export type AddressRegistrationData = z.infer<typeof AddressRegistrationSchema>;
export type ContactsSocialRegistrationData = z.infer<
  typeof ContactsSocialRegistrationSchema
>;
export type DocumentUploadTermsData = z.infer<typeof DocumentUploadTermsSchema>;
export type PaymentCardRegistrationData = z.infer<
  typeof PaymentCardRegistrationSchema
>;

// Validation helper function
export const validateRegistrationStep = <T>(
  schema: z.ZodSchema<T>,
  data: unknown
): {isValid: boolean; errors: Record<string, string>; data?: T} => {
  try {
    const validatedData = schema.parse(data);
    return {isValid: true, errors: {}, data: validatedData};
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach((err) => {
        const field = err.path.join(".");
        // Note: This utility function doesn't have access to translation
        // The error message should be translated by the calling component
        errors[field] = err.message;
      });
      return {isValid: false, errors};
    }
    return {isValid: false, errors: {general: "Erro de validação"}};
  }
};
