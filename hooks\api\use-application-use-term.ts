/**
 * Hook to fetch Application Use term
 */

import {useQuery} from "@tanstack/react-query";
import ApplicationUseTermsService from "@/services/api/terms/application-use-terms.service";
import {ApiTerm} from "@/models/api/terms.models";

export const APPLICATION_USE_TERM_KEYS = {
  all: ["application-use-term"] as const,
  detail: () => [...APPLICATION_USE_TERM_KEYS.all, "detail"] as const
};

export const useApplicationUseTerm = (enabled: boolean = true) => {
  return useQuery<ApiTerm | null, Error>({
    queryKey: APPLICATION_USE_TERM_KEYS.detail(),
    queryFn: () => ApplicationUseTermsService.getApplicationUseTerm(),
    enabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  });
};

