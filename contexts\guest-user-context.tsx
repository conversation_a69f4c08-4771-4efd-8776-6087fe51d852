import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
  useMemo,
  useEffect
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {useQueryClient} from "@tanstack/react-query";
import {notificationsKeys} from "@/hooks/api/use-notifications";
import {chatsKeys} from "@/hooks/api/use-chats";
import {referralsKeys} from "@/hooks/api/use-referrals";

const GUEST_USER_STORAGE_KEY = "@club_m_guest_user_data";

export interface GuestUserData {
  document: string;
  name: string;
  phone: string;
}

export interface GuestUserContextType {
  isGuest: boolean;
  guestData: GuestUserData | null;
  isLoading: boolean;
  setGuestUser: (data: GuestUserData) => Promise<void>;
  clearGuestUser: () => Promise<void>;
}

const GuestUserContext = createContext<GuestUserContextType | undefined>(
  undefined
);

interface GuestUserProviderProps {
  readonly children: ReactNode;
}

export function GuestUserProvider({children}: GuestUserProviderProps) {
  const [guestData, setGuestData] = useState<GuestUserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();

  // Load guest data from AsyncStorage on mount
  useEffect(() => {
    const loadGuestData = async () => {
      try {
        const storedData = await AsyncStorage.getItem(GUEST_USER_STORAGE_KEY);
        if (storedData) {
          const parsedData = JSON.parse(storedData) as GuestUserData;
          setGuestData(parsedData);
          console.log("🔄 Guest user data loaded from storage:", !!parsedData);
        }
      } catch (error) {
        console.error("❌ Error loading guest user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGuestData();
  }, []);

  const setGuestUser = useCallback(
    async (data: GuestUserData) => {
      try {
        console.log("🔄 Salvando dados do guest user...");

        // Salvar dados do guest user
        setGuestData(data);
        await AsyncStorage.setItem(
          GUEST_USER_STORAGE_KEY,
          JSON.stringify(data)
        );
        console.log("✅ Guest user data saved to storage");

        // Clear all guest-disabled APIs cache when entering guest mode
        queryClient.removeQueries({queryKey: notificationsKeys.all});
        queryClient.removeQueries({queryKey: chatsKeys.all});
        queryClient.removeQueries({queryKey: referralsKeys.all});
        queryClient.removeQueries({queryKey: ["indications"]});
        queryClient.removeQueries({queryKey: ["polls"]});
        queryClient.removeQueries({queryKey: ["forms"]});
        console.log("🧹 Cleared cache for guest-disabled APIs on setGuestUser");
      } catch (error) {
        console.error("❌ Error saving guest user data:", error);
      }
    },
    [queryClient]
  );

  const clearGuestUser = useCallback(async () => {
    try {
      setGuestData(null);
      await AsyncStorage.removeItem(GUEST_USER_STORAGE_KEY);
      console.log("✅ Guest user data cleared from storage");
    } catch (error) {
      console.error("❌ Error clearing guest user data:", error);
    }
  }, []);

  const isGuest = useMemo(() => !!guestData, [guestData]);

  // Clear notifications, polls, and forms cache when guest status changes
  useEffect(() => {
    if (isGuest) {
      // Remove all cached data for guest-disabled APIs
      queryClient.removeQueries({queryKey: notificationsKeys.all});
      queryClient.removeQueries({queryKey: chatsKeys.all});
      queryClient.removeQueries({queryKey: referralsKeys.all});
      queryClient.removeQueries({queryKey: ["indications"]});
      queryClient.removeQueries({queryKey: ["polls"]});
      queryClient.removeQueries({queryKey: ["forms"]});
      console.log("🧹 Cleared cache for guest-disabled APIs");
    }
  }, [isGuest, queryClient]);

  const contextValue = useMemo(
    () => ({
      isGuest,
      guestData,
      isLoading,
      setGuestUser,
      clearGuestUser
    }),
    [isGuest, guestData, isLoading, setGuestUser, clearGuestUser]
  );

  return (
    <GuestUserContext.Provider value={contextValue}>
      {children}
    </GuestUserContext.Provider>
  );
}

export function useGuestUser(): GuestUserContextType {
  const context = useContext(GuestUserContext);

  if (context === undefined) {
    throw new Error("useGuestUser must be used within a GuestUserProvider");
  }

  return context;
}

export default GuestUserProvider;
