import React, {useState, useCallback, useMemo} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  ListRenderItemInfo
} from "react-native";
import {useTranslation} from "react-i18next";
import {UserInfo} from "@/models/api/auth.models";
import {useUsers, useCurrentUser} from "@/hooks/api/use-users";
import {useCreateChat} from "@/hooks/api/use-chats";
import {ChatType} from "@/models/api/chats.models";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import Search from "../search";
import CheckCircleIcon from "../icons/check-circle-icon";
import UserIcon from "../icons/user-icon";
import styles from "@/styles/modals/user-selection-modal.style";

export interface UserSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onChatCreated?: (chatId: string) => void;
}

interface SelectableUser extends UserInfo {
  isSelected: boolean;
}

const UserSelectionModal: React.FC<UserSelectionModalProps> = ({
  visible,
  onClose,
  onChatCreated
}) => {
  const {t} = useTranslation();
  const [searchValue, setSearchValue] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(
    new Set()
  );

  // Buscar usuário atual para excluir da lista
  const {data: currentUser} = useCurrentUser();

  // Buscar lista de usuários
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    isError: isUsersError
  } = useUsers(
    {
      search: searchValue || undefined,
      pageSize: 50
    },
    {
      enabled: visible
    } as any
  );

  // Hook para criar chat
  const createChatMutation = useCreateChat({
    onSuccess: (chat) => {
      // Navegar imediatamente para o chat recém-criado, sem exibir alert/toast
      onChatCreated?.(chat.id);
      onClose();
    },
    onError: (error) => {
      Alert.alert(
        t("messages.createChat.error", "Erro"),
        t(
          "messages.createChat.errorMessage",
          "Erro ao criar chat. Tente novamente."
        ),
        [
          {
            text: t("common.ok", "OK")
          }
        ]
      );
    }
  });

  // Filtrar usuários (excluir usuário atual e mapear para SelectableUser)
  const selectableUsers: SelectableUser[] = useMemo(() => {
    if (!usersData?.data || !currentUser) return [];

    return usersData.data
      .filter((user) => user.id !== currentUser.id)
      .map((user) => ({
        ...user,
        isSelected: selectedUserIds.has(user.id)
      }));
  }, [usersData?.data, currentUser, selectedUserIds]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleUserToggle = useCallback((userId: string) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  }, []);

  const handleCreateChat = useCallback(() => {
    if (selectedUserIds.size === 0) {
      Alert.alert(
        t("messages.createChat.validation", "Validação"),
        t(
          "messages.createChat.selectUsers",
          "Selecione pelo menos um usuário para criar o chat."
        )
      );
      return;
    }

    const memberIds = Array.from(selectedUserIds);
    createChatMutation.mutate({
      type: selectedUserIds.size === 1 ? ChatType.DIRECT : ChatType.GROUP,
      memberIds,
      isPrivate: false
    });
  }, [selectedUserIds, createChatMutation, t]);

  const handleClose = useCallback(() => {
    setSearchValue("");
    setSelectedUserIds(new Set());
    onClose();
  }, [onClose]);

  const renderUserItem = useCallback(
    ({item}: ListRenderItemInfo<SelectableUser>) => (
      <TouchableOpacity
        style={[styles.userItem, item.isSelected && styles.userItemSelected]}
        onPress={() => handleUserToggle(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.userInfo}>
          <View style={styles.userAvatar}>
            <UserIcon />
          </View>
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{item.name}</Text>
            <Text style={styles.userEmail}>{item.email}</Text>
            {item.companyName && (
              <Text style={styles.userCompany}>{item.companyName}</Text>
            )}
          </View>
        </View>
        <View style={styles.checkboxContainer}>
          {item.isSelected && <CheckCircleIcon width={24} height={24} />}
        </View>
      </TouchableOpacity>
    ),
    [handleUserToggle]
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            {t("messages.createChat.selectUsers", "Selecionar Usuários")}
          </Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Search
            searchBarValue={searchValue}
            onSearchBarChange={handleSearchChange}
            searchBarPlaceholder={t(
              "messages.createChat.searchPlaceholder",
              "Buscar usuários..."
            )}
          />

          {selectedUserIds.size > 0 && (
            <View style={styles.selectedCount}>
              <Text style={styles.selectedCountText}>
                {t(
                  "messages.createChat.selectedCount",
                  "{{count}} usuário(s) selecionado(s)",
                  {count: selectedUserIds.size}
                )}
              </Text>
            </View>
          )}

          {isLoadingUsers ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" />
              <Text style={styles.loadingText}>
                {t(
                  "messages.createChat.loadingUsers",
                  "Carregando usuários..."
                )}
              </Text>
            </View>
          ) : isUsersError ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>
                {t(
                  "messages.createChat.errorLoadingUsers",
                  "Erro ao carregar usuários"
                )}
              </Text>
            </View>
          ) : (
            <FlatList
              data={selectableUsers}
              renderItem={renderUserItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              style={styles.usersList}
            />
          )}
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={
              createChatMutation.isPending
                ? t("messages.createChat.creating", "Criando...")
                : t("messages.createChat.create", "Criar Chat")
            }
            onPress={handleCreateChat}
            disabled={
              selectedUserIds.size === 0 || createChatMutation.isPending
            }
          />
          <InvisibleFullSizeButton
            text={t("common.cancel", "Cancelar")}
            onPress={handleClose}
          />
        </View>
      </View>
    </Modal>
  );
};

export default UserSelectionModal;
