/**
 * Hooks para gerenciamento de termos e condições
 * Seguindo os padrões estabelecidos no projeto com TanStack Query
 */

import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {useCallback} from "react";
import Toast from "react-native-toast-message";
import TermsService from "../../services/api/terms/terms.service";
import {
  Term,
  TermAcceptance,
  TermsListParams,
  TermType,
  TermStatus
} from "../../models/api/terms.models";
import {useLoading} from "../../contexts/loading-context";

// Query Keys para cache
export const TERMS_QUERY_KEYS = {
  all: ["terms"] as const,
  lists: () => [...TERMS_QUERY_KEYS.all, "list"] as const,
  list: (params: TermsListParams) =>
    [...TERMS_QUERY_KEYS.lists(), params] as const,
  details: () => [...TERMS_QUERY_KEYS.all, "detail"] as const,
  detail: (id: number) => [...TERMS_QUERY_KEYS.details(), id] as const,
  activePayment: () => [...TERMS_QUERY_KEYS.all, "active", "payment"] as const,
  activeGeneral: () => [...TERMS_QUERY_KEYS.all, "active", "general"] as const,
  activeTermsOfUse: () =>
    [...TERMS_QUERY_KEYS.all, "active", "terms-of-use"] as const
};

/**
 * Hook para buscar lista de termos
 */
export const useTerms = (params?: TermsListParams) => {
  return useQuery({
    queryKey: TERMS_QUERY_KEYS.list(params || {}),
    queryFn: () => TermsService.getTerms(params),
    select: (data) => data.data,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 3
  });
};

/**
 * Hook para buscar detalhes de um termo específico
 */
export const useTerm = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: TERMS_QUERY_KEYS.detail(id),
    queryFn: () => TermsService.getTermById(id),
    select: (data) => data.data,
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: 3
  });
};

/**
 * Hook para buscar termos de pagamento ativos
 */
export const useActivePaymentTerms = (enabled: boolean = true) => {
  return useQuery({
    queryKey: TERMS_QUERY_KEYS.activePayment(),
    queryFn: () => TermsService.getActivePaymentTerms(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: 3
  });
};

/**
 * Hook para buscar termos gerais ativos
 */
export const useActiveGeneralTerms = (enabled: boolean = true) => {
  return useQuery({
    queryKey: TERMS_QUERY_KEYS.activeGeneral(),
    queryFn: () => TermsService.getActiveGeneralTerms(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: 3
  });
};

/**
 * Hook para buscar termos de uso ativos
 */
export const useActiveTermsOfUse = (enabled: boolean = true) => {
  return useQuery({
    queryKey: TERMS_QUERY_KEYS.activeTermsOfUse(),
    queryFn: () => TermsService.getActiveTermsOfUse(),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: 3
  });
};

/**
 * Hook para aceitar uma versão específica de um termo com tratamento gracioso para 409
 * Usado em fluxos de pagamento onde o termo já pode ter sido aceito
 */
export const useAcceptTermGraceful = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: ({termId, version}: {termId: number; version: number}) => {
      console.log("🚀 [USE-TERMS] Aceitando termo via API (graceful)", {
        termId,
        version
      });
      return TermsService.acceptTermVersionGraceful(termId, version);
    },
    onMutate: () => {
      console.log("⏳ [USE-TERMS] Iniciando loading (graceful)");
      setCurrentLoading?.(true);
    },
    onSuccess: (data, variables) => {
      console.log("✅ [USE-TERMS] Termo aceito com sucesso (graceful):", data);

      // Invalida queries relacionadas
      queryClient.invalidateQueries({queryKey: TERMS_QUERY_KEYS.all});

      // Só mostrar toast de sucesso se não foi já aceito
      if (!data.alreadyAccepted) {
        Toast.show({
          type: "success",
          text1: "Sucesso!",
          text2: "Termos aceitos com sucesso!",
          position: "top",
          topOffset: 60
        });
      }

      return data.data;
    },
    onError: (error: any) => {
      console.error("❌ [USE-TERMS] Erro ao aceitar termo (graceful):", error);

      // Para este hook, não mostrar toast de erro para 409 pois já foi tratado no service
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao aceitar termos",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      console.log("🏁 [USE-TERMS] Finalizando loading (graceful)");
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para aceitar uma versão específica de um termo
 */
export const useAcceptTerm = (options?: {
  suppressGlobalErrorFor409?: boolean;
}) => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();
  const suppressGlobalErrorFor409 = options?.suppressGlobalErrorFor409 || false;

  return useMutation({
    mutationFn: ({termId, version}: {termId: number; version: number}) => {
      console.log("🚀 [USE-TERMS] Aceitando termo via API", {termId, version});
      return TermsService.acceptTermVersion(termId, version);
    },
    onMutate: () => {
      console.log("⏳ [USE-TERMS] Iniciando loading");
      setCurrentLoading?.(true);
    },
    onSuccess: (data, variables) => {
      console.log("✅ [USE-TERMS] Termo aceito com sucesso:", data);

      // Invalida queries relacionadas
      queryClient.invalidateQueries({queryKey: TERMS_QUERY_KEYS.all});

      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Termos aceitos com sucesso!",
        position: "top",
        topOffset: 60
      });

      return data.data;
    },
    onError: (error: any) => {
      console.error("❌ [USE-TERMS] Erro ao aceitar termo:", error);

      // Verificar se é erro 409 (termo já aceito) - não mostrar toast de erro
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as any;
        if (apiError.response?.status === 409) {
          console.log(
            "ℹ️ [USE-TERMS] Termo já aceito (409), não mostrando toast de erro"
          );
          if (suppressGlobalErrorFor409) {
            console.log("ℹ️ [USE-TERMS] Suprimindo erro global para 409");
            return; // Não mostrar toast de erro para termos já aceitos
          }
        }
      }

      // Para outros erros, ou se não estiver suprimindo 409, mostrar toast
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao aceitar termos",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      console.log("🏁 [USE-TERMS] Finalizando loading");
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para invalidar cache de termos
 */
export const useInvalidateTerms = () => {
  const queryClient = useQueryClient();

  return useCallback(() => {
    console.log("🔄 [USE-TERMS] Invalidando cache de termos");
    queryClient.invalidateQueries({queryKey: TERMS_QUERY_KEYS.all});
  }, [queryClient]);
};

/**
 * Hook combinado para gerenciar termos de pagamento
 * Busca os termos ativos e fornece função para aceitar
 */
export const usePaymentTermsManager = () => {
  const {
    data: activeTerms,
    isLoading: isLoadingTerms,
    error: termsError
  } = useActivePaymentTerms();

  const acceptTermMutation = useAcceptTerm();

  const acceptTerms = useCallback(async () => {
    if (!activeTerms) {
      throw new Error("Nenhum termo ativo encontrado");
    }

    try {
      const result = await acceptTermMutation.mutateAsync({
        termId: activeTerms.id,
        version: activeTerms.version
      });
      return result;
    } catch (error) {
      console.error(
        "❌ [USE-TERMS] Erro ao aceitar termos de pagamento:",
        error
      );

      // Verificar se é erro 409 (termo já aceito)
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as any;
        if (apiError.response?.status === 409) {
          const errorMessage = apiError.response?.data?.message || "";
          console.log(
            "ℹ️ [USE-TERMS] Termo já foi aceito anteriormente (409):",
            errorMessage
          );
          // Retorna sucesso pois o termo já foi aceito
          return {success: true, alreadyAccepted: true};
        }
      }

      throw error;
    }
  }, [activeTerms, acceptTermMutation]);

  return {
    activeTerms,
    isLoadingTerms,
    termsError,
    acceptTerms,
    isAcceptingTerms: acceptTermMutation.isPending,
    acceptTermsError: acceptTermMutation.error
  };
};
