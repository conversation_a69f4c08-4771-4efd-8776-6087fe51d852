import React from "react";
import {Modal, View, Text, TouchableWithoutFeedback} from "react-native";
import {useRouter} from "expo-router";
import {useTranslation} from "react-i18next";
import SmallLogo from "@/components/logos/small-logo";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import FullSizeButton from "@/components/full-size-button";
import InvisibleFullSizeButton from "@/components/invisible-full-size-button";
import {useGuestUser} from "@/contexts/guest-user-context";
import styles from "@/styles/modals/upsell-drawer.style";

export interface UpsellDrawerProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
}

const UpsellDrawer: React.FC<UpsellDrawerProps> = ({
  visible,
  onClose,
  title,
  description
}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    onClose();
    // Clear guest user data (logout from upsell) and navigate to login
    await clearGuestUser();
    router.push("/(auth)/login");
  };

  const handleLoginPress = async () => {
    onClose();
    // Clear guest user data (logout from upsell) and navigate to login
    await clearGuestUser();
    router.push("/(auth)/login");
  };

  const defaultTitle = t("upsellDrawer.title", "Quase lá!");
  const defaultDescription = t(
    "upsellDrawer.description",
    "Faça seu cadastro e torne-se membro para adquirir esse produto e outros diversos benefícios no aplicativo."
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.container}>
              <BackgroundLogoTexture gray={false} />

              <View style={styles.content}>
                <View style={styles.logoContainer}>
                  <SmallLogo width={63.3} height={86.4} />
                </View>

                <View style={styles.textContainer}>
                  <Text style={styles.title}>{title || defaultTitle}</Text>
                  <Text style={styles.description}>
                    {description || defaultDescription}
                  </Text>
                </View>

                <View style={styles.buttonsContainer}>
                  <FullSizeButton
                    text={t(
                      "upsellDrawer.registerButton",
                      "Cadastre-se agora!"
                    )}
                    onPress={handleRegisterPress}
                  />

                  <InvisibleFullSizeButton
                    text={t("upsellDrawer.loginButton", "Ir para login")}
                    onPress={handleLoginPress}
                  />
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default UpsellDrawer;
