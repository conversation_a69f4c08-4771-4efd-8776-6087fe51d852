1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="studio.takasaki.clubm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:3-75
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:3-68
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:20-66
17    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:3-69
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:20-67
18    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:3-71
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:20-69
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:3-63
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:20-61
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Permissions used by Manage Permissions screen -->
20-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:3-78
20-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:20-76
21    <uses-permission android:name="android.permission.CAMERA" />
21-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:3-63
21-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:20-60
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:3-77
22-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:20-74
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:3-79
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:20-76
24
25    <queries>
25-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:3-20:13
26        <intent>
26-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:15:5-19:14
27            <action android:name="android.intent.action.VIEW" />
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
29-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
30
31            <data android:scheme="https" />
31-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
31-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
32        </intent>
33        <intent>
33-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
34            <action android:name="org.chromium.intent.action.PAY" />
34-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
34-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
35        </intent>
36        <intent>
36-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
37            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
37-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
37-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
38        </intent>
39        <intent>
39-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
40            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
40-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
40-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
41        </intent>
42
43        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
43-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
43-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
44        <intent>
44-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:10:9-17:18
45            <action android:name="android.intent.action.OPEN_DOCUMENT" />
45-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:13-74
45-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:21-71
46
47            <category android:name="android.intent.category.DEFAULT" />
47-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
47-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
48            <category android:name="android.intent.category.OPENABLE" />
48-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
48-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
49
50            <data android:mimeType="*/*" />
50-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
51        </intent> <!-- Query open documents -->
52        <intent>
52-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
53            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
53-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
53-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
54        </intent>
55        <intent>
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
56
57            <!-- Required for picking images from the camera roll if targeting API 30 -->
58            <action android:name="android.media.action.IMAGE_CAPTURE" />
58-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
58-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
59        </intent>
60        <intent>
60-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
61
62            <!-- Required for picking images from the camera if targeting API 30 -->
63            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
63-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
63-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
64        </intent>
65        <intent>
65-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
66
67            <!-- Required for file sharing if targeting API 30 -->
68            <action android:name="android.intent.action.SEND" />
68-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
68-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
69
70            <data android:mimeType="*/*" />
70-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
71        </intent>
72        <intent>
72-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
73
74            <!-- Required for opening tabs if targeting API 30 -->
75            <action android:name="android.support.customtabs.action.CustomTabsService" />
75-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
75-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
76        </intent>
77        <intent>
77-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
78            <action android:name="android.intent.action.GET_CONTENT" />
78-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
78-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
79
80            <category android:name="android.intent.category.OPENABLE" />
80-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
80-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
81
82            <data android:mimeType="*/*" />
82-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
83        </intent>
84    </queries>
85    <!--
86         Required to keep CPU alive while downloading files
87        (NOT to keep screen awake)
88    -->
89    <uses-permission android:name="android.permission.WAKE_LOCK" />
89-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-68
89-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-65
90    <!--
91         Required to poll the state of the network connection
92        and respond to changes
93    -->
94    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required to check whether Wi-Fi is enabled -->
94-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-79
94-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-76
95    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Required to to download files without a notification -->
95-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-76
95-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-73
96    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
96-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:5-88
96-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:22-85
97    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
97-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
97-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
98    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
98-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
98-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
99    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
99-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
99-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
100
101    <permission
101-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
102        android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
102-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
103        android:protectionLevel="signature" />
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
104
105    <uses-permission android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
105-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
105-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
106    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
106-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
106-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
107    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
108    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
109    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
110    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
111    <!-- for Samsung -->
112    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
113    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
114    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
115    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
116    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
117    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
118    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
119    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
120    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
121    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
122    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
123    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
124    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
125    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
126    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
126-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
126-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
127    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
127-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
127-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
128
129    <application
129-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:3-63:17
130        android:name="studio.takasaki.clubm.MainApplication"
130-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:16-47
131        android:allowBackup="true"
131-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:162-188
132        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
132-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
133        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
133-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:307-376
134        android:debuggable="true"
135        android:extractNativeLibs="false"
136        android:fullBackupContent="@xml/secure_store_backup_rules"
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:248-306
137        android:icon="@mipmap/ic_launcher"
137-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:81-115
138        android:label="@string/app_name"
138-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:48-80
139        android:roundIcon="@mipmap/ic_launcher_round"
139-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:116-161
140        android:supportsRtl="true"
140-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:221-247
141        android:theme="@style/AppTheme"
141-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:189-220
142        android:usesCleartextTraffic="true" >
142-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\debug\AndroidManifest.xml:6:18-53
143        <meta-data
143-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:5-83
144            android:name="expo.modules.updates.ENABLED"
144-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:16-59
145            android:value="false" />
145-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:60-81
146        <meta-data
146-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:5-105
147            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
147-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:16-80
148            android:value="ALWAYS" />
148-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:81-103
149        <meta-data
149-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:24:5-99
150            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
150-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:24:16-79
151            android:value="0" />
151-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:24:80-97
152
153        <activity
153-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:5-62:16
154            android:name="studio.takasaki.clubm.MainActivity"
154-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:15-43
155            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
155-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:44-157
156            android:exported="true"
156-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:279-302
157            android:launchMode="singleTask"
157-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:158-189
158            android:screenOrientation="portrait"
158-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:303-339
159            android:theme="@style/Theme.App.SplashScreen"
159-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:233-278
160            android:windowSoftInputMode="adjustResize" >
160-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:190-232
161            <intent-filter>
161-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:26:7-29:23
162                <action android:name="android.intent.action.MAIN" />
162-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-60
162-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:17-58
163
164                <category android:name="android.intent.category.LAUNCHER" />
164-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-68
164-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-66
165            </intent-filter>
166            <intent-filter>
166-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:30:7-36:23
167                <action android:name="android.intent.action.VIEW" />
167-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
167-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
168
169                <category android:name="android.intent.category.DEFAULT" />
169-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
169-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
170                <category android:name="android.intent.category.BROWSABLE" />
170-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
170-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
171
172                <data android:scheme="clubm" />
172-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
172-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
173                <data android:scheme="exp+club-m-app" />
173-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
173-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
174            </intent-filter>
175            <!-- Universal Links -->
176            <intent-filter android:autoVerify="true" >
176-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:38:7-43:23
176-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:38:22-47
177                <action android:name="android.intent.action.VIEW" />
177-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
177-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
178
179                <category android:name="android.intent.category.DEFAULT" />
179-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
179-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
180                <category android:name="android.intent.category.BROWSABLE" />
180-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
180-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
181
182                <data
182-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
183                    android:host="clubm.app"
184                    android:scheme="https" />
184-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
185            </intent-filter>
186            <intent-filter android:autoVerify="true" >
186-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:44:7-49:23
186-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:44:22-47
187                <action android:name="android.intent.action.VIEW" />
187-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
187-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
188
189                <category android:name="android.intent.category.DEFAULT" />
189-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
189-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
190                <category android:name="android.intent.category.BROWSABLE" />
190-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
190-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
191
192                <data
192-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
193                    android:host="app.clubm.com"
194                    android:scheme="https" />
194-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
195            </intent-filter>
196            <intent-filter android:autoVerify="true" >
196-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:50:7-55:23
196-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:50:22-47
197                <action android:name="android.intent.action.VIEW" />
197-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
197-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
198
199                <category android:name="android.intent.category.DEFAULT" />
199-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
199-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
200                <category android:name="android.intent.category.BROWSABLE" />
200-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
200-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
201
202                <data
202-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
203                    android:host="www.clubm.app"
204                    android:scheme="https" />
204-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
205            </intent-filter>
206            <intent-filter android:autoVerify="true" >
206-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:56:7-61:23
206-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:56:22-47
207                <action android:name="android.intent.action.VIEW" />
207-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
207-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
208
209                <category android:name="android.intent.category.DEFAULT" />
209-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
209-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
210                <category android:name="android.intent.category.BROWSABLE" />
210-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
210-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
211
212                <data
212-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
213                    android:host="www.app.clubm.com"
214                    android:scheme="https" />
214-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
215            </intent-filter>
216        </activity>
217
218        <provider
218-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
219            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
219-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
220            android:authorities="studio.takasaki.clubm.fileprovider"
220-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
221            android:exported="false"
221-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
222            android:grantUriPermissions="true" >
222-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
223            <meta-data
223-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
224                android:name="android.support.FILE_PROVIDER_PATHS"
224-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
225                android:resource="@xml/file_provider_paths" />
225-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
226        </provider>
227        <provider
227-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-43:20
228            android:name="com.ReactNativeBlobUtil.Utils.FileProvider"
228-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-70
229            android:authorities="studio.takasaki.clubm.provider"
229-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-60
230            android:exported="false"
230-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
231            android:grantUriPermissions="true" >
231-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-47
232            <meta-data
232-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
233                android:name="android.support.FILE_PROVIDER_PATHS"
233-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
234                android:resource="@xml/provider_paths" />
234-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
235        </provider>
236
237        <activity
237-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
238            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
238-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
239            android:exported="true"
239-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
240            android:launchMode="singleTask"
240-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
241            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
241-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
242            <intent-filter>
242-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
243                <action android:name="android.intent.action.VIEW" />
243-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
243-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
244
245                <category android:name="android.intent.category.DEFAULT" />
245-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
245-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
246                <category android:name="android.intent.category.BROWSABLE" />
246-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
246-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
247
248                <data android:scheme="expo-dev-launcher" />
248-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
248-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
249            </intent-filter>
250        </activity>
251        <activity
251-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
252            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
252-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
253            android:screenOrientation="portrait"
253-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
254            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
254-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
255        <activity
255-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
256            android:name="expo.modules.devmenu.DevMenuActivity"
256-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
257            android:exported="true"
257-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
258            android:launchMode="singleTask"
258-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
259            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
259-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
260            <intent-filter>
260-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
261                <action android:name="android.intent.action.VIEW" />
261-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:7-58
261-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:15-56
262
263                <category android:name="android.intent.category.DEFAULT" />
263-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:9-67
263-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:32:19-65
264                <category android:name="android.intent.category.BROWSABLE" />
264-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:7-67
264-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:17-65
265
266                <data android:scheme="expo-dev-menu" />
266-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:7-37
266-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:13-35
267            </intent-filter>
268        </activity>
269
270        <meta-data
270-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
271            android:name="org.unimodules.core.AppLoader#react-native-headless"
271-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
272            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
272-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
273        <meta-data
273-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
274            android:name="com.facebook.soloader.enabled"
274-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
275            android:value="true" />
275-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
276
277        <activity
277-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
278            android:name="com.facebook.react.devsupport.DevSettingsActivity"
278-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
279            android:exported="false" />
279-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
280
281        <provider
281-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:8:9-15:20
282            android:name="expo.modules.clipboard.ClipboardFileProvider"
282-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:9:13-72
283            android:authorities="studio.takasaki.clubm.ClipboardFileProvider"
283-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:10:13-73
284            android:exported="true" >
284-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:11:13-36
285            <meta-data
285-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:12:13-14:68
286                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
286-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:13:17-84
287                android:resource="@xml/clipboard_provider_paths" />
287-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:14:17-65
288        </provider>
289        <provider
289-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
290            android:name="expo.modules.filesystem.FileSystemFileProvider"
290-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
291            android:authorities="studio.takasaki.clubm.FileSystemFileProvider"
291-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
292            android:exported="false"
292-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
293            android:grantUriPermissions="true" >
293-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
294            <meta-data
294-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
295                android:name="android.support.FILE_PROVIDER_PATHS"
295-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
296                android:resource="@xml/file_system_provider_paths" />
296-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
297        </provider>
298
299        <service
299-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
300            android:name="com.google.android.gms.metadata.ModuleDependencies"
300-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
301            android:enabled="false"
301-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
302            android:exported="false" >
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
303            <intent-filter>
303-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
304                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
304-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
304-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
305            </intent-filter>
306
307            <meta-data
307-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
308                android:name="photopicker_activity:0:required"
308-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
309                android:value="" />
309-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
310        </service>
311
312        <activity
312-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
313            android:name="com.canhub.cropper.CropImageActivity"
313-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
314            android:exported="true"
314-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
315            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
315-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
316        <provider
316-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
317            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
317-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
318            android:authorities="studio.takasaki.clubm.ImagePickerFileProvider"
318-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
319            android:exported="false"
319-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
320            android:grantUriPermissions="true" >
320-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
321            <meta-data
321-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
322                android:name="android.support.FILE_PROVIDER_PATHS"
322-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
323                android:resource="@xml/image_picker_provider_paths" />
323-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
324        </provider>
325
326        <service
326-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
327            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
327-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
328            android:exported="false" >
328-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
329            <intent-filter android:priority="-1" >
329-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
329-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
330                <action android:name="com.google.firebase.MESSAGING_EVENT" />
330-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
330-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
331            </intent-filter>
332        </service>
333
334        <receiver
334-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
335            android:name="expo.modules.notifications.service.NotificationsService"
335-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
336            android:enabled="true"
336-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
337            android:exported="false" >
337-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
338            <intent-filter android:priority="-1" >
338-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
338-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
339                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
339-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
339-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
340                <action android:name="android.intent.action.BOOT_COMPLETED" />
340-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
340-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
341                <action android:name="android.intent.action.REBOOT" />
341-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
341-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
342                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
342-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
342-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
343                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
343-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
343-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
344                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
344-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
344-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
345            </intent-filter>
346        </receiver>
347
348        <activity
348-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
349            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
349-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
350            android:excludeFromRecents="true"
350-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
351            android:exported="false"
351-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
352            android:launchMode="standard"
352-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
353            android:noHistory="true"
353-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
354            android:taskAffinity=""
354-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
355            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
355-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
356
357        <provider
357-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
358            android:name="expo.modules.sharing.SharingFileProvider"
358-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
359            android:authorities="studio.takasaki.clubm.SharingFileProvider"
359-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
360            android:exported="false"
360-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
361            android:grantUriPermissions="true" >
361-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
362            <meta-data
362-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
363                android:name="android.support.FILE_PROVIDER_PATHS"
363-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
364                android:resource="@xml/sharing_provider_paths" />
364-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
365        </provider>
366
367        <receiver
367-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
368            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
368-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
369            android:exported="true"
369-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
370            android:permission="com.google.android.c2dm.permission.SEND" >
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
371            <intent-filter>
371-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
372                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
372-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
372-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
373            </intent-filter>
374
375            <meta-data
375-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
376                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
376-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
377                android:value="true" />
377-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
378        </receiver>
379        <!--
380             FirebaseMessagingService performs security checks at runtime,
381             but set to not exported to explicitly avoid allowing another app to call it.
382        -->
383        <service
383-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
384            android:name="com.google.firebase.messaging.FirebaseMessagingService"
384-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
385            android:directBootAware="true"
385-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
386            android:exported="false" >
386-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
387            <intent-filter android:priority="-500" >
387-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
387-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
388                <action android:name="com.google.firebase.MESSAGING_EVENT" />
388-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
388-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
389            </intent-filter>
390        </service>
391        <service
391-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
392            android:name="com.google.firebase.components.ComponentDiscoveryService"
392-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
393            android:directBootAware="true"
393-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
394            android:exported="false" >
394-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
395            <meta-data
395-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
396                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
396-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
397                android:value="com.google.firebase.components.ComponentRegistrar" />
397-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
398            <meta-data
398-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
399                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
399-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
400                android:value="com.google.firebase.components.ComponentRegistrar" />
400-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
401            <meta-data
401-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
402                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
402-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
403                android:value="com.google.firebase.components.ComponentRegistrar" />
403-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
404            <meta-data
404-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
405                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
405-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
406                android:value="com.google.firebase.components.ComponentRegistrar" />
406-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
407            <meta-data
407-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
408                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
408-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
409                android:value="com.google.firebase.components.ComponentRegistrar" />
409-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
410            <meta-data
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
411                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
411-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
412                android:value="com.google.firebase.components.ComponentRegistrar" />
412-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
413            <meta-data
413-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
414                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
414-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
415                android:value="com.google.firebase.components.ComponentRegistrar" />
415-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
416        </service>
417
418        <provider
418-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
419            android:name="com.canhub.cropper.CropFileProvider"
419-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
420            android:authorities="studio.takasaki.clubm.cropper.fileprovider"
420-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
421            android:exported="false"
421-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
422            android:grantUriPermissions="true" >
422-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
423            <meta-data
423-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
424                android:name="android.support.FILE_PROVIDER_PATHS"
424-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
425                android:resource="@xml/library_file_paths" />
425-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
426        </provider>
427
428        <meta-data
428-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
429            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
429-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
430            android:value="GlideModule" />
430-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
431
432        <activity
432-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
433            android:name="com.google.android.gms.common.api.GoogleApiActivity"
433-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
434            android:exported="false"
434-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
435            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
435-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
436
437        <provider
437-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
438            android:name="com.google.firebase.provider.FirebaseInitProvider"
438-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
439            android:authorities="studio.takasaki.clubm.firebaseinitprovider"
439-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
440            android:directBootAware="true"
440-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
441            android:exported="false"
441-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
442            android:initOrder="100" />
442-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
443        <provider
443-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
444            android:name="androidx.startup.InitializationProvider"
444-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
445            android:authorities="studio.takasaki.clubm.androidx-startup"
445-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
446            android:exported="false" >
446-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
447            <meta-data
447-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
448                android:name="androidx.emoji2.text.EmojiCompatInitializer"
448-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
449                android:value="androidx.startup" />
449-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
450            <meta-data
450-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
451                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
451-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
452                android:value="androidx.startup" />
452-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
453            <meta-data
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
454                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
455                android:value="androidx.startup" />
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
456        </provider>
457
458        <meta-data
458-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
459            android:name="com.google.android.gms.version"
459-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
460            android:value="@integer/google_play_services_version" />
460-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
461
462        <receiver
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
463            android:name="androidx.profileinstaller.ProfileInstallReceiver"
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
464            android:directBootAware="false"
464-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
465            android:enabled="true"
465-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
466            android:exported="true"
466-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
467            android:permission="android.permission.DUMP" >
467-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
468            <intent-filter>
468-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
469                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
470            </intent-filter>
471            <intent-filter>
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
472                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
473            </intent-filter>
474            <intent-filter>
474-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
475                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
475-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
475-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
476            </intent-filter>
477            <intent-filter>
477-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
478                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
478-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
478-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
479            </intent-filter>
480        </receiver>
481
482        <service
482-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
483            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
483-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
484            android:exported="false" >
484-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
485            <meta-data
485-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
486                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
486-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
487                android:value="cct" />
487-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
488        </service>
489        <service
489-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
490            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
490-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
491            android:exported="false"
491-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
492            android:permission="android.permission.BIND_JOB_SERVICE" >
492-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
493        </service>
494
495        <receiver
495-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
496            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
496-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
497            android:exported="false" />
497-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
498    </application>
499
500</manifest>
