import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface DownloadIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const DownloadIcon: React.FC<DownloadIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#DFE9F0",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        stroke={color}
        d="M17.5 12.5V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V12.5"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M5.83333 8.33333L10 12.5L14.1667 8.33333"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M10 12.5V2.5"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default DownloadIcon;
