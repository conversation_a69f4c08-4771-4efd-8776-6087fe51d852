/**
 * Hooks para gerenciamento de áreas de interesse
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import AreaInterestsService, {
  AreaInterest,
  CreateAreaInterestRequest,
  AreaInterestsListParams
} from "@/services/api/area-interests/area-interests.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";
import {PaginationResponse} from "@/models/api/common.models";

// Query keys para cache
export const areaInterestsKeys = {
  all: ["areaInterests"] as const,
  lists: () => [...areaInterestsKeys.all, "list"] as const,
  list: (params?: AreaInterestsListParams) => [...areaInterestsKeys.lists(), params] as const
};

/**
 * Hook para buscar lista de áreas de interesse
 */
export const useAreaInterests = (
  params?: AreaInterestsListParams,
  options?: UseQueryOptions<PaginationResponse<AreaInterest>, BaseApiError>
) => {
  return useQuery({
    queryKey: areaInterestsKeys.list(params),
    queryFn: () => AreaInterestsService.getAreaInterests(params),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para criar nova área de interesse
 */
export const useCreateAreaInterest = (
  options?: UseMutationOptions<AreaInterest, BaseApiError, CreateAreaInterestRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => AreaInterestsService.createAreaInterest(data),
    onSuccess: (data) => {
      // Invalidar cache das listas de áreas de interesse
      queryClient.invalidateQueries({
        queryKey: areaInterestsKeys.lists()
      });

      ApiLogger.info("Área de interesse criada e cache atualizado", {
        id: data.id,
        name: data.name
      });
    },
    onError: (error) => {
      ApiLogger.error("Erro ao criar área de interesse", error);
    },
    ...options
  });
};
