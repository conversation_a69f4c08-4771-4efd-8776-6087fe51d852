import React, {useCallback} from "react";
import {Text, View, ScrollView} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import FullSizeButton from "@/components/full-size-button";
import {useRegistrationAnalysis} from "@/contexts/registration-analysis-context";

const RegistrationSuccess: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const {activate} = useRegistrationAnalysis();

  const formatCurrency = useCallback((value: string) => {
    const numValue = parseFloat(value) / 100;
    return `R$ ${numValue.toFixed(2).replace(".", ",")}`;
  }, []);

  const handleCheckAccountStatus = useCallback(async () => {
    try {
      await activate();
    } catch {}
    router.replace("/(registration)/registration-status");
  }, [activate, router]);

  const handleGoToHome = useCallback(() => {
    // Navegar para a home do app
    router.replace("/(tabs)/home");
  }, [router]);

  const handleGoToWallet = useCallback(() => {
    // Navegar para a carteira
    router.push("/(wallet)/wallet");
  }, [router]);

  return (
    <Screen>
      <ScrollView
        style={{flex: 1, backgroundColor: "#1A1A1A"}}
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 60,
          paddingBottom: 20
        }}
      >
        <View style={{marginBottom: 32}}>
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t("registrationSuccess.createAccount", "Criar conta")}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("registrationSuccess.stepProgress", "7 / 7 Concluído")}
          </Text>
          <View
            style={{height: 4, backgroundColor: "#333333", borderRadius: 2}}
          >
            <View
              style={{
                height: "100%",
                backgroundColor: "#00D4AA",
                borderRadius: 2,
                width: "100%"
              }}
            />
          </View>
        </View>

        <View style={{alignItems: "center", marginBottom: 32}}>
          <View
            style={{
              width: 100,
              height: 100,
              backgroundColor: "#00D4AA",
              borderRadius: 50,
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 24
            }}
          >
            <Text style={{fontSize: 48, color: "#FFFFFF"}}>🎉</Text>
          </View>

          <Text
            style={{
              fontSize: 28,
              fontWeight: "bold",
              color: "#FFFFFF",
              textAlign: "center",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("registrationSuccess.title", "Bem-vindo ao Club M!")}
          </Text>

          <Text
            style={{
              fontSize: 16,
              color: "#CCCCCC",
              textAlign: "center",
              lineHeight: 24,
              marginBottom: 32,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "registrationSuccess.description",
              "Sua conta foi criada com sucesso e os pagamentos foram processados. Agora você faz parte da nossa comunidade!"
            )}
          </Text>
        </View>

        <View
          style={{
            backgroundColor: "#2A2A2A",
            borderRadius: 12,
            padding: 16,
            marginBottom: 24
          }}
        >
          <Text
            style={{
              fontSize: 18,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("registrationSuccess.paymentSummary", "Resumo dos Pagamentos")}
          </Text>

          <View style={{marginBottom: 16}}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: "bold",
                color: "#00D4AA",
                marginBottom: 8,
                fontFamily: "Ubuntu"
              }}
            >
              {t("registrationSuccess.titlePayment", "Título de Adesão")}
            </Text>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 4
              }}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.paymentMethod", "Método:")}
              </Text>
              <Text
                style={{fontSize: 14, color: "#FFFFFF", fontFamily: "Ubuntu"}}
              >
                {params.titlePaymentMethod ||
                  t("registrationSuccess.notSpecified", "Não especificado")}
              </Text>
            </View>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 4
              }}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.installments", "Parcelamento:")}
              </Text>
              <Text
                style={{fontSize: 14, color: "#FFFFFF", fontFamily: "Ubuntu"}}
              >
                {params.selectedInstallments === "1"
                  ? t("registrationSuccess.singlePayment", "À vista")
                  : t("registrationSuccess.installmentsCount", "{{count}}x", {
                      count: params.selectedInstallments
                    })}
              </Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.value", "Valor:")}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: "#00D4AA",
                  fontFamily: "Ubuntu"
                }}
              >
                {params.totalValue
                  ? formatCurrency(params.totalValue as string)
                  : t("registrationSuccess.notSpecified", "Não especificado")}
              </Text>
            </View>
          </View>

          <View
            style={{
              borderTopWidth: 1,
              borderTopColor: "#333333",
              paddingTop: 16
            }}
          >
            <Text
              style={{
                fontSize: 16,
                fontWeight: "bold",
                color: "#00D4AA",
                marginBottom: 8,
                fontFamily: "Ubuntu"
              }}
            >
              {t("registrationSuccess.planPayment", "Plano de Assinatura")}
            </Text>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 4
              }}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.plan", "Plano:")}
              </Text>
              <Text
                style={{fontSize: 14, color: "#FFFFFF", fontFamily: "Ubuntu"}}
              >
                {params.selectedPlanName ||
                  t("registrationSuccess.notSpecified", "Não especificado")}
              </Text>
            </View>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 4
              }}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.paymentMethod", "Método:")}
              </Text>
              <Text
                style={{fontSize: 14, color: "#FFFFFF", fontFamily: "Ubuntu"}}
              >
                {params.planPaymentMethod ||
                  t("registrationSuccess.notSpecified", "Não especificado")}
              </Text>
            </View>
            <View
              style={{flexDirection: "row", justifyContent: "space-between"}}
            >
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {t("registrationSuccess.monthlyValue", "Valor mensal:")}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: "#00D4AA",
                  fontFamily: "Ubuntu"
                }}
              >
                {params.planValue
                  ? formatCurrency(params.planValue as string)
                  : t("registrationSuccess.notSpecified", "Não especificado")}
              </Text>
            </View>
          </View>
        </View>

        <View
          style={{
            backgroundColor: "#2A2A2A",
            borderRadius: 12,
            padding: 16,
            marginBottom: 32
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 12,
              fontFamily: "Ubuntu"
            }}
          >
            {t("registrationSuccess.nextSteps", "Próximos Passos")}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "registrationSuccess.step1",
              "• Explore os recursos disponíveis na plataforma"
            )}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "registrationSuccess.step2",
              "• Acesse sua carteira digital para gerenciar pagamentos"
            )}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "registrationSuccess.step3",
              "• Conecte-se com outros membros da comunidade"
            )}
          </Text>
          <Text style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}>
            {t(
              "registrationSuccess.step4",
              "• Participe de eventos e oportunidades exclusivas"
            )}
          </Text>
        </View>

        <View style={{gap: 12}}>
          <FullSizeButton
            text={t(
              "registrationSuccess.checkAccountStatus",
              "Verificar Status da Conta"
            )}
            onPress={handleCheckAccountStatus}
            variant="secondary"
          />

          <FullSizeButton
            text={t("registrationSuccess.goToWallet", "Ir para Carteira")}
            onPress={handleGoToWallet}
            variant="secondary"
          />

          <FullSizeButton
            text={t("registrationSuccess.goToHome", "Ir para Home")}
            onPress={handleGoToHome}
          />
        </View>
      </ScrollView>
    </Screen>
  );
};

export default RegistrationSuccess;
