import React from "react";
import Svg, {Defs, ClipPath, Path, G} from "react-native-svg";

interface MedalIconProps {
  width?: number;
  height?: number;
  color?: string;
  stroke?: string;
}

const MedalIcon: React.FC<MedalIconProps> = ({
  width = 24,
  height = 24,
  color = "rgb(52, 64, 83)",
  stroke
}) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Defs>
        <ClipPath id="clipPath0197783901">
          <Path
            d="M0 0L24 0L24 24L0 24L0 0Z"
            fillRule="nonzero"
            transform="matrix(1 0 0 1 0 0)"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath0197783901)">
        <Path
          d="M12.85 1.86678Q12.933 2.06748 13.1335 2.15077L14.4416 2.69261Q14.9055 2.88478 15.2606 3.23986Q15.6157 3.59494 15.8078 4.05887Q16 4.5228 16 5.02495Q16 5.5271 15.8078 5.99103L15.2661 7.29902Q15.1828 7.49996 15.2659 7.69943L15.8076 9.00914Q15.9997 9.47244 15.9997 9.9748Q15.9998 10.4772 15.8076 10.9413Q15.6153 11.4054 15.2601 11.7606Q14.9048 12.1158 14.4406 12.3079L13.133 12.8495Q12.9317 12.9327 12.8484 13.1333L12.6585 13.5917L13.4913 19.8685Q13.5136 20.0367 13.4792 20.2028Q13.4448 20.369 13.3575 20.5145Q13.2552 20.6851 13.0953 20.8035Q12.9355 20.9219 12.7425 20.9701Q12.5496 21.0184 12.3528 20.9891Q12.1561 20.9598 11.9855 20.8575L7.5 18.1662L3.0145 20.8575Q2.86896 20.9448 2.70277 20.9792Q2.53658 21.0136 2.36834 20.9913Q2.2707 20.9783 2.17747 20.9466Q2.08425 20.9148 1.99901 20.8655Q1.91377 20.8161 1.83979 20.7511Q1.76582 20.6861 1.70595 20.6079Q1.64608 20.5296 1.60262 20.4413Q1.55916 20.3529 1.53378 20.2577Q1.5084 20.1625 1.50207 20.0643Q1.49574 19.966 1.50871 19.8683L2.34201 13.5945L2.1509 13.1331Q2.06768 12.9318 1.86713 12.8486L0.559052 12.3067Q0.0953301 12.1146 -0.259644 11.7598Q-0.614614 11.4049 -0.806845 10.9412Q-0.999074 10.4776 -0.999325 9.97564Q-0.999577 9.47371 -0.807811 9.00985L-0.265787 7.70124Q-0.183186 7.50132 -0.266424 7.30071L-0.808043 5.98932Q-0.99992 5.5265 -1 5.02413Q-1.00008 4.52177 -0.807842 4.05765Q-0.615603 3.59353 -0.260331 3.23836Q0.0949438 2.88318 0.559124 2.69108L1.86678 2.14941Q2.06785 2.06627 2.15119 1.86594L2.69275 0.558436Q2.88491 0.094505 3.23998 -0.260575Q3.59505 -0.615658 4.05897 -0.807828Q4.5229 -1 5.02505 -1Q5.5272 -1 5.99113 -0.807828L7.29908 -0.266038Q7.49896 -0.18345 7.69952 -0.266676L9.01094 -0.807569Q9.47354 -0.99911 9.97559 -0.999072Q10.4776 -0.999034 10.9415 -0.8069Q11.4053 -0.614768 11.7603 -0.25978Q12.1154 0.0952033 12.3076 0.559012L12.8346 1.83147Q12.8426 1.84898 12.85 1.86678ZM4.286 13.0615L3.99867 12.3678Q3.80711 11.9045 3.45243 11.5492Q3.09774 11.194 2.63414 11.0015L1.32444 10.459Q1.12383 10.3759 1.04067 10.1753Q0.957497 9.97466 1.04047 9.77396L1.58198 8.46658Q1.774 8.00183 1.77363 7.49985Q1.77325 6.99787 1.58087 6.53422L1.0405 5.22585Q1.00002 5.1282 1 5.02381Q0.999983 4.91943 1.03993 4.823Q1.07987 4.72656 1.15368 4.65277Q1.22749 4.57898 1.32392 4.53907L2.63217 3.99716Q3.09508 3.80575 3.4501 3.45148Q3.80512 3.09722 3.99777 2.63414L4.54052 1.32378Q4.62366 1.12306 4.82436 1.03992Q5.02505 0.95679 5.22574 1.03992L6.53369 1.58171Q6.99844 1.77375 7.50043 1.77337Q8.00241 1.77299 8.46606 1.5806L9.77352 1.04134Q9.97544 0.957736 10.1761 1.04085Q10.3767 1.12397 10.4599 1.32463L11.0022 2.63382Q11.0115 2.65645 11.022 2.67859Q11.2119 3.11321 11.5482 3.4501Q11.9029 3.80533 12.3665 3.99784L13.6762 4.54036Q13.8769 4.6235 13.9601 4.82421Q14.0432 5.02495 13.9601 5.22568L13.4183 6.53368Q13.226 6.99799 13.2261 7.50028Q13.2262 8.00367 13.4195 8.46805L13.9594 9.77355Q13.9997 9.87074 13.9997 9.97512Q13.9998 10.0795 13.9598 10.1759Q13.9199 10.2724 13.8461 10.3462Q13.7722 10.42 13.6758 10.4599L12.3676 11.0018Q11.9043 11.1934 11.549 11.548Q11.1938 11.9027 11.0013 12.3663L10.7146 13.0586C10.7098 13.0696 10.7052 13.0808 10.7008 13.092L10.4588 13.6761Q10.3757 13.8768 10.175 13.96Q9.97429 14.0431 9.77361 13.96L8.46566 13.4182Q8.0009 13.2261 7.49892 13.2265Q6.99694 13.2269 6.53329 13.4193L5.22544 13.9602Q5.02499 14.0431 4.82447 13.9601Q4.62395 13.877 4.54076 13.6765L4.29981 13.0948C4.2954 13.0836 4.2908 13.0725 4.286 13.0615ZM4.06525 15.8104L3.76444 18.0751L6.57391 16.3895Q6.93863 16.1706 7.17826 16.118Q7.5 16.0473 7.82174 16.118Q8.06137 16.1706 8.42609 16.3895L11.2359 18.0753L10.9353 15.8098Q10.4737 15.9999 9.9743 15.9999Q9.47214 15.9999 9.00821 15.8077L7.70027 15.2659Q7.50039 15.1833 7.29983 15.2665L5.9899 15.8084Q5.52612 16.0002 5.02437 16Q4.52597 15.9998 4.06525 15.8104Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 4.5 2)"
          fill={stroke || color}
        />
      </G>
    </Svg>
  );
};

export default MedalIcon;
