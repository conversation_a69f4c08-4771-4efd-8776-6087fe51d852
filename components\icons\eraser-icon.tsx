import React from "react";
import Svg, {Path} from "react-native-svg";

interface EraserIconProps {
  width?: number;
  height?: number;
  color?: string;
}

const EraserIcon: React.FC<EraserIconProps> = ({
  width = 20,
  height = 20,
  color = "#FBFCFD"
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <Path
        d="M8.33333 15H15M3.33333 15L10.8333 7.5C11.2936 7.03976 11.9064 6.78125 12.5 6.78125C13.0936 6.78125 13.7064 7.03976 14.1667 7.5L16.6667 10C17.1269 10.4602 17.3854 11.073 17.3854 11.6667C17.3854 12.2603 17.1269 12.8731 16.6667 13.3333L9.16667 20.8333C8.70643 21.2936 8.09357 21.5521 7.5 21.5521C6.90643 21.5521 6.29357 21.2936 5.83333 20.8333L3.33333 18.3333C2.87309 17.8731 2.61458 17.2603 2.61458 16.6667C2.61458 16.073 2.87309 15.4602 3.33333 15Z"
        stroke={color}
        strokeWidth="1.67"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default EraserIcon;
