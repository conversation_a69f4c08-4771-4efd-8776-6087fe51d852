/**
 * Testes para o sistema de filtragem da tela de mensagens
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Messages from '../../app/(tabs)/messages';

// Mock dos hooks necessários
jest.mock('@/hooks/api/use-chats', () => ({
  useChats: jest.fn()
}));

jest.mock('@/contexts/guest-user-context', () => ({
  useGuestUser: () => ({ isGuest: false })
}));

jest.mock('@/hooks/use-upsell-drawer', () => ({
  useUpsellDrawer: () => ({
    isVisible: false,
    config: {},
    showUpsellDrawer: jest.fn(),
    hideUpsellDrawer: jest.fn()
  })
}));

jest.mock('@/contexts/signalr-chat-context', () => ({
  useSignalRChatContextOptional: () => null
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn()
  })
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock data para testes
const mockChatsData = {
  data: [
    {
      id: '1',
      name: 'Chat Recente',
      lastMessage: {
        content: 'Mensagem recente',
        createdAt: new Date().toISOString()
      },
      lastActivity: new Date().toISOString(),
      unreadCount: 2
    },
    {
      id: '2',
      name: 'Chat Antigo',
      lastMessage: {
        content: 'Mensagem antiga',
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() // 10 dias atrás
      },
      lastActivity: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      unreadCount: 0
    },
    {
      id: '3',
      name: 'Chat Não Lido',
      lastMessage: {
        content: 'Mensagem não lida',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 horas atrás
      },
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      unreadCount: 5
    }
  ],
  pagination: {
    currentPage: 1,
    totalPages: 1,
    pageSize: 20,
    totalCount: 3,
    hasNextPage: false,
    hasPreviousPage: false
  }
};

describe('Messages Filter System', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    // Mock do useChats hook
    const { useChats } = require('@/hooks/api/use-chats');
    useChats.mockReturnValue({
      data: mockChatsData,
      isLoading: false,
      isError: false
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderMessages = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <Messages />
      </QueryClientProvider>
    );
  };

  it('deve renderizar todos os chats sem filtros', async () => {
    const { getByText } = renderMessages();
    
    await waitFor(() => {
      expect(getByText('Chat Recente')).toBeTruthy();
      expect(getByText('Chat Antigo')).toBeTruthy();
      expect(getByText('Chat Não Lido')).toBeTruthy();
    });
  });

  it('deve aplicar filtro de mensagens não lidas', async () => {
    const { getByText, getByTestId } = renderMessages();
    
    // Abrir drawer de filtros
    const filterButton = getByTestId('filter-button');
    fireEvent.press(filterButton);
    
    // Selecionar filtro "Não lidas"
    const unreadFilter = getByText('Não lidas');
    fireEvent.press(unreadFilter);
    
    // Fechar drawer
    const closeButton = getByTestId('close-filter-drawer');
    fireEvent.press(closeButton);
    
    await waitFor(() => {
      expect(getByText('Chat Recente')).toBeTruthy();
      expect(getByText('Chat Não Lido')).toBeTruthy();
      // Chat Antigo não deve aparecer (unreadCount = 0)
    });
  });

  it('deve aplicar filtro de mensagens antigas', async () => {
    const { getByText, getByTestId } = renderMessages();
    
    // Abrir drawer de filtros
    const filterButton = getByTestId('filter-button');
    fireEvent.press(filterButton);
    
    // Selecionar filtro "Mais antigas"
    const olderFilter = getByText('Mais antigas');
    fireEvent.press(olderFilter);
    
    // Fechar drawer
    const closeButton = getByTestId('close-filter-drawer');
    fireEvent.press(closeButton);
    
    await waitFor(() => {
      expect(getByText('Chat Antigo')).toBeTruthy();
      // Chats recentes não devem aparecer
    });
  });

  it('deve aplicar filtro de mensagens recentes', async () => {
    const { getByText, getByTestId } = renderMessages();
    
    // Abrir drawer de filtros
    const filterButton = getByTestId('filter-button');
    fireEvent.press(filterButton);
    
    // Selecionar filtro "Mais recentes"
    const recentFilter = getByText('Mais recentes');
    fireEvent.press(recentFilter);
    
    // Fechar drawer
    const closeButton = getByTestId('close-filter-drawer');
    fireEvent.press(closeButton);
    
    await waitFor(() => {
      expect(getByText('Chat Recente')).toBeTruthy();
      expect(getByText('Chat Não Lido')).toBeTruthy();
      // Chat Antigo não deve aparecer (mais de 24h)
    });
  });

  it('deve limpar todos os filtros', async () => {
    const { getByText, getByTestId } = renderMessages();
    
    // Abrir drawer de filtros
    const filterButton = getByTestId('filter-button');
    fireEvent.press(filterButton);
    
    // Selecionar um filtro
    const unreadFilter = getByText('Não lidas');
    fireEvent.press(unreadFilter);
    
    // Limpar filtros
    const clearButton = getByText('Limpar filtros');
    fireEvent.press(clearButton);
    
    // Fechar drawer
    const closeButton = getByTestId('close-filter-drawer');
    fireEvent.press(closeButton);
    
    await waitFor(() => {
      expect(getByText('Chat Recente')).toBeTruthy();
      expect(getByText('Chat Antigo')).toBeTruthy();
      expect(getByText('Chat Não Lido')).toBeTruthy();
    });
  });
});
