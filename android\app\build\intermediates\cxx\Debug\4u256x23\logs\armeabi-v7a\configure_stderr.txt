CMake Warning in C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/./

  has 194 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/ReactNativeBlobUtilSpec/ComponentDescriptors.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


