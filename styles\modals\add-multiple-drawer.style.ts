import {StyleSheet} from "react-native";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828",
    paddingTop: 50
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  backButton: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  title: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  content: {
    flex: 1,
    paddingHorizontal: 24
  },
  label: {
    color: "#EAECF0",
    fontSize: 14,
    marginBottom: 8
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  input: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    paddingHorizontal: 12,
    paddingVertical: 8,
    color: "#1D2939",
    fontSize: 14
  },
  addButton: {
    backgroundColor: "#FFFFFF",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  addButtonText: {
    color: "#1D2939",
    fontSize: 18,
    fontWeight: "700"
  },
  counter: {
    color: "#EAECF0",
    fontSize: 12,
    marginTop: 6
  },
  addedList: {
    marginTop: 16,
    gap: 8
  },
  addedTitle: {
    color: "#EAECF0",
    fontSize: 14,
    fontWeight: "600"
  },
  addedItemRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#0B1220",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#1F2A37",
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  addedItemText: {
    color: "#DFE9F0",
    fontSize: 14
  },
  removeChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: "#1F2A37"
  },
  removeChipText: {
    color: "#DFE9F0",
    fontSize: 12,
    fontWeight: "600"
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 34,
    gap: 4
  }
});

export default styles;

