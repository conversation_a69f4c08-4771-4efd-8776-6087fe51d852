import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import CheckIcon from "@/components/icons/check-icon";
import {useTitleInstallmentOptions} from "@/hooks/api/use-titles";
import {TitleInstallmentOption} from "@/models/api/payments.models";
import styles from "@/styles/registration/title-installment-selection.style";

const TitleInstallmentSelection: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [selectedOption, setSelectedOption] =
    useState<TitleInstallmentOption | null>(null);

  // Buscar opções de parcelamento do título
  const {
    data: installmentOptions,
    isLoading,
    error
  } = useTitleInstallmentOptions();

  const handleOptionSelect = useCallback((option: TitleInstallmentOption) => {
    setSelectedOption(option);
  }, []);

  const handleNext = useCallback(() => {
    console.log("🔍 [TITLE-INSTALLMENT] handleNext chamado");
    console.log("🔍 [TITLE-INSTALLMENT] selectedOption:", selectedOption);
    console.log("🔍 [TITLE-INSTALLMENT] params recebidos:", params);

    if (!selectedOption) {
      console.log("❌ [TITLE-INSTALLMENT] Nenhuma opção selecionada");
      Alert.alert(
        t("titleInstallmentSelection.error.title", "Erro de validação"),
        t(
          "titleInstallmentSelection.error.selectOption",
          "Você deve selecionar uma opção de parcelamento para continuar"
        )
      );
      return;
    }

    const navigationParams = {
      ...params,
      selectedInstallments: selectedOption.installments.toString(),
      installmentValue: selectedOption.value.toString(),
      totalValue: selectedOption.total.toString(),
      withFee: selectedOption.withFee.toString(),
      feePercentage: selectedOption.feePercentage?.toString() || "0"
    };

    console.log(
      "🚀 [TITLE-INSTALLMENT] Navegando para payment-method-selection com params:",
      navigationParams
    );

    // Navegar para a próxima tela com os dados selecionados
    router.push({
      pathname: "/(registration)/payment-method-selection",
      params: navigationParams
    });
  }, [selectedOption, params, router, t]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  const formatCurrency = useCallback((value: number) => {
    return `R$ ${(value / 100).toFixed(2).replace(".", ",")}`;
  }, []);

  const renderInstallmentOption = useCallback(
    (option: TitleInstallmentOption) => {
      const isSelected = selectedOption?.installments === option.installments;

      return (
        <TouchableOpacity
          key={option.installments}
          style={[styles.optionCard, isSelected && styles.optionCardSelected]}
          onPress={() => handleOptionSelect(option)}
          activeOpacity={0.8}
        >
          {isSelected && (
            <View style={styles.greenSection}>
              <View style={styles.checkIcon}>
                <CheckIcon width={24} height={24} replaceColor="#FFFFFF" />
              </View>
            </View>
          )}

          <View
            style={[
              styles.optionContent,
              isSelected && styles.optionContentSelected
            ]}
          >
            <View style={styles.optionHeader}>
              <Text style={styles.installmentText}>
                {option.installments === 1
                  ? t("titleInstallmentSelection.singlePayment", "À vista")
                  : t(
                      "titleInstallmentSelection.installments",
                      "{{count}}x de {{value}}",
                      {
                        count: option.installments,
                        value: formatCurrency(option.value)
                      }
                    )}
              </Text>
              {option.withFee && (
                <View style={styles.feeBadge}>
                  <Text style={styles.feeText}>
                    {t("titleInstallmentSelection.withFee", "Com taxa")}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.optionDetails}>
              <Text style={styles.totalText}>
                {t("titleInstallmentSelection.total", "Total: {{value}}", {
                  value: formatCurrency(option.total)
                })}
              </Text>
              {option.withFee && option.feePercentage && (
                <Text style={styles.feePercentageText}>
                  {t(
                    "titleInstallmentSelection.feePercentage",
                    "Taxa de {{percentage}}%",
                    {percentage: option.feePercentage}
                  )}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      );
    },
    [selectedOption, handleOptionSelect, formatCurrency, t]
  );

  if (isLoading) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>
            {t(
              "titleInstallmentSelection.loading",
              "Carregando opções de parcelamento..."
            )}
          </Text>
        </View>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t(
              "titleInstallmentSelection.error.loadOptions",
              "Erro ao carregar opções de parcelamento. Tente novamente."
            )}
          </Text>
          <FullSizeButton
            text={t("common.tryAgain", "Tentar novamente")}
            onPress={() => router.back()}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <View style={styles.container}>
        <BackButton />

        <View style={styles.progressContainer}>
          <Text style={styles.progressTitle}>
            {t("titleInstallmentSelection.createAccount", "Criar conta")}
          </Text>
          <Text style={styles.progressStep}>
            {t(
              "titleInstallmentSelection.stepProgress",
              "2 / 7 Parcelamento do título"
            )}
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, {width: "28%"}]} />
          </View>
        </View>

        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t("titleInstallmentSelection.title", "Título de Adesão")}
          </Text>
          <Text style={styles.subtitle}>
            {t(
              "titleInstallmentSelection.description",
              "Escolha a forma de pagamento do seu título de adesão."
            )}
          </Text>
        </View>

        <ScrollView
          style={styles.optionsContainer}
          showsVerticalScrollIndicator={false}
        >
          {installmentOptions?.map(renderInstallmentOption)}
        </ScrollView>

        <View style={styles.buttonContainer}>
          <View style={styles.backButton}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={styles.nextButton}>
            <FullSizeButton
              text={t("common.next", "Avançar")}
              onPress={handleNext}
              disabled={!selectedOption}
            />
          </View>
        </View>
      </View>
    </Screen>
  );
};

export default TitleInstallmentSelection;
