import React, {createContext, useContext, useState, ReactNode} from "react";

// Tipos para os dados do fluxo de cadastro
export interface PersonalData {
  fullName: string;
  document: string;
  birthDate: string;
  sex: string;
  selectedPlanId: string;
  selectedPlanTermId?: string; // carry plan termId through registration flow
  selectedFranchiseId: string;
}

export interface ProfessionalData {
  profession: string;
  companyName: string;
  companyRole: string;
  companySegmentId: string;
}

export interface AddressData {
  addressStreet: string;
  addressNumber: string;
  addressComplement: string;
  addressNeighborhood: string;
  addressCity: string;
  addressState: string;
  addressZip: string;
}

export interface ContactsData {
  phoneNumber: string;
  email: string;
  website: string;
  facebook: string;
  instagram: string;
  linkedin: string;
  whereMet: string;
}

export interface RegistrationData {
  personal: PersonalData | null;
  professional: ProfessionalData | null;
  address: AddressData | null;
  contacts: ContactsData | null;
}

interface RegistrationContextType {
  data: RegistrationData;
  updatePersonalData: (data: PersonalData) => void;
  updateProfessionalData: (data: ProfessionalData) => void;
  updateAddressData: (data: AddressData) => void;
  updateContactsData: (data: ContactsData) => void;
  clearData: () => void;
  getAllData: () => Record<string, any>;
}

const RegistrationContext = createContext<RegistrationContextType | undefined>(
  undefined
);

export const useRegistrationContext = () => {
  const context = useContext(RegistrationContext);
  if (!context) {
    throw new Error(
      "useRegistrationContext must be used within a RegistrationProvider"
    );
  }
  return context;
};

interface RegistrationProviderProps {
  children: ReactNode;
}

export const RegistrationProvider: React.FC<RegistrationProviderProps> = ({
  children
}) => {
  const [data, setData] = useState<RegistrationData>({
    personal: null,
    professional: null,
    address: null,
    contacts: null
  });

  const updatePersonalData = (personalData: PersonalData) => {
    console.log(
      "🔄 [REGISTRATION-CONTEXT] Atualizando dados pessoais:",
      personalData
    );
    setData((prev) => ({...prev, personal: personalData}));
  };

  const updateProfessionalData = (professionalData: ProfessionalData) => {
    console.log(
      "🔄 [REGISTRATION-CONTEXT] Atualizando dados profissionais:",
      professionalData
    );
    setData((prev) => ({...prev, professional: professionalData}));
  };

  const updateAddressData = (addressData: AddressData) => {
    console.log(
      "🔄 [REGISTRATION-CONTEXT] Atualizando dados de endereço:",
      addressData
    );
    setData((prev) => ({...prev, address: addressData}));
  };

  const updateContactsData = (contactsData: ContactsData) => {
    console.log(
      "🔄 [REGISTRATION-CONTEXT] Atualizando dados de contato:",
      contactsData
    );
    setData((prev) => ({...prev, contacts: contactsData}));
  };

  const clearData = () => {
    console.log("🗑️ [REGISTRATION-CONTEXT] Limpando todos os dados");
    setData({
      personal: null,
      professional: null,
      address: null,
      contacts: null
    });
  };

  const getAllData = () => {
    const allData: Record<string, any> = {};

    if (data.personal) {
      Object.assign(allData, data.personal);
    }

    if (data.professional) {
      Object.assign(allData, data.professional);
    }

    if (data.address) {
      Object.assign(allData, data.address);
    }

    if (data.contacts) {
      Object.assign(allData, data.contacts);
    }

    console.log(
      "📦 [REGISTRATION-CONTEXT] Retornando todos os dados:",
      allData
    );
    return allData;
  };

  return (
    <RegistrationContext.Provider
      value={{
        data,
        updatePersonalData,
        updateProfessionalData,
        updateAddressData,
        updateContactsData,
        clearData,
        getAllData
      }}
    >
      {children}
    </RegistrationContext.Provider>
  );
};
