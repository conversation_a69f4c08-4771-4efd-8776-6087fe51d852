import {useQuery, useMutation, UseQueryOptions} from "@tanstack/react-query";
import {
  AddressesService,
  State,
  City,
  AddressData
} from "@/services/api/addresses/addresses.service";
import {BaseApiError} from "@/services/api/base/api-errors";

export const addressesKeys = {
  all: ["addresses"] as const,
  states: () => [...addressesKeys.all, "states"] as const,
  cities: (uf: string) => [...addressesKeys.all, "cities", uf] as const,
  cep: (cep: string) => [...addressesKeys.all, "cep", cep] as const
};

export const useStates = (options?: UseQueryOptions<State[], BaseApiError>) => {
  return useQuery({
    queryKey: addressesKeys.states(),
    queryFn: () => AddressesService.getStates(),
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    ...options
  });
};

export const useCitiesByState = (
  uf: string,
  options?: UseQueryOptions<City[], BaseApiError>
) => {
  return useQuery({
    queryKey: addressesKeys.cities(uf),
    queryFn: () => AddressesService.getCitiesByState(uf),
    enabled: !!uf,
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    ...options
  });
};

export const useCepLookup = () => {
  return useMutation({
    mutationFn: (cep: string) => AddressesService.getAddressByCep(cep),
    retry: 1,
    retryDelay: 1000
  });
};
