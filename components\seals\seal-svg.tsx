import React from "react";
import {View} from "react-native";
import Svg, {Defs, LinearGradient, Stop, Rect, Path} from "react-native-svg";
import {BadgeType} from "@/models/api/user-badges.models";

export interface SealSVGProps {
  badgeType: BadgeType;
  size?: number;
  variant?: "square" | "circle";
  level?: number; // Novo prop para controlar o nível do badge
}

const SealSVG: React.FC<SealSVGProps> = ({
  badgeType,
  size = 40,
  variant = "square",
  level = 0 // Padrão é nível 0 (Bronze)
}) => {
  // Usar IDs estáveis baseados no tipo de badge, variante e nível para evitar re-renderizações desnecessárias
  const gradientId = `gradient-${badgeType}-${variant}-${size}-${level}`;

  // Função para renderizar ícone baseado no nível (indexação baseada em zero)
  const renderLevelIcon = () => {
    switch (level) {
      case 0:
        return renderLevel1Icon(); // Nível 0 = Ícone 1 (Bronze)
      case 1:
        return renderLevel2Icon(); // Nível 1 = Ícone 2 (Prata)
      case 2:
        return renderLevel3Icon(); // Nível 2 = Ícone 3 (Ouro)
      case 3:
        return renderLevel4Icon(); // Nível 3 = Ícone 4 (Platina)
      default:
        return renderLevel1Icon(); // Fallback para nível 0 (Bronze)
    }
  };

  // Para versão circular, usar os mesmos ícones dinâmicos mas com fundo circular
  if (variant === "circle") {
    return (
      <View
        style={{
          width: size,
          height: size,
          alignItems: "center",
          justifyContent: "center",
          borderRadius: size / 2, // Fazer circular
          overflow: "hidden"
        }}
      >
        {renderLevelIcon()}
      </View>
    );
  }

  // Função para renderizar ícone do Nível 1
  function renderLevel1Icon() {
    return (
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient
            id={`${gradientId}-4846060465`}
            gradientTransform="matrix(-28.117 -1.22903e-06 1.22903e-06 -28.117 28.3429 45.9456)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${gradientId}-1760128730`}
            gradientTransform="matrix(-37.8196 7.02816e-07 -7.02816e-07 -37.8196 64 50.7969)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${gradientId}-7485699677`}
            gradientTransform="matrix(-1.39936e-15 11.5421 -11.5421 7.06749e-16 24.7412 26.3982)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${gradientId}-4538329168`}
            gradientTransform="matrix(1.91662e-15 31.3008 -31.3008 -1.83677e-15 34.6205 -2.43201e-16)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${gradientId}-7864771216`}
            gradientTransform="matrix(-0.0484654 -13.4659 13.4734 -4.77492 1.93862 15.8534)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0.0625" stopColor="rgb(134, 112, 78)" />
            <Stop offset="1" stopColor="rgb(202, 198, 197)" />
          </LinearGradient>
          <LinearGradient
            id={`${gradientId}-3832923907`}
            gradientTransform="matrix(-0.0377393 -10.4857 10.4915 -3.71816 1.50957 12.3448)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0.0625" stopColor="rgb(134, 112, 78)" />
            <Stop offset="1" stopColor="rgb(202, 198, 197)" />
          </LinearGradient>
        </Defs>

        <Rect
          width="64"
          height="64"
          rx="11.815386"
          ry="11.815386"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${gradientId}-4846060465)`}
          fillOpacity="0.15"
        />

        <Path
          d="M11.1857 0.934603C9.20046 1.10213 7.81274 1.43166 6.64488 2.05C4.68734 3.08647 3.08647 4.68734 2.05 6.64488C1.43166 7.81274 1.10213 9.20046 0.934603 11.1857C0.766281 13.1804 0.765645 15.7077 0.765645 19.1409L0.765645 44.8591C0.765645 48.2923 0.766281 50.8196 0.934603 52.8143C1.10213 54.7995 1.43166 56.1873 2.05 57.3551C3.08647 59.3127 4.68734 60.9135 6.64488 61.95C7.81274 62.5683 9.20046 62.8979 11.1857 63.0654C13.1804 63.2337 15.7077 63.2344 19.1409 63.2344L44.8591 63.2344C48.2923 63.2344 50.8196 63.2337 52.8143 63.0654C54.7995 62.8979 56.1873 62.5683 57.3551 61.95C59.3127 60.9135 60.9135 59.3127 61.95 57.3551C62.5683 56.1873 62.8979 54.7995 63.0654 52.8143C63.2337 50.8196 63.2344 48.2923 63.2344 44.8591L63.2344 19.1409C63.2344 15.7077 63.2337 13.1804 63.0654 11.1857C62.8979 9.20046 62.5683 7.81274 61.95 6.64488C60.9135 4.68734 59.3127 3.08647 57.3551 2.05C56.1873 1.43166 54.7995 1.10213 52.8143 0.934603C50.8196 0.766281 48.2923 0.765645 44.8591 0.765645L19.1409 0.765645C15.7077 0.765645 13.1804 0.766281 11.1857 0.934603ZM1.37335 6.28661C0 8.88043 0 12.3006 0 19.1409L0 44.8591C0 51.6994 0 55.1196 1.37335 57.7134C2.48164 59.8066 4.19343 61.5184 6.28661 62.6266C8.88043 64 12.3006 64 19.1409 64L44.8591 64C51.6994 64 55.1196 64 57.7134 62.6266C59.8066 61.5184 61.5184 59.8066 62.6266 57.7134C64 55.1196 64 51.6994 64 44.8591L64 19.1409C64 12.3006 64 8.88043 62.6266 6.28661C61.5184 4.19343 59.8066 2.48164 57.7134 1.37335C55.1196 0 51.6994 0 44.8591 0L19.1409 0C12.3006 0 8.88043 0 6.28661 1.37335C4.19343 2.48164 2.48164 4.19343 1.37335 6.28661Z"
          fillRule="evenodd"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${gradientId}-1760128730)`}
          fillOpacity="0.28"
        />

        <Rect
          width="37.940308"
          height="37.940308"
          rx="8.4220972"
          ry="8.4220972"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 5.17215 32)"
          fill={`url(#${gradientId}-7485699677)`}
          fillOpacity="0.15"
        />

        <Path
          d="M7.99175 0.885301C6.59073 1.00353 5.63481 1.23445 4.83942 1.65559C3.48302 2.37376 2.37376 3.48302 1.65559 4.83942C1.23445 5.63481 1.00353 6.59073 0.885301 7.99175C0.766281 9.40216 0.765645 11.1928 0.765645 13.6438L0.765645 24.2965C0.765645 26.7475 0.766281 28.5382 0.885301 29.9486C1.00353 31.3496 1.23445 32.3055 1.65559 33.1009C2.37376 34.4573 3.48302 35.5665 4.83942 36.2847C5.63481 36.7059 6.59073 36.9368 7.99175 37.055C9.40216 37.174 11.1928 37.1747 13.6438 37.1747L24.2965 37.1747C26.7475 37.1747 28.5382 37.174 29.9486 37.055C31.3496 36.9368 32.3055 36.7059 33.1009 36.2847C34.4573 35.5665 35.5665 34.4573 36.2847 33.1009C36.7059 32.3055 36.9368 31.3496 37.055 29.9486C37.174 28.5382 37.1747 26.7475 37.1747 24.2965L37.1747 13.6438C37.1747 11.1928 37.174 9.40216 37.055 7.99175C36.9368 6.59073 36.7059 5.63481 36.2847 4.83942C35.5665 3.48302 34.4573 2.37376 33.1009 1.65559C32.3055 1.23445 31.3496 1.00353 29.9486 0.885301C28.5382 0.766281 26.7475 0.765645 24.2965 0.765645L13.6438 0.765645C11.1928 0.765645 9.40216 0.766281 7.99175 0.885301ZM0.978937 4.48115C0 6.33004 0 8.76796 0 13.6438L0 24.2965C0 29.1723 0 31.6103 0.978937 33.4592C1.76893 34.9512 2.98911 36.1714 4.48115 36.9614C6.33004 37.9403 8.76796 37.9403 13.6438 37.9403L24.2965 37.9403C29.1723 37.9403 31.6103 37.9403 33.4592 36.9614C34.9512 36.1714 36.1714 34.9512 36.9614 33.4592C37.9403 31.6103 37.9403 29.1723 37.9403 24.2965L37.9403 13.6438C37.9403 8.76796 37.9403 6.33004 36.9614 4.48115C36.1714 2.98911 34.9512 1.76893 33.4592 0.978937C31.6103 0 29.1723 0 24.2965 0L13.6438 0C8.76796 0 6.33004 0 4.48115 0.978937C2.98911 1.76893 1.76893 2.98911 0.978937 4.48115Z"
          fillRule="evenodd"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 5.17215 32)"
          fill={`url(#${gradientId}-4538329168)`}
          fillOpacity="0.28"
        />

        <Path
          d="M0 6.73297C0 3.01445 3.01445 0 6.73297 0L6.74307 0C10.4616 0 13.4734 3.01445 13.4734 6.73297L13.4734 6.74307C13.4734 10.4616 10.4589 13.4659 6.74041 13.4659L6.7303 13.4659C3.01178 13.4659 0 10.5515 0 6.83297L0 6.73297Z"
          fillRule="nonzero"
          transform="matrix(1 0 0 1 25.341 25.7935)"
          fill={`url(#${gradientId}-7864771216)`}
        />

        <Path
          d="M6.73297 0C3.01445 0 0 3.01445 0 6.73297L0 6.83297C0 10.5515 3.01178 13.4659 6.7303 13.4659L6.74041 13.4659C10.4589 13.4659 13.4734 10.4616 13.4734 6.74307L13.4734 6.73297C13.4734 3.01445 10.4616 0 6.74307 0L6.73297 0ZM4.74336 2.02137Q5.69171 1.62025 6.73297 1.62025L6.74307 1.62025Q7.7842 1.62025 8.73209 2.0213Q9.64878 2.40914 10.3567 3.11742Q11.0646 3.82572 11.4523 4.74297Q11.8531 5.69142 11.8531 6.73297L11.8531 6.74307Q11.8531 7.78386 11.4523 8.73046Q11.0647 9.64563 10.3568 10.3521Q9.64884 11.0586 8.7315 11.4455Q7.78276 11.8457 6.74041 11.8457L6.7303 11.8457Q5.67822 11.8457 4.72645 11.4541Q3.80853 11.0764 3.10478 10.3867Q2.40199 9.69795 2.01845 8.80105Q1.62025 7.86989 1.62025 6.83297L1.62025 6.73297Q1.62025 5.69171 2.02137 4.74336Q2.40932 3.82614 3.11773 3.11773Q3.82614 2.40932 4.74336 2.02137Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 25.341 25.7935)"
          fill="rgb(255, 255, 255)"
          fillOpacity="0.2"
        />

        <Path
          d="M0 5.24287C0 2.34731 2.34731 0 5.24287 0L5.25074 0C8.14629 0 10.4915 2.34731 10.4915 5.24287L10.4915 5.25073C10.4915 8.14629 8.14421 10.4857 5.24866 10.4857L5.24079 10.4857C2.34523 10.4857 0 8.14629 0 5.25073L0 5.24287Z"
          fillRule="nonzero"
          transform="matrix(1 0 0 1 26.9144 27.3663)"
          fill={`url(#${gradientId}-3832923907)`}
        />
      </Svg>
    );
  }

  // Função para renderizar ícone do Nível 2
  function renderLevel2Icon() {
    const uniqueId = `level2-${gradientId}`;
    return (
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient
            id={`${uniqueId}-7685726797`}
            gradientTransform="matrix(-28.117 -1.22903e-06 1.22903e-06 -28.117 28.3429 45.9456)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-0859483711`}
            gradientTransform="matrix(-37.8196 7.02816e-07 -7.02816e-07 -37.8196 64 50.7969)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-2284100926`}
            gradientTransform="matrix(-1.28234e-15 10.5769 -10.5769 6.47648e-16 22.6722 24.1907)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-9215345321`}
            gradientTransform="matrix(1.75634e-15 28.6833 -28.6833 -1.68317e-15 31.7254 -2.22864e-16)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-6541931261`}
            gradientTransform="matrix(18.3581 20.3517 -20.3517 18.3581 10.6743 -8.76371)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(122, 150, 172)" />
            <Stop offset="0.18000001" stopColor="rgb(234, 239, 243)" />
            <Stop offset="0.315" stopColor="rgb(194, 211, 225)" />
            <Stop offset="0.49191856" stopColor="rgb(255, 255, 255)" />
            <Stop offset="0.61500001" stopColor="rgb(212, 222, 229)" />
            <Stop offset="0.78500003" stopColor="rgb(170, 188, 200)" />
            <Stop offset="0.95499998" stopColor="rgb(187, 202, 214)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-0624779553`}
            gradientTransform="matrix(15.4507 15.6168 -15.6168 15.4507 10.4666 -4.23648)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(158, 137, 118)" />
            <Stop offset="0.19499999" stopColor="rgb(122, 94, 80)" />
            <Stop offset="0.41" stopColor="rgb(246, 208, 171)" />
            <Stop offset="0.61000001" stopColor="rgb(157, 119, 78)" />
            <Stop offset="0.85500002" stopColor="rgb(201, 155, 112)" />
            <Stop offset="1" stopColor="rgb(121, 95, 82)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-4850796721`}
            gradientTransform="matrix(-18.2636 8.88934 -8.88934 -18.2636 24.272 17.3222)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(158, 137, 118)" />
            <Stop offset="0.19499999" stopColor="rgb(122, 94, 80)" />
            <Stop offset="0.41" stopColor="rgb(246, 208, 171)" />
            <Stop offset="0.61000001" stopColor="rgb(157, 119, 78)" />
            <Stop offset="0.85500002" stopColor="rgb(201, 155, 112)" />
            <Stop offset="1" stopColor="rgb(121, 95, 82)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-9196268035`}
            gradientTransform="matrix(6.14173 12.2835 -12.2835 6.14173 7.07973 -2.13283)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(158, 137, 118)" />
            <Stop offset="0.19499999" stopColor="rgb(122, 94, 80)" />
            <Stop offset="0.41" stopColor="rgb(246, 208, 171)" />
            <Stop offset="0.61000001" stopColor="rgb(157, 119, 78)" />
            <Stop offset="0.85500002" stopColor="rgb(201, 155, 112)" />
            <Stop offset="1" stopColor="rgb(121, 95, 82)" />
          </LinearGradient>
        </Defs>

        <Rect
          width="64.000008"
          height="64.000008"
          rx="10.97143"
          ry="10.97143"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${uniqueId}-7685726797)`}
          fillOpacity="0.15"
        />

        <Path
          d="M10.3893 0.898341C8.54782 1.05374 7.26385 1.35918 6.18457 1.93062C4.37227 2.89019 2.89019 4.37227 1.93062 6.18457C1.35918 7.26385 1.05374 8.54782 0.898341 10.3893C0.742176 12.2399 0.74156 14.5852 0.74156 17.7737L0.74156 46.2263C0.74156 49.4148 0.742176 51.7601 0.898341 53.6107C1.05374 55.4522 1.35918 56.7362 1.93062 57.8154C2.89019 59.6277 4.37227 61.1098 6.18457 62.0694C7.26385 62.6408 8.54782 62.9463 10.3893 63.1017C12.2399 63.2578 14.5852 63.2584 17.7737 63.2584L46.2263 63.2584C49.4148 63.2584 51.7601 63.2578 53.6107 63.1017C55.4522 62.9463 56.7362 62.6408 57.8154 62.0694C59.6277 61.1098 61.1098 59.6277 62.0694 57.8154C62.6408 56.7362 62.9463 55.4522 63.1017 53.6107C63.2578 51.7601 63.2584 49.4148 63.2584 46.2263L63.2584 17.7737C63.2584 14.5852 63.2578 12.2399 63.1017 10.3893C62.9463 8.54782 62.6408 7.26385 62.0694 6.18457C61.1098 4.37227 59.6277 2.89019 57.8154 1.93062C56.7362 1.35918 55.4522 1.05374 53.6107 0.898341C51.7601 0.742176 49.4148 0.74156 46.2263 0.74156L17.7737 0.74156C14.5852 0.74156 12.2399 0.742176 10.3893 0.898341ZM1.27526 5.83757C0 8.24611 0 11.422 0 17.7737L0 46.2263C0 52.578 0 55.7539 1.27526 58.1624C2.30438 60.1061 3.8939 61.6956 5.83757 62.7248C8.24611 64 11.422 64 17.7737 64L46.2263 64C52.578 64 55.7539 64 58.1624 62.7248C60.1061 61.6956 61.6956 60.1061 62.7248 58.1624C64 55.7539 64 52.578 64 46.2263L64 17.7737C64 11.422 64 8.24611 62.7248 5.83757C61.6956 3.8939 60.1061 2.30438 58.1624 1.27526C55.7539 0 52.578 0 46.2263 0L17.7737 0C11.422 0 8.24611 0 5.83757 1.27526C3.8939 2.30438 2.30438 3.8939 1.27526 5.83757Z"
          fillRule="evenodd"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${uniqueId}-0859483711)`}
          fillOpacity="0.28"
        />

        <Rect
          width="34.767593"
          height="34.767593"
          rx="8.1571627"
          ry="8.1571627"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 7.41562 32)"
          fill={`url(#${uniqueId}-2284100926)`}
          fillOpacity="0.15"
        />

        <Path
          d="M7.74035 0.857452C6.38341 0.97196 5.45755 1.19562 4.68718 1.60351C3.37345 2.29909 2.29909 3.37345 1.60351 4.68718C1.19562 5.45755 0.97196 6.38341 0.857452 7.74035C0.742176 9.10639 0.74156 10.8407 0.74156 13.2146L0.74156 21.553C0.74156 23.9269 0.742176 25.6612 0.857452 27.0272C0.97196 28.3842 1.19562 29.31 1.60351 30.0804C2.29909 31.3941 3.37345 32.4685 4.68718 33.1641C5.45755 33.572 6.38341 33.7956 7.74035 33.9101C9.10639 34.0254 10.8407 34.026 13.2146 34.026L21.553 34.026C23.9269 34.026 25.6612 34.0254 27.0272 33.9101C28.3842 33.7956 29.31 33.572 30.0804 33.1641C31.3941 32.4685 32.4685 31.3941 33.1641 30.0804C33.572 29.31 33.7956 28.3842 33.9101 27.0272C34.0254 25.6612 34.026 23.9269 34.026 21.553L34.026 13.2146C34.026 10.8407 34.0254 9.10639 33.9101 7.74035C33.7956 6.38341 33.572 5.45755 33.1641 4.68718C32.4685 3.37345 31.3941 2.29909 30.0804 1.60351C29.31 1.19562 28.3842 0.97196 27.0272 0.857452C25.6612 0.742176 23.9269 0.74156 21.553 0.74156L13.2146 0.74156C10.8407 0.74156 9.10639 0.742176 7.74035 0.857452ZM0.948143 4.34018C0 6.13091 0 8.49214 0 13.2146L0 21.553C0 26.2754 0 28.6367 0.948143 30.4274C1.71329 31.8725 2.89508 33.0543 4.34018 33.8195C6.13091 34.7676 8.49214 34.7676 13.2146 34.7676L21.553 34.7676C26.2754 34.7676 28.6367 34.7676 30.4274 33.8195C31.8725 33.0543 33.0543 31.8725 33.8195 30.4274C34.7676 28.6367 34.7676 26.2754 34.7676 21.553L34.7676 13.2146C34.7676 8.49214 34.7676 6.13091 33.8195 4.34018C33.0543 2.89508 31.8725 1.71329 30.4274 0.948143C28.6367 0 26.2754 0 21.553 0L13.2146 0C8.49214 0 6.13091 0 4.34018 0.948143C2.89508 1.71329 1.71329 2.89508 0.948143 4.34018Z"
          fillRule="evenodd"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 7.41562 32)"
          fill={`url(#${uniqueId}-9215345321)`}
          fillOpacity="0.28"
        />

        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 31.9999)"
          fill={`url(#${uniqueId}-6541931261)`}
        />
        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 31.9999)"
          fill={`url(#${uniqueId}-0624779553)`}
        />
        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 31.9999)"
          fill={`url(#${uniqueId}-4850796721)`}
        />

        <Rect
          width="16.916586"
          height="16.916586"
          rx="2.9662414"
          ry="2.9662414"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 20.0382 31.9999)"
          fill="rgb(0, 0, 0)"
          fillOpacity="0.2"
        />

        <Rect
          width="12.219916"
          height="12.219916"
          rx="2.2246809"
          ry="2.2246809"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 23.3592 31.9999)"
          fill={`url(#${uniqueId}-9196268035)`}
        />
      </Svg>
    );
  }

  // Função para renderizar ícone do Nível 3
  function renderLevel3Icon() {
    const uniqueId = `level3-${gradientId}`;
    return (
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient
            id={`${uniqueId}-2491240991`}
            gradientTransform="matrix(-28.117 -1.22903e-06 1.22903e-06 -28.117 28.3429 45.9456)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-0602309084`}
            gradientTransform="matrix(-37.8196 7.02816e-07 -7.02816e-07 -37.8196 64 50.7969)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-6366544114`}
            gradientTransform="matrix(-1.28234e-15 10.5769 -10.5769 6.47648e-16 22.6722 24.1907)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-5599732210`}
            gradientTransform="matrix(1.75634e-15 28.6833 -28.6833 -1.68317e-15 31.7254 -2.22864e-16)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-9881366847`}
            gradientTransform="matrix(18.3581 20.3517 -20.3517 18.3581 10.6743 -8.76371)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(122, 150, 172)" />
            <Stop offset="0.18000001" stopColor="rgb(234, 239, 243)" />
            <Stop offset="0.315" stopColor="rgb(194, 211, 225)" />
            <Stop offset="0.49191856" stopColor="rgb(255, 255, 255)" />
            <Stop offset="0.61500001" stopColor="rgb(212, 222, 229)" />
            <Stop offset="0.78500003" stopColor="rgb(170, 188, 200)" />
            <Stop offset="0.95499998" stopColor="rgb(187, 202, 214)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-7536618451`}
            gradientTransform="matrix(15.4507 15.6168 -15.6168 15.4507 10.4666 -4.23648)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(158, 137, 118)" />
            <Stop offset="0.19499999" stopColor="rgb(122, 94, 80)" />
            <Stop offset="0.41" stopColor="rgb(246, 208, 171)" />
            <Stop offset="0.61000001" stopColor="rgb(157, 119, 78)" />
            <Stop offset="0.85500002" stopColor="rgb(201, 155, 112)" />
            <Stop offset="1" stopColor="rgb(121, 95, 82)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-3377664173`}
            gradientTransform="matrix(-17.4554 6.14173 -6.14173 -17.4554 22.5749 18.2111)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(140, 66, 29)" />
            <Stop offset="0.32527247" stopColor="rgb(251, 230, 123)" />
            <Stop offset="0.53548807" stopColor="rgb(252, 251, 231)" />
            <Stop offset="0.76991719" stopColor="rgb(247, 209, 78)" />
            <Stop offset="1" stopColor="rgb(212, 160, 65)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-4095673950`}
            gradientTransform="matrix(6.78823 12.6067 -12.6067 6.78823 9.01921 -3.58745)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(140, 66, 29)" />
            <Stop offset="0.32527247" stopColor="rgb(251, 230, 123)" />
            <Stop offset="0.53548807" stopColor="rgb(252, 251, 231)" />
            <Stop offset="0.76991719" stopColor="rgb(247, 209, 78)" />
            <Stop offset="1" stopColor="rgb(212, 160, 65)" />
          </LinearGradient>
        </Defs>

        <Rect
          width="64.000008"
          height="64.000008"
          rx="10.97143"
          ry="10.97143"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${uniqueId}-2491240991)`}
          fillOpacity="0.15"
        />

        <Path
          d="M10.3893 0.898341C8.54782 1.05374 7.26385 1.35918 6.18457 1.93062C4.37227 2.89019 2.89019 4.37227 1.93062 6.18457C1.35918 7.26385 1.05374 8.54782 0.898341 10.3893C0.742176 12.2399 0.74156 14.5852 0.74156 17.7737L0.74156 46.2263C0.74156 49.4148 0.742176 51.7601 0.898341 53.6107C1.05374 55.4522 1.35918 56.7362 1.93062 57.8154C2.89019 59.6277 4.37227 61.1098 6.18457 62.0694C7.26385 62.6408 8.54782 62.9463 10.3893 63.1017C12.2399 63.2578 14.5852 63.2584 17.7737 63.2584L46.2263 63.2584C49.4148 63.2584 51.7601 63.2578 53.6107 63.1017C55.4522 62.9463 56.7362 62.6408 57.8154 62.0694C59.6277 61.1098 61.1098 59.6277 62.0694 57.8154C62.6408 56.7362 62.9463 55.4522 63.1017 53.6107C63.2578 51.7601 63.2584 49.4148 63.2584 46.2263L63.2584 17.7737C63.2584 14.5852 63.2578 12.2399 63.1017 10.3893C62.9463 8.54782 62.6408 7.26385 62.0694 6.18457C61.1098 4.37227 59.6277 2.89019 57.8154 1.93062C56.7362 1.35918 55.4522 1.05374 53.6107 0.898341C51.7601 0.742176 49.4148 0.74156 46.2263 0.74156L17.7737 0.74156C14.5852 0.74156 12.2399 0.742176 10.3893 0.898341ZM1.27526 5.83757C0 8.24611 0 11.422 0 17.7737L0 46.2263C0 52.578 0 55.7539 1.27526 58.1624C2.30438 60.1061 3.8939 61.6956 5.83757 62.7248C8.24611 64 11.422 64 17.7737 64L46.2263 64C52.578 64 55.7539 64 58.1624 62.7248C60.1061 61.6956 61.6956 60.1061 62.7248 58.1624C64 55.7539 64 52.578 64 46.2263L64 17.7737C64 11.422 64 8.24611 62.7248 5.83757C61.6956 3.8939 60.1061 2.30438 58.1624 1.27526C55.7539 0 52.578 0 46.2263 0L17.7737 0C11.422 0 8.24611 0 5.83757 1.27526C3.8939 2.30438 2.30438 3.8939 1.27526 5.83757Z"
          fillRule="evenodd"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 1.39876e-06 64)"
          fill={`url(#${uniqueId}-0602309084)`}
          fillOpacity="0.28"
        />

        <Rect
          width="34.767593"
          height="34.767593"
          rx="8.1571627"
          ry="8.1571627"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 7.41562 32)"
          fill={`url(#${uniqueId}-6366544114)`}
          fillOpacity="0.15"
        />

        <Path
          d="M7.74035 0.857452C6.38341 0.97196 5.45755 1.19562 4.68718 1.60351C3.37345 2.29909 2.29909 3.37345 1.60351 4.68718C1.19562 5.45755 0.97196 6.38341 0.857452 7.74035C0.742176 9.10639 0.74156 10.8407 0.74156 13.2146L0.74156 21.553C0.74156 23.9269 0.742176 25.6612 0.857452 27.0272C0.97196 28.3842 1.19562 29.31 1.60351 30.0804C2.29909 31.3941 3.37345 32.4685 4.68718 33.1641C5.45755 33.572 6.38341 33.7956 7.74035 33.9101C9.10639 34.0254 10.8407 34.026 13.2146 34.026L21.553 34.026C23.9269 34.026 25.6612 34.0254 27.0272 33.9101C28.3842 33.7956 29.31 33.572 30.0804 33.1641C31.3941 32.4685 32.4685 31.3941 33.1641 30.0804C33.572 29.31 33.7956 28.3842 33.9101 27.0272C34.0254 25.6612 34.026 23.9269 34.026 21.553L34.026 13.2146C34.026 10.8407 34.0254 9.10639 33.9101 7.74035C33.7956 6.38341 33.572 5.45755 33.1641 4.68718C32.4685 3.37345 31.3941 2.29909 30.0804 1.60351C29.31 1.19562 28.3842 0.97196 27.0272 0.857452C25.6612 0.742176 23.9269 0.74156 21.553 0.74156L13.2146 0.74156C10.8407 0.74156 9.10639 0.742176 7.74035 0.857452ZM0.948143 4.34018C0 6.13091 0 8.49214 0 13.2146L0 21.553C0 26.2754 0 28.6367 0.948143 30.4274C1.71329 31.8725 2.89508 33.0543 4.34018 33.8195C6.13091 34.7676 8.49214 34.7676 13.2146 34.7676L21.553 34.7676C26.2754 34.7676 28.6367 34.7676 30.4274 33.8195C31.8725 33.0543 33.0543 31.8725 33.8195 30.4274C34.7676 28.6367 34.7676 26.2754 34.7676 21.553L34.7676 13.2146C34.7676 8.49214 34.7676 6.13091 33.8195 4.34018C33.0543 2.89508 31.8725 1.71329 30.4274 0.948143C28.6367 0 26.2754 0 21.553 0L13.2146 0C8.49214 0 6.13091 0 4.34018 0.948143C2.89508 1.71329 1.71329 2.89508 0.948143 4.34018Z"
          fillRule="evenodd"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 7.41562 32)"
          fill={`url(#${uniqueId}-5599732210)`}
          fillOpacity="0.28"
        />

        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 32)"
          fill={`url(#${uniqueId}-9881366847)`}
        />
        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 32)"
          fill={`url(#${uniqueId}-7536618451)`}
        />
        <Rect
          width="19.936392"
          height="19.936392"
          rx="4.4493618"
          ry="4.4493618"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.9029 32)"
          fill={`url(#${uniqueId}-3377664173)`}
        />

        <Rect
          width="16.916586"
          height="16.916586"
          rx="2.9662414"
          ry="2.9662414"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 20.0382 32)"
          fill="rgb(0, 0, 0)"
          fillOpacity="0.2"
        />

        <Rect
          width="12.219916"
          height="12.219916"
          rx="2.2246809"
          ry="2.2246809"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 23.3592 32)"
          fill={`url(#${uniqueId}-4095673950)`}
        />
      </Svg>
    );
  }

  // Função para renderizar ícone do Nível 4
  function renderLevel4Icon() {
    const uniqueId = `level4-${gradientId}`;
    return (
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient
            id={`${uniqueId}-7028603460`}
            gradientTransform="matrix(-28.117 -1.22903e-06 1.22903e-06 -28.117 28.3429 45.9456)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-9923798424`}
            gradientTransform="matrix(-37.8196 7.02816e-07 -7.02816e-07 -37.8196 64 50.7969)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-2552042839`}
            gradientTransform="matrix(-1.32399e-15 10.9204 -10.9204 6.68682e-16 23.4086 24.9764)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-4738599143`}
            gradientTransform="matrix(1.81339e-15 29.6149 -29.6149 -1.73784e-15 32.7558 -2.30102e-16)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(255, 255, 255)" />
            <Stop offset="1" stopColor="rgb(255, 255, 255)" stopOpacity="0" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-4885803239`}
            gradientTransform="matrix(-19.6911 7.0087 -7.0087 -19.6911 23.3081 20.9719)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(122, 150, 172)" />
            <Stop offset="0.18000001" stopColor="rgb(234, 239, 243)" />
            <Stop offset="0.315" stopColor="rgb(194, 211, 225)" />
            <Stop offset="0.49191856" stopColor="rgb(255, 255, 255)" />
            <Stop offset="0.61500001" stopColor="rgb(212, 222, 229)" />
            <Stop offset="0.78500003" stopColor="rgb(170, 188, 200)" />
            <Stop offset="0.95499998" stopColor="rgb(187, 202, 214)" />
          </LinearGradient>
          <LinearGradient
            id={`${uniqueId}-6565283100`}
            gradientTransform="matrix(11.618 12.8797 -12.8797 11.618 6.75525 -5.54614)"
            gradientUnits="userSpaceOnUse"
            x1="0"
            y1="0.5"
            x2="1"
            y2="0.5"
          >
            <Stop offset="0" stopColor="rgb(122, 150, 172)" />
            <Stop offset="0.18000001" stopColor="rgb(234, 239, 243)" />
            <Stop offset="0.315" stopColor="rgb(194, 211, 225)" />
            <Stop offset="0.49191856" stopColor="rgb(255, 255, 255)" />
            <Stop offset="0.61500001" stopColor="rgb(212, 222, 229)" />
            <Stop offset="0.78500003" stopColor="rgb(170, 188, 200)" />
            <Stop offset="0.95499998" stopColor="rgb(187, 202, 214)" />
          </LinearGradient>
        </Defs>

        <Rect
          width="64"
          height="64"
          rx="11.815386"
          ry="11.815386"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 2.79753e-06 64)"
          fill={`url(#${uniqueId}-7028603460)`}
          fillOpacity="0.15"
        />

        <Path
          d="M11.1857 0.934603C9.20046 1.10213 7.81274 1.43166 6.64488 2.05C4.68734 3.08647 3.08647 4.68734 2.05 6.64488C1.43166 7.81274 1.10213 9.20046 0.934603 11.1857C0.766281 13.1804 0.765645 15.7077 0.765645 19.1409L0.765645 44.8591C0.765645 48.2923 0.766281 50.8196 0.934603 52.8143C1.10213 54.7995 1.43166 56.1873 2.05 57.3551C3.08647 59.3127 4.68734 60.9135 6.64488 61.95C7.81274 62.5683 9.20046 62.8979 11.1857 63.0654C13.1804 63.2337 15.7077 63.2344 19.1409 63.2344L44.8591 63.2344C48.2923 63.2344 50.8196 63.2337 52.8143 63.0654C54.7995 62.8979 56.1873 62.5683 57.3551 61.95C59.3127 60.9135 60.9135 59.3127 61.95 57.3551C62.5683 56.1873 62.8979 54.7995 63.0654 52.8143C63.2337 50.8196 63.2344 48.2923 63.2344 44.8591L63.2344 19.1409C63.2344 15.7077 63.2337 13.1804 63.0654 11.1857C62.8979 9.20046 62.5683 7.81274 61.95 6.64488C60.9135 4.68734 59.3127 3.08647 57.3551 2.05C56.1873 1.43166 54.7995 1.10213 52.8143 0.934603C50.8196 0.766281 48.2923 0.765645 44.8591 0.765645L19.1409 0.765645C15.7077 0.765645 13.1804 0.766281 11.1857 0.934603ZM1.37335 6.28661C0 8.88043 0 12.3006 0 19.1409L0 44.8591C0 51.6994 0 55.1196 1.37335 57.7134C2.48164 59.8066 4.19343 61.5184 6.28661 62.6266C8.88043 64 12.3006 64 19.1409 64L44.8591 64C51.6994 64 55.1196 64 57.7134 62.6266C59.8066 61.5184 61.5184 59.8066 62.6266 57.7134C64 55.1196 64 51.6994 64 44.8591L64 19.1409C64 12.3006 64 8.88043 62.6266 6.28661C61.5184 4.19343 59.8066 2.48164 57.7134 1.37335C55.1196 0 51.6994 0 44.8591 0L19.1409 0C12.3006 0 8.88043 0 6.28661 1.37335C4.19343 2.48164 2.48164 4.19343 1.37335 6.28661Z"
          fillRule="evenodd"
          transform="matrix(-4.37114e-08 -1 1 -4.37114e-08 2.79753e-06 64)"
          fill={`url(#${uniqueId}-9923798424)`}
          fillOpacity="0.28"
        />

        <Rect
          width="35.896797"
          height="35.896797"
          rx="8.4220972"
          ry="8.4220972"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 6.89231 32)"
          fill={`url(#${uniqueId}-2552042839)`}
          fillOpacity="0.15"
        />

        <Path
          d="M7.99175 0.885301C6.59074 1.00353 5.63481 1.23445 4.83942 1.65559C3.48302 2.37376 2.37376 3.48302 1.65559 4.83942C1.23445 5.63481 1.00353 6.59073 0.885301 7.99175C0.766281 9.40215 0.765645 11.1928 0.765645 13.6438L0.765645 22.253C0.765645 24.704 0.766281 26.4946 0.885301 27.905C1.00353 29.3061 1.23445 30.262 1.65559 31.0574C2.37376 32.4138 3.48302 33.523 4.83942 34.2412C5.63481 34.6623 6.59073 34.8933 7.99175 35.0115C9.40215 35.1305 11.1928 35.1312 13.6438 35.1312L22.253 35.1312C24.704 35.1312 26.4946 35.1305 27.905 35.0115C29.3061 34.8933 30.262 34.6623 31.0574 34.2412C32.4138 33.523 33.523 32.4138 34.2412 31.0574C34.6623 30.262 34.8933 29.3061 35.0115 27.9051C35.1305 26.4946 35.1312 24.704 35.1312 22.253L35.1312 13.6438C35.1312 11.1928 35.1305 9.40216 35.0115 7.99175C34.8933 6.59074 34.6623 5.63481 34.2412 4.83942C33.523 3.48302 32.4138 2.37376 31.0574 1.65559C30.262 1.23445 29.3061 1.00353 27.9051 0.885301C26.4946 0.766281 24.704 0.765645 22.253 0.765645L13.6438 0.765645C11.1928 0.765645 9.40216 0.766281 7.99175 0.885301ZM0.978937 4.48115C0 6.33004 0 8.76796 0 13.6438L0 22.253C0 27.1288 0 29.5668 0.978937 31.4157C1.76893 32.9077 2.98911 34.1279 4.48115 34.9179C6.33004 35.8968 8.76796 35.8968 13.6438 35.8968L22.253 35.8968C27.1288 35.8968 29.5668 35.8968 31.4157 34.9179C32.9077 34.1279 34.1279 32.9077 34.9179 31.4157C35.8968 29.5668 35.8968 27.1288 35.8968 22.253L35.8968 13.6438C35.8968 8.76796 35.8968 6.33004 34.9179 4.48115C34.1279 2.98911 32.9077 1.76893 31.4157 0.978937C29.5668 0 27.1288 0 22.253 0L13.6438 0C8.76796 0 6.33004 0 4.48115 0.978937C2.98911 1.76893 1.76893 2.98911 0.978937 4.48115Z"
          fillRule="evenodd"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 6.89231 32)"
          fill={`url(#${uniqueId}-4738599143)`}
          fillOpacity="0.28"
        />

        <Rect
          width="20.583899"
          height="20.583899"
          rx="4.5938716"
          ry="4.5938716"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 17.7199 32)"
          fill={`url(#${uniqueId}-4885803239)`}
        />

        <Rect
          width="17.466015"
          height="17.466015"
          rx="3.0625811"
          ry="3.0625811"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 19.9245 32)"
          fill="rgb(0, 0, 0)"
          fillOpacity="0.2"
        />

        <Rect
          width="12.616804"
          height="12.616804"
          rx="2.2969358"
          ry="2.2969358"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 23.3533 32)"
          fill={`url(#${uniqueId}-6565283100)`}
        />
      </Svg>
    );
  }

  // Renderizar versão quadrada com ícones dinâmicos baseados no nível
  return (
    <View
      style={{
        width: size,
        height: size,
        alignItems: "center",
        justifyContent: "center"
      }}
    >
      {renderLevelIcon()}
    </View>
  );
};

export default SealSVG;
