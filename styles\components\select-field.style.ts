import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

export default StyleSheet.create({
  container: {
    marginBottom: 16
  },
  
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.white,
    marginBottom: 8
  },
  
  selectButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: stylesConstants.colors.gray800,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: "transparent"
  },
  
  selectButtonError: {
    borderColor: stylesConstants.colors.red500
  },
  
  selectButtonDisabled: {
    opacity: 0.5
  },
  
  selectText: {
    flex: 1,
    fontSize: 16,
    color: stylesConstants.colors.white
  },
  
  selectTextPlaceholder: {
    color: stylesConstants.colors.gray25
  },
  
  selectTextDisabled: {
    color: stylesConstants.colors.gray25
  },
  
  chevronIcon: {
    marginLeft: 8,
    transform: [{rotate: "0deg"}]
  },
  
  chevronIconRotated: {
    transform: [{rotate: "180deg"}]
  },
  
  errorText: {
    fontSize: 14,
    color: stylesConstants.colors.red500,
    marginTop: 4
  },
  
  // Modal styles
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  
  modal: {
    backgroundColor: stylesConstants.colors.gray900,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
    minHeight: "50%"
  },
  
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.gray800
  },
  
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.white
  },
  
  closeButton: {
    padding: 4
  },
  
  searchContainer: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.gray800
  },
  
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.gray800,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: stylesConstants.colors.white
  },
  
  optionsList: {
    flex: 1
  },
  
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.gray800
  },
  
  optionItemSelected: {
    backgroundColor: stylesConstants.colors.gray800
  },
  
  optionText: {
    flex: 1,
    fontSize: 16,
    color: stylesConstants.colors.white
  },
  
  optionTextSelected: {
    color: stylesConstants.colors.primary500,
    fontWeight: "600"
  },
  
  emptyContainer: {
    padding: 40,
    alignItems: "center"
  },
  
  emptyText: {
    fontSize: 16,
    color: stylesConstants.colors.gray25,
    textAlign: "center"
  }
});
