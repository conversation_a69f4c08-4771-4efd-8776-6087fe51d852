import {StyleSheet} from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20,
    paddingTop: 60
  },
  progressContainer: {
    marginBottom: 32
  },
  progressTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  progressStep: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 16,
    fontFamily: "Ubuntu"
  },
  progressBar: {
    height: 4,
    backgroundColor: "#333333",
    borderRadius: 2
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#00D4AA",
    borderRadius: 2
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  subtitle: {
    fontSize: 16,
    color: "#CCCCCC",
    lineHeight: 24,
    fontFamily: "Ubuntu"
  },
  summaryContainer: {
    backgroundColor: "#2A2A2A",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 12,
    fontFamily: "Ubuntu"
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  summaryLabel: {
    fontSize: 14,
    color: "#CCCCCC",
    fontFamily: "Ubuntu"
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },
  methodsContainer: {
    flex: 1,
    marginBottom: 24
  },
  methodCard: {
    backgroundColor: "#2A2A2A",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    borderWidth: 2,
    borderColor: "transparent"
  },
  methodCardSelected: {
    borderColor: "#00D4AA"
  },
  greenSection: {
    backgroundColor: "#00D4AA",
    height: 40,
    justifyContent: "center",
    alignItems: "flex-end",
    paddingRight: 16
  },
  checkIcon: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  methodContent: {
    padding: 16
  },
  methodContentSelected: {
    paddingTop: 16
  },
  methodHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8
  },
  methodIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#333333",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  pixIcon: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#00D4AA",
    fontFamily: "Ubuntu"
  },
  boletoIcon: {
    fontSize: 20
  },
  methodInfo: {
    flex: 1
  },
  methodName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 4,
    fontFamily: "Ubuntu"
  },
  methodDescription: {
    fontSize: 14,
    color: "#CCCCCC",
    fontFamily: "Ubuntu"
  },
  processingTime: {
    fontSize: 12,
    color: "#999999",
    fontFamily: "Ubuntu"
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
    paddingBottom: 20
  },
  backButton: {
    flex: 1
  },
  nextButton: {
    flex: 1
  }
});
