import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import CheckIcon from "@/components/icons/check-icon";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import CreditCardShieldIcon from "@/components/icons/credit-card-shield-icon";
import UserEditIcon from "@/components/icons/user-edit-icon";
import PaymentInputField from "@/components/payment-input-field";
import {PaymentType} from "@/models/api/payments.models";
import {
  useCreateSubscription,
  CreateSubscriptionRequest
} from "@/hooks/api/use-subscriptions";
import {useCurrentUser} from "@/hooks/api/use-auth";
import {usePlansPublic} from "@/hooks/api";

interface PaymentMethodOption {
  type: PaymentType;
  name: string;
  description: string;
  icon: React.ReactNode;
  processingTime: string;
}

const PlanPaymentSelection: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [selectedMethod, setSelectedMethod] = useState<PaymentType | null>(
    null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [formData, setFormData] = useState({
    holderName: "",
    number: "",
    expiryMonth: "",
    expiryYear: "",
    ccv: "",
    cpfCnpj: "",
    postalCode: "",
    addressNumber: "",
    addressComplement: "",
    phoneNumber: ""
  });

  const {data: currentUser} = useCurrentUser();
  // Fetch plans list and filter to ensure accurate pricing
  const planId = params.selectedPlanId
    ? parseInt(params.selectedPlanId as string)
    : 0;
  const {data: plansResponse} = usePlansPublic({page: 1, pageSize: 50});
  const monthlyCents = React.useMemo(() => {
    const plan = plansResponse?.data?.find((p) => p.id === planId);
    return typeof plan?.value === "number" ? plan.value : 0;
  }, [plansResponse?.data, planId]);

  const createSubscriptionMutation = useCreateSubscription();

  const paymentMethods: PaymentMethodOption[] = [
    {
      type: PaymentType.CreditCard,
      name: t("planPaymentSelection.creditCard.name", "Cartão de Crédito"),
      description: t(
        "planPaymentSelection.creditCard.description",
        "Pagamento recorrente mensal"
      ),
      icon: <CreditCardIcon width={24} height={24} />,
      processingTime: t(
        "planPaymentSelection.creditCard.processingTime",
        "Instantâneo"
      )
    }
  ];

  const handleMethodSelect = useCallback((method: PaymentType) => {
    setSelectedMethod(method);
  }, []);

  const handleNext = useCallback(async () => {
    if (isProcessing) return;
    if (selectedMethod === null) {
      Alert.alert(
        t("planPaymentSelection.error.title", "Erro de validação"),
        t(
          "planPaymentSelection.error.selectMethod",
          "Você deve selecionar um método de pagamento para continuar"
        )
      );
      return;
    }

    if (!currentUser) {
      Alert.alert(
        t("planPaymentSelection.error.title", "Erro"),
        t("planPaymentSelection.error.userNotFound", "Usuário não encontrado")
      );
      return;
    }

    setIsProcessing(true);

    try {
      // Determinar o dia de vencimento escolhido (10, 20 ou 30)
      const parsedDay = parseInt((params.selectedDueDay as string) || "");
      const selectedDay = [10, 20, 30].includes(parsedDay) ? parsedDay : 10;

      // Criar assinatura/pagamento do plano via /api/app/subscriptions
      const subscriptionRequest: CreateSubscriptionRequest = {
        entity: 1, // tipo 1 = plano
        entityId: parseInt(params.selectedPlanId as string) || 0, // ID do plano escolhido
        paymentType: selectedMethod,
        creditCard: {
          holderName: formData.holderName,
          number: formData.number.replace(/\s/g, ""),
          expiryMonth: formData.expiryMonth,
          expiryYear: formData.expiryYear,
          ccv: formData.ccv,
          name: formData.holderName,
          cpfCnpj: formData.cpfCnpj,
          postalCode: formData.postalCode,
          addressNumber: formData.addressNumber,
          addressComplement: formData.addressComplement,
          phoneNumber: formData.phoneNumber
        },
        day: selectedDay
      };

      console.log(
        "🚀 [PLAN-PAYMENT] Criando pagamento do plano:",
        subscriptionRequest
      );

      const response = await createSubscriptionMutation.mutateAsync(
        subscriptionRequest
      );

      console.log("✅ [PLAN-PAYMENT] Pagamento do plano criado:", response);

      // Navegar para tela de sucesso
      router.push({
        pathname: "/(registration)/registration-success",
        params: {
          ...params,
          planPaymentId: response.data.id,
          planPaymentMethod:
            selectedMethod === PaymentType.CreditCard
              ? "Cartão de Crédito"
              : "Outro"
        }
      });
    } catch (error) {
      console.error(
        "❌ [PLAN-PAYMENT] Erro ao criar pagamento do plano:",
        error
      );
      Alert.alert(
        t("planPaymentSelection.error.title", "Erro"),
        t(
          "planPaymentSelection.error.createPayment",
          "Erro ao processar pagamento do plano. Tente novamente."
        )
      );
    } finally {
      setIsProcessing(false);
    }
  }, [
    selectedMethod,
    currentUser,
    createSubscriptionMutation,
    formData,
    params,
    router,
    t
  ]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  // Função para lidar com mudanças nos inputs (igual à tela do título)
  const handleInputChange = useCallback(
    (field: keyof typeof formData) => (value: string) => {
      let processedValue = value;

      // Aplicar máscaras
      if (field === "number") {
        processedValue = value
          .replace(/\D/g, "")
          .slice(0, 16)
          .replace(/(\d{4})(?=\d)/g, "$1 ");
      } else if (field === "expiryMonth" || field === "expiryYear") {
        processedValue = value
          .replace(/\D/g, "")
          .slice(0, field === "expiryMonth" ? 2 : 4);
      } else if (field === "ccv") {
        processedValue = value.replace(/\D/g, "").slice(0, 4);
      } else if (field === "cpfCnpj") {
        processedValue = value.replace(/\D/g, "").slice(0, 14);
      } else if (field === "postalCode") {
        processedValue = value.replace(/\D/g, "").slice(0, 8);
      } else if (field === "phoneNumber") {
        processedValue = value.replace(/\D/g, "").slice(0, 11);
      }

      setFormData((prev) => ({...prev, [field]: processedValue}));
    },
    []
  );

  const renderPaymentMethod = (method: PaymentMethodOption) => {
    const isSelected = selectedMethod === method.type;

    return (
      <TouchableOpacity
        key={method.type}
        style={{
          backgroundColor: "#2A2A2A",
          borderRadius: 12,
          marginBottom: 16,
          overflow: "hidden",
          borderWidth: 2,
          borderColor: isSelected ? "#00D4AA" : "transparent"
        }}
        onPress={() => handleMethodSelect(method.type)}
        activeOpacity={0.8}
        disabled={isSelected}
      >
        {isSelected && (
          <View
            style={{
              backgroundColor: "#00D4AA",
              height: 40,
              justifyContent: "center",
              alignItems: "flex-end",
              paddingRight: 16
            }}
          >
            <View
              style={{
                width: 24,
                height: 24,
                justifyContent: "center",
                alignItems: "center"
              }}
            >
              <CheckIcon width={24} height={24} replaceColor="#FFFFFF" />
            </View>
          </View>
        )}

        <View style={{padding: 16, paddingTop: isSelected ? 16 : 16}}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 8
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#333333",
                borderRadius: 8,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12
              }}
            >
              {method.icon}
            </View>
            <View style={{flex: 1}}>
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "bold",
                  color: "#FFFFFF",
                  marginBottom: 4,
                  fontFamily: "Ubuntu"
                }}
              >
                {method.name}
              </Text>
              <Text
                style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
              >
                {method.description}
              </Text>
            </View>
          </View>
          <Text style={{fontSize: 12, color: "#999999", fontFamily: "Ubuntu"}}>
            {t(
              "planPaymentSelection.processingTime",
              "Processamento: {{time}}",
              {time: method.processingTime}
            )}
          </Text>

          {/* Formulário de cartão para assinatura */}
          {false && (
            <View
              style={{
                backgroundColor: "#2A2A2A",
                borderRadius: 12,
                padding: 16,
                marginBottom: 24
              }}
            >
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "bold",
                  color: "#FFFFFF",
                  marginBottom: 12,
                  fontFamily: "Ubuntu"
                }}
              >
                {t(
                  "planPaymentSelection.card.title",
                  "Dados do cartão (plano)"
                )}
              </Text>

              <PaymentInputField
                placeholder={t(
                  "creditCardPayment.holderName",
                  "Nome do titular"
                )}
                value={formData.holderName}
                onChangeText={(v) =>
                  setFormData((prev) => ({...prev, holderName: v}))
                }
                icon={<UserEditIcon />}
                style={{marginBottom: 16}}
              />

              <PaymentInputField
                placeholder={t(
                  "creditCardPayment.cardNumber",
                  "Número do cartão"
                )}
                value={formData.number}
                onChangeText={(v) =>
                  setFormData((prev) => ({
                    ...prev,
                    number: v
                      .replace(/\D/g, "")
                      .slice(0, 16)
                      .replace(/(\d{4})(?=\d)/g, "$1 ")
                  }))
                }
                icon={<CreditCardIcon />}
                inputMode="numeric"
                maxLength={19}
                style={{marginBottom: 16}}
              />

              <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
                <View style={{flex: 1}}>
                  <PaymentInputField
                    placeholder={t("creditCardPayment.expiryMonth", "Mês")}
                    value={formData.expiryMonth}
                    onChangeText={(v) =>
                      setFormData((prev) => ({
                        ...prev,
                        expiryMonth: v.replace(/\D/g, "").slice(0, 2)
                      }))
                    }
                    icon={<CreditCardIcon />}
                    inputMode="numeric"
                    maxLength={2}
                  />
                </View>
                <View style={{flex: 1}}>
                  <PaymentInputField
                    placeholder={t("creditCardPayment.expiryYear", "Ano")}
                    value={formData.expiryYear}
                    onChangeText={(v) =>
                      setFormData((prev) => ({
                        ...prev,
                        expiryYear: v.replace(/\D/g, "").slice(0, 4)
                      }))
                    }
                    icon={<CreditCardIcon />}
                    inputMode="numeric"
                    maxLength={4}
                  />
                </View>
                <View style={{flex: 1}}>
                  <PaymentInputField
                    placeholder={t("creditCardPayment.ccv", "CVV")}
                    value={formData.ccv}
                    onChangeText={(v) =>
                      setFormData((prev) => ({
                        ...prev,
                        ccv: v.replace(/\D/g, "").slice(0, 4)
                      }))
                    }
                    icon={<CreditCardShieldIcon />}
                    inputMode="numeric"
                    maxLength={4}
                  />
                </View>
              </View>

              <PaymentInputField
                placeholder={t("creditCardPayment.cpfCnpj", "CPF/CNPJ")}
                value={formData.cpfCnpj}
                onChangeText={(v) =>
                  setFormData((prev) => ({
                    ...prev,
                    cpfCnpj: v.replace(/\D/g, "").slice(0, 14)
                  }))
                }
                inputMode="numeric"
                style={{marginBottom: 16}}
              />

              <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
                <View style={{flex: 2}}>
                  <PaymentInputField
                    placeholder={t("creditCardPayment.postalCode", "CEP")}
                    value={formData.postalCode}
                    onChangeText={(v) =>
                      setFormData((prev) => ({
                        ...prev,
                        postalCode: v.replace(/\D/g, "").slice(0, 8)
                      }))
                    }
                    inputMode="numeric"
                    maxLength={8}
                  />
                </View>
                <View style={{flex: 1}}>
                  <PaymentInputField
                    placeholder={t("creditCardPayment.addressNumber", "Número")}
                    value={formData.addressNumber}
                    onChangeText={(v) =>
                      setFormData((prev) => ({...prev, addressNumber: v}))
                    }
                  />
                </View>
              </View>

              <PaymentInputField
                placeholder={t(
                  "creditCardPayment.addressComplement",
                  "Complemento (opcional)"
                )}
                value={formData.addressComplement}
                onChangeText={(v) =>
                  setFormData((prev) => ({...prev, addressComplement: v}))
                }
                style={{marginBottom: 16}}
              />

              <PaymentInputField
                placeholder={t("creditCardPayment.phoneNumber", "Telefone")}
                value={formData.phoneNumber}
                onChangeText={(v) =>
                  setFormData((prev) => ({
                    ...prev,
                    phoneNumber: v.replace(/\D/g, "").slice(0, 11)
                  }))
                }
                inputMode="numeric"
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (isProcessing) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A"
          }}
        >
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text
            style={{
              marginTop: 16,
              fontSize: 16,
              color: "#FFFFFF",
              textAlign: "center"
            }}
          >
            {t(
              "planPaymentSelection.processing",
              "Processando pagamento do plano..."
            )}
          </Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <View
        style={{
          flex: 1,
          backgroundColor: "#111828",
          paddingHorizontal: 20,
          paddingTop: 60
        }}
      >
        <BackButton />

        <View style={{marginBottom: 32}}>
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t("planPaymentSelection.createAccount", "Criar conta")}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("planPaymentSelection.stepProgress", "6 / 7 Pagamento do plano")}
          </Text>
          <View
            style={{height: 4, backgroundColor: "#333333", borderRadius: 2}}
          >
            <View
              style={{
                height: "100%",
                backgroundColor: "#00D4AA",
                borderRadius: 2,
                width: "85%"
              }}
            />
          </View>
        </View>

        <View style={{marginBottom: 24}}>
          <Text
            style={{
              fontSize: 20,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t("planPaymentSelection.title", "Pagamento do Plano")}
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: "#CCCCCC",
              lineHeight: 24,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "planPaymentSelection.description",
              "Agora vamos configurar o pagamento da sua assinatura mensal."
            )}
          </Text>
        </View>

        <View
          style={{
            backgroundColor: "#2A2A2A",
            borderRadius: 12,
            padding: 16,
            marginBottom: 24
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 12,
              fontFamily: "Ubuntu"
            }}
          >
            {t("planPaymentSelection.summary", "Resumo da assinatura")}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text
              style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
            >
              {t("planPaymentSelection.plan", "Plano:")}
            </Text>
            <Text
              style={{
                fontSize: 14,
                fontWeight: "bold",
                color: "#FFFFFF",
                fontFamily: "Ubuntu"
              }}
            >
              {params.selectedPlanName ||
                t("planPaymentSelection.notSpecified", "Plano selecionado")}
            </Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text
              style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
            >
              {t("planPaymentSelection.frequency", "Frequência:")}
            </Text>
            <Text
              style={{
                fontSize: 14,
                fontWeight: "bold",
                color: "#FFFFFF",
                fontFamily: "Ubuntu"
              }}
            >
              {t("planPaymentSelection.monthly", "Mensal")}
            </Text>
          </View>
          <View style={{flexDirection: "row", justifyContent: "space-between"}}>
            <Text
              style={{fontSize: 14, color: "#CCCCCC", fontFamily: "Ubuntu"}}
            >
              {t("planPaymentSelection.monthlyValue", "Valor mensal:")}
            </Text>
            <Text
              style={{
                fontSize: 16,
                fontWeight: "bold",
                color: "#00D4AA",
                fontFamily: "Ubuntu"
              }}
            >
              {(() => {
                const value = (monthlyCents || 0) / 100;
                return `R$ ${value.toFixed(2).replace(".", ",")}`;
              })()}
            </Text>
          </View>
        </View>

        <View style={{marginBottom: 24}}>
          {paymentMethods.map(renderPaymentMethod)}
        </View>

        {/* Formulário de cartão fora da lista, para evitar remount durante digitação */}
        {selectedMethod === PaymentType.CreditCard && (
          <View
            style={{
              backgroundColor: "#2A2A2A",
              borderRadius: 12,
              padding: 16,
              marginBottom: 24
            }}
          >
            <Text
              style={{
                fontSize: 16,
                fontWeight: "bold",
                color: "#FFFFFF",
                marginBottom: 12,
                fontFamily: "Ubuntu"
              }}
            >
              {t("planPaymentSelection.card.title", "Dados do cartão (plano)")}
            </Text>

            <PaymentInputField
              placeholder={t("creditCardPayment.holderName", "Nome do titular")}
              value={formData.holderName}
              onChangeText={handleInputChange("holderName")}
              icon={<UserEditIcon />}
              style={{marginBottom: 16}}
            />

            <PaymentInputField
              placeholder={t(
                "creditCardPayment.cardNumber",
                "Número do cartão"
              )}
              value={formData.number}
              onChangeText={handleInputChange("number")}
              icon={<CreditCardIcon />}
              inputMode="numeric"
              maxLength={19}
              style={{marginBottom: 16}}
            />

            <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
              <View style={{flex: 1}}>
                <PaymentInputField
                  placeholder={t("creditCardPayment.expiryMonth", "Mês")}
                  value={formData.expiryMonth}
                  onChangeText={handleInputChange("expiryMonth")}
                  icon={<CreditCardIcon />}
                  inputMode="numeric"
                  maxLength={2}
                />
              </View>
              <View style={{flex: 1}}>
                <PaymentInputField
                  placeholder={t("creditCardPayment.expiryYear", "Ano")}
                  value={formData.expiryYear}
                  onChangeText={handleInputChange("expiryYear")}
                  icon={<CreditCardIcon />}
                  inputMode="numeric"
                  maxLength={4}
                />
              </View>
              <View style={{flex: 1}}>
                <PaymentInputField
                  placeholder={t("creditCardPayment.ccv", "CVV")}
                  value={formData.ccv}
                  onChangeText={handleInputChange("ccv")}
                  icon={<CreditCardShieldIcon />}
                  inputMode="numeric"
                  maxLength={4}
                />
              </View>
            </View>

            <PaymentInputField
              placeholder={t("creditCardPayment.cpfCnpj", "CPF/CNPJ")}
              value={formData.cpfCnpj}
              onChangeText={handleInputChange("cpfCnpj")}
              inputMode="numeric"
              style={{marginBottom: 16}}
            />

            <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
              <View style={{flex: 2}}>
                <PaymentInputField
                  placeholder={t("creditCardPayment.postalCode", "CEP")}
                  value={formData.postalCode}
                  onChangeText={handleInputChange("postalCode")}
                  inputMode="numeric"
                  maxLength={8}
                />
              </View>
              <View style={{flex: 1}}>
                <PaymentInputField
                  placeholder={t("creditCardPayment.addressNumber", "Número")}
                  value={formData.addressNumber}
                  onChangeText={handleInputChange("addressNumber")}
                />
              </View>
            </View>

            <PaymentInputField
              placeholder={t(
                "creditCardPayment.addressComplement",
                "Complemento (opcional)"
              )}
              value={formData.addressComplement}
              onChangeText={handleInputChange("addressComplement")}
              style={{marginBottom: 16}}
            />

            <PaymentInputField
              placeholder={t("creditCardPayment.phoneNumber", "Telefone")}
              value={formData.phoneNumber}
              onChangeText={handleInputChange("phoneNumber")}
              inputMode="numeric"
            />
          </View>
        )}

        <View style={{flexDirection: "row", gap: 12, paddingBottom: 20}}>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("planPaymentSelection.finalize", "Finalizar")}
              onPress={handleNext}
              loading={isProcessing}
              disabled={
                selectedMethod === null ||
                isProcessing ||
                (selectedMethod === PaymentType.CreditCard &&
                  (!formData.holderName ||
                    !formData.number ||
                    !formData.expiryMonth ||
                    !formData.expiryYear ||
                    !formData.ccv ||
                    !formData.cpfCnpj ||
                    !formData.postalCode ||
                    !formData.addressNumber ||
                    !formData.phoneNumber))
              }
            />
          </View>
        </View>
      </View>
    </Screen>
  );
};

export default PlanPaymentSelection;
