# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 40ms
  generate-prefab-packages
    [gap of 153ms]
    exec-prefab 4166ms
    [gap of 88ms]
  generate-prefab-packages completed in 4407ms
  execute-generate-process
    exec-configure 3667ms
    [gap of 286ms]
  execute-generate-process completed in 3958ms
  [gap of 22ms]
  remove-unexpected-so-files 18ms
  [gap of 78ms]
generate_cxx_metadata completed in 8550ms

