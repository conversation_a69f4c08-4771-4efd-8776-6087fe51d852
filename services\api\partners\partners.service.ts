/**
 * Serviço de Parceiros para ClubM
 * Implementa operações completas para parceiros e benefícios
 */

import {apiClient} from "../base/api-client";
import {PaginationResponse} from "@/models/api/common.models";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {
  Partner,
  PartnerListParams,
  PartnerListResponse,
  PartnerDetailsResponse,
  CreatePartnerRequest,
  UpdatePartnerRequest
} from "@/models/api/partners.models";

export class PartnersService {
  private static readonly BASE_PATH = "/api/app/partners";

  /**
   * Buscar lista de parceiros com filtros e paginação
   */
  static async getPartners(
    params?: PartnerListParams
  ): Promise<PartnerListResponse> {
    try {
      ApiLogger.info("Buscando lista de parceiros", params);

      const response = await firstValueFrom(
        apiClient.get<PartnerListResponse>(this.BASE_PATH, {
          params: {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || "",
            Status: params?.status || 1
          }
        })
      );

      ApiLogger.info(`Encontrados ${response.data.length} parceiros`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar parceiros", error as Error);
      throw error;
    }
  }

  /**
   * Buscar parceiro por ID com detalhes completos
   * Como a API não tem endpoint específico para um parceiro, busca na lista
   */
  static async getPartner(partnerId: string | number): Promise<Partner> {
    try {
      ApiLogger.info("Buscando parceiro por ID", {partnerId});

      // Busca todos os parceiros e filtra pelo ID
      const response = await firstValueFrom(
        apiClient.get<PartnerListResponse>(this.BASE_PATH, {
          params: {
            Page: 1,
            PageSize: 100 // Busca um número grande para garantir que encontre o parceiro
          }
        })
      );

      const partner = response.data.find(p => p.id.toString() === partnerId.toString());

      if (!partner) {
        throw new Error(`Parceiro com ID ${partnerId} não encontrado`);
      }

      ApiLogger.info("Parceiro encontrado", {
        partnerId,
        name: partner.name
      });

      return partner;
    } catch (error) {
      ApiLogger.error("Erro ao buscar parceiro", error as Error, {partnerId});
      throw error;
    }
  }

  /**
   * Criar novo parceiro (admin)
   */
  static async createPartner(partner: CreatePartnerRequest): Promise<Partner> {
    try {
      ApiLogger.info("Criando novo parceiro", {name: partner.name});

      const response = await firstValueFrom(
        apiClient.post<Partner>(this.BASE_PATH, partner)
      );

      ApiLogger.info("Parceiro criado com sucesso", {
        partnerId: response.id,
        name: response.name
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao criar parceiro", error as Error);
      throw error;
    }
  }

  /**
   * Atualizar parceiro existente (admin)
   */
  static async updatePartner(
    partnerId: string | number,
    partner: UpdatePartnerRequest
  ): Promise<Partner> {
    try {
      ApiLogger.info("Atualizando parceiro", {partnerId});

      const response = await firstValueFrom(
        apiClient.put<Partner>(`${this.BASE_PATH}/${partnerId}`, partner)
      );

      ApiLogger.info("Parceiro atualizado com sucesso", {
        partnerId,
        name: response.name
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao atualizar parceiro", error as Error, {
        partnerId
      });
      throw error;
    }
  }

  /**
   * Deletar parceiro (admin)
   */
  static async deletePartner(partnerId: string | number): Promise<void> {
    try {
      ApiLogger.info("Deletando parceiro", {partnerId});

      await firstValueFrom(
        apiClient.delete<void>(`${this.BASE_PATH}/${partnerId}`)
      );

      ApiLogger.info("Parceiro deletado com sucesso", {partnerId});
    } catch (error) {
      ApiLogger.error("Erro ao deletar parceiro", error as Error, {partnerId});
      throw error;
    }
  }

  /**
   * Buscar parceiros recomendados
   */
  static async getRecommendedPartners(
    params?: Omit<PartnerListParams, "search">
  ): Promise<PartnerListResponse> {
    try {
      ApiLogger.info("Buscando parceiros recomendados", params);

      const response = await firstValueFrom(
        apiClient.get<PartnerListResponse>(`${this.BASE_PATH}/recommended`, {
          Page: params?.page || 1,
          PageSize: params?.pageSize || 10,
          Status: params?.status || 1
        })
      );

      ApiLogger.info(
        `Encontrados ${response.data.length} parceiros recomendados`
      );
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar parceiros recomendados", error as Error);
      throw error;
    }
  }

  /**
   * Buscar parceiros por categoria
   */
  static async getPartnersByCategory(
    category: string,
    params?: PartnerListParams
  ): Promise<PartnerListResponse> {
    try {
      ApiLogger.info("Buscando parceiros por categoria", {category, ...params});

      const response = await firstValueFrom(
        apiClient.get<PartnerListResponse>(
          `${this.BASE_PATH}/category/${category}`,
          {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || "",
            Status: params?.status || 1
          }
        )
      );

      ApiLogger.info(
        `Encontrados ${response.data.length} parceiros na categoria ${category}`
      );
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar parceiros por categoria",
        error as Error,
        {category}
      );
      throw error;
    }
  }

  /**
   * Buscar estatísticas de parceiros (admin)
   * NOTA: Endpoint /stats não existe na API, retornando dados mock
   */
  static async getPartnersStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    categories: Array<{name: string; count: number}>;
  }> {
    try {
      ApiLogger.info("Calculando estatísticas de parceiros (dados mock)");

      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Retornar dados mock já que o endpoint não existe
      const mockStats = {
        total: 0,
        active: 0,
        inactive: 0,
        categories: []
      };

      ApiLogger.warn(
        "Endpoint /api/partners/stats não existe na API, retornando dados mock"
      );
      console.log(
        "⚠️ API Status: GET /api/partners/stats - 404 Not Found (Endpoint não implementado)"
      );

      return mockStats;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar estatísticas de parceiros",
        error as Error
      );
      throw error;
    }
  }
}
