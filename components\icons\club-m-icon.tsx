import React from "react";
import {Image, View, StyleSheet} from "react-native";

interface ClubMIconProps {
  size?: number;
}

const ClubMIcon: React.FC<ClubMIconProps> = ({size = 40}) => {
  return (
    <View style={[styles.container, {width: size, height: size}]}>
      <Image
        source={require("@/assets/images/clubm-icon.png")}
        style={[styles.image, {width: size, height: size}]}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center"
  },
  image: {
    borderRadius: 20 // Half of default size (40/2) for circular appearance
  }
});

export default ClubMIcon;
