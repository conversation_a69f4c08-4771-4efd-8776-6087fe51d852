{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-71:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "41,42,43,44,45,46,47,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3549,3641,3741,3835,3931,4024,4117,15500", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3636,3736,3830,3926,4019,4112,4213,15596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4557,4661,4795,4915,5021,5153,5273,5378,5599,5733,5834,5967,6086,6206,6326,6386,6445", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4656,4790,4910,5016,5148,5268,5373,5472,5728,5829,5962,6081,6201,6321,6381,6440,6511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5477", "endColumns": "121", "endOffsets": "5594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "71,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6618,7267,7362,7463", "endColumns": "92,94,100,94", "endOffsets": "6706,7357,7458,7553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,197,265,331,407,473,540,612,687,763,839,906,981,1056,1128,1205,1281,1353,1423,1492,1570,1638,1709,1777", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "118,192,260,326,402,468,535,607,682,758,834,901,976,1051,1123,1200,1276,1348,1418,1487,1565,1633,1704,1772,1843"}, "to": {"startLines": "33,51,95,102,103,105,119,120,121,171,172,173,175,180,181,182,183,184,185,186,187,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2889,4483,9005,9392,9458,9592,10515,10582,10654,14210,14286,14362,14535,14911,14986,15058,15135,15211,15283,15353,15422,15601,15669,15740,15808", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "2952,4552,9068,9453,9529,9653,10577,10649,10724,14281,14357,14424,14605,14981,15053,15130,15206,15278,15348,15417,15495,15664,15735,15803,15874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,151,208,263,313,364,413,472", "endColumns": "53,41,56,54,49,50,48,58,51", "endOffsets": "104,146,203,258,308,359,408,467,519"}, "to": {"startLines": "72,96,97,98,99,100,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6711,9073,9115,9172,9227,9277,14050,14099,14158", "endColumns": "53,41,56,54,49,50,48,58,51", "endOffsets": "6760,9110,9167,9222,9272,9323,14094,14153,14205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,74,75,76,101,104,106,107,108,109,110,111,112,113,114,115,116,117,118,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3186,3253,3317,3386,3467,4218,4303,4407,6850,6903,6966,9328,9534,9658,9739,9800,9864,9919,9978,10035,10089,10182,10238,10295,10349,10415,10729,10805,10876,10955,11028,11109,11231,11293,11355,11456,11535,11610,11663,11714,11780,11850,11920,11991,12061,12125,12196,12264,12327,12418,12497,12560,12640,12722,12794,12865,12937,12985,13057,13121,13196,13273,13335,13399,13462,13529,13615,13701,13782,13865,13922,13977,14689,14767,14840", "endLines": "5,36,37,38,39,40,48,49,50,74,75,76,101,104,106,107,108,109,110,111,112,113,114,115,116,117,118,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,177,178,179", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,3248,3312,3381,3462,3544,4298,4402,4478,6898,6961,7045,9387,9587,9734,9795,9859,9914,9973,10030,10084,10177,10233,10290,10344,10410,10510,10800,10871,10950,11023,11104,11226,11288,11350,11451,11530,11605,11658,11709,11775,11845,11915,11986,12056,12120,12191,12259,12322,12413,12492,12555,12635,12717,12789,12860,12932,12980,13052,13116,13191,13268,13330,13394,13457,13524,13610,13696,13777,13860,13917,13972,14045,14762,14835,14906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,14610", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,14684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,284,386,471,591,688,789,892,1005,1119,1238,1344,1460,1556,1684,1789,1908,2027,2135,2241,2327,2436,2514,2617,2703,2812", "endColumns": "125,102,101,84,119,96,100,102,112,113,118,105,115,95,127,104,118,118,107,105,85,108,77,102,85,108,88", "endOffsets": "176,279,381,466,586,683,784,887,1000,1114,1233,1339,1455,1551,1679,1784,1903,2022,2130,2236,2322,2431,2509,2612,2698,2807,2896"}, "to": {"startLines": "34,35,70,73,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,174,193,194,195,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2957,3083,6516,6765,7050,7170,7558,7659,7762,7875,7989,8108,8214,8330,8426,8554,8659,8778,8897,14429,15879,15965,16074,16152,16255,16341,16450", "endColumns": "125,102,101,84,119,96,100,102,112,113,118,105,115,95,127,104,118,118,107,105,85,108,77,102,85,108,88", "endOffsets": "3078,3181,6613,6845,7165,7262,7654,7757,7870,7984,8103,8209,8325,8421,8549,8654,8773,8892,9000,14530,15960,16069,16147,16250,16336,16445,16534"}}]}]}