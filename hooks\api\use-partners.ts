import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery
} from "@tanstack/react-query";
import {
  Partner,
  PartnerListParams,
  PartnerListResponse
} from "@/models/api/partners.models";
import {
  Benefit,
  BenefitListParams,
  BenefitListResponse,
  BenefitInteractionRequest
} from "@/models/api/benefits.models";
import { PartnersService } from "@/services/api/partners/partners.service";
import { BenefitsService } from "@/services/api/benefits/benefits.service";
import {useMemo} from "react";

// Hooks para integração com a API de parceiros e benefícios

/**
 * Hook for fetching a list of partners with pagination and search
 */
export const usePartners = (params: PartnerListParams) => {
  return useQuery<PartnerListResponse>({
    queryKey: ["partners", params],
    queryFn: () => PartnersService.getPartners(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
};

/**
 * Hook for fetching partners with infinite scroll
 */
export const usePartnersInfinite = (
  params: Omit<PartnerListParams, "page">
) => {
  return useInfiniteQuery({
    queryKey: ["partners", "infinite", params],
    queryFn: ({pageParam = 1}) =>
      PartnersService.getPartners({...params, page: pageParam}),
    getNextPageParam: (lastPage) => {
      return lastPage.hasNextPage ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching a single partner by ID
 */
export const usePartner = (partnerId: string | number) => {
  return useQuery<Partner>({
    queryKey: ["partner", partnerId],
    queryFn: () => PartnersService.getPartner(partnerId),
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching benefits for a specific partner
 */
export const usePartnerBenefits = (
  partnerId: string | number,
  params: BenefitListParams
) => {
  return useQuery<BenefitListResponse>({
    queryKey: ["partner", partnerId, "benefits", params],
    queryFn: () => BenefitsService.getPartnerBenefits(partnerId, params),
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching partner benefits with infinite scroll
 */
export const usePartnerBenefitsInfinite = (
  partnerId: string | number,
  params: Omit<BenefitListParams, "page">
) => {
  return useInfiniteQuery({
    queryKey: ["partner", partnerId, "benefits", "infinite", params],
    queryFn: ({pageParam = 1}) =>
      BenefitsService.getPartnerBenefits(partnerId, {
        ...params,
        page: pageParam
      }),
    getNextPageParam: (lastPage) => {
      return lastPage.hasNextPage ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching a single benefit by ID
 */
export const useBenefit = (partnerId: string | number, benefitId: number) => {
  return useQuery<Benefit>({
    queryKey: ["partner", partnerId, "benefit", benefitId],
    queryFn: () => BenefitsService.getBenefit(partnerId, benefitId),
    enabled: !!partnerId && !!benefitId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for benefit interactions (view, click, redeem)
 */
export const useBenefitInteraction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: BenefitInteractionRequest) =>
      BenefitsService.interactWithBenefit(
        request.partnerId,
        request.benefitId,
        request.interactionType
      ),
    onSuccess: (_, variables) => {
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["partner", variables.partnerId, "benefits"]
      });
      queryClient.invalidateQueries({
        queryKey: [
          "partner",
          variables.partnerId,
          "benefit",
          variables.benefitId
        ]
      });
    }
  });
};

/**
 * Hook for creating a new partner
 */
export const useCreatePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (partner: Omit<Partner, "id" | "createdAt" | "updatedAt">) =>
      PartnersService.createPartner(partner),
    onSuccess: () => {
      // Invalidate partners list to refresh data
      queryClient.invalidateQueries({queryKey: ["partners"]});
    }
  });
};

/**
 * Hook for updating a partner
 */
export const useUpdatePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partnerId,
      partner
    }: {
      partnerId: string | number;
      partner: Partial<Partner>;
    }) => PartnersService.updatePartner(partnerId, partner),
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({queryKey: ["partners"]});
      queryClient.invalidateQueries({
        queryKey: ["partner", variables.partnerId]
      });
    }
  });
};

/**
 * Hook for deleting a partner
 */
export const useDeletePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (partnerId: string | number) =>
      PartnersService.deletePartner(partnerId),
    onSuccess: (_, partnerId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({queryKey: ["partner", partnerId]});
      queryClient.invalidateQueries({queryKey: ["partners"]});
    }
  });
};

/**
 * Utility hook to get flattened partners from infinite query
 */
export const useFlattenedPartners = (
  infiniteQuery: ReturnType<typeof usePartnersInfinite>
) => {
  return useMemo(() => {
    return infiniteQuery.data?.pages.flatMap((page) => page.data) ?? [];
  }, [infiniteQuery.data]);
};

/**
 * Utility hook to get flattened benefits from infinite query
 */
export const useFlattenedBenefits = (
  infiniteQuery: ReturnType<typeof usePartnerBenefitsInfinite>
) => {
  return useMemo(() => {
    return infiniteQuery.data?.pages.flatMap((page) => page.data) ?? [];
  }, [infiniteQuery.data]);
};
