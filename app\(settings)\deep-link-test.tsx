/**
 * Deep Link Test Screen
 * Development screen to test deep linking functionality
 */

import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, Share } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Linking } from 'react-native';
import { DeepLinkTester } from '@/components/deep-link/deep-link-tester';
import { useDeepLink } from '@/contexts/DeepLinkContext';
import { useDeepLinkAuth } from '@/hooks/use-deep-link-auth';
import { useAuth } from '@/contexts/AuthContext';
import styles from '@/styles/settings/deep-link-test.style';

export default function DeepLinkTestScreen() {
  const router = useRouter();
  const [showTester, setShowTester] = useState(false);
  const { pendingDeepLink, lastProcessedUrl } = useDeepLink();
  const { hasPendingDeepLink, needsBiometricAuth, needsLogin } = useDeepLinkAuth();
  const { isAuthenticated, requiresBiometricAuth, user } = useAuth();

  const testUrls = [
    { label: 'Product Page', url: 'clubm://products/123' },
    { label: 'Event Sale', url: 'clubm://events/456' },
    { label: 'Business Center', url: 'clubm://opportunities' },
    { label: 'Specific Opportunity', url: 'clubm://opportunities/789' },
    { label: 'User Profile', url: 'clubm://profile/101' },
    { label: 'Chat', url: 'clubm://chat/202' },
    { label: 'Wallet', url: 'clubm://wallet' },
    { label: 'Magazine', url: 'clubm://magazine/303' },
    { label: 'Referral', url: 'clubm://referral' },
    { label: 'Schedule', url: 'clubm://schedule' },
    { label: 'Home', url: 'clubm://home' },
  ];

  const universalLinks = [
    { label: 'Universal Link - Product', url: 'https://clubm.app/products/123' },
    { label: 'Universal Link - Event', url: 'https://clubm.app/events/456' },
    { label: 'Universal Link - Opportunity', url: 'https://app.clubm.com/opportunities/789' },
  ];

  const handleTestUrl = async (url: string) => {
    try {
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Cannot open URL');
      }
    } catch (error) {
      Alert.alert('Error', `Failed to open URL: ${error}`);
    }
  };

  const handleShareUrl = async (url: string) => {
    try {
      await Share.share({
        message: `Test this deep link: ${url}`,
        url: url,
      });
    } catch (error) {
      Alert.alert('Error', `Failed to share URL: ${error}`);
    }
  };

  const getStatusColor = (status: boolean) => status ? '#34C759' : '#FF3B30';

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Deep Link Test</Text>
        <TouchableOpacity 
          onPress={() => setShowTester(!showTester)} 
          style={styles.testerButton}
        >
          <Text style={styles.testerButtonText}>
            {showTester ? 'Hide' : 'Show'} Tester
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Auth Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Authentication Status</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Authenticated:</Text>
            <Text style={[styles.statusValue, { color: getStatusColor(isAuthenticated) }]}>
              {isAuthenticated ? 'Yes' : 'No'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Needs Biometric:</Text>
            <Text style={[styles.statusValue, { color: getStatusColor(!requiresBiometricAuth) }]}>
              {requiresBiometricAuth ? 'Yes' : 'No'}
            </Text>
          </View>
          {user && (
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>User:</Text>
              <Text style={styles.statusValue}>{user.name || user.email}</Text>
            </View>
          )}
        </View>

        {/* Deep Link Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Deep Link Status</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Has Pending:</Text>
            <Text style={[styles.statusValue, { color: getStatusColor(!hasPendingDeepLink) }]}>
              {hasPendingDeepLink ? 'Yes' : 'No'}
            </Text>
          </View>
          {pendingDeepLink && (
            <>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Type:</Text>
                <Text style={styles.statusValue}>{pendingDeepLink.type}</Text>
              </View>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>ID:</Text>
                <Text style={styles.statusValue}>{pendingDeepLink.id || 'N/A'}</Text>
              </View>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Needs Login:</Text>
                <Text style={[styles.statusValue, { color: getStatusColor(!needsLogin) }]}>
                  {needsLogin ? 'Yes' : 'No'}
                </Text>
              </View>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Needs Biometric:</Text>
                <Text style={[styles.statusValue, { color: getStatusColor(!needsBiometricAuth) }]}>
                  {needsBiometricAuth ? 'Yes' : 'No'}
                </Text>
              </View>
            </>
          )}
          {lastProcessedUrl && (
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Last URL:</Text>
              <Text style={[styles.statusValue, styles.urlText]} numberOfLines={2}>
                {lastProcessedUrl}
              </Text>
            </View>
          )}
        </View>

        {/* Custom URL Schemes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Custom URL Schemes (clubm://)</Text>
          {testUrls.map((item, index) => (
            <View key={index} style={styles.urlItem}>
              <View style={styles.urlInfo}>
                <Text style={styles.urlLabel}>{item.label}</Text>
                <Text style={styles.urlValue}>{item.url}</Text>
              </View>
              <View style={styles.urlActions}>
                <TouchableOpacity 
                  style={styles.testButton} 
                  onPress={() => handleTestUrl(item.url)}
                >
                  <Text style={styles.buttonText}>Test</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.shareButton} 
                  onPress={() => handleShareUrl(item.url)}
                >
                  <Text style={styles.buttonText}>Share</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Universal Links */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Universal Links (https://)</Text>
          <Text style={styles.sectionNote}>
            Note: Universal links require server-side setup. See docs/deep-linking-setup.md
          </Text>
          {universalLinks.map((item, index) => (
            <View key={index} style={styles.urlItem}>
              <View style={styles.urlInfo}>
                <Text style={styles.urlLabel}>{item.label}</Text>
                <Text style={styles.urlValue}>{item.url}</Text>
              </View>
              <View style={styles.urlActions}>
                <TouchableOpacity 
                  style={styles.testButton} 
                  onPress={() => handleTestUrl(item.url)}
                >
                  <Text style={styles.buttonText}>Test</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.shareButton} 
                  onPress={() => handleShareUrl(item.url)}
                >
                  <Text style={styles.buttonText}>Share</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Floating Tester */}
      <DeepLinkTester visible={showTester} />
    </SafeAreaView>
  );
}
