import {StyleSheet} from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20,
    paddingTop: 60
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#FFFFFF",
    textAlign: "center",
    fontFamily: "Ubuntu"
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20
  },
  errorText: {
    fontSize: 16,
    color: "#FF6B6B",
    textAlign: "center",
    marginBottom: 24,
    fontFamily: "Ubuntu"
  },
  progressContainer: {
    marginBottom: 32
  },
  progressTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  progressStep: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 16,
    fontFamily: "Ubuntu"
  },
  progressBar: {
    height: 4,
    backgroundColor: "#333333",
    borderRadius: 2
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#00D4AA",
    borderRadius: 2
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  subtitle: {
    fontSize: 16,
    color: "#CCCCCC",
    lineHeight: 24,
    fontFamily: "Ubuntu"
  },
  optionsContainer: {
    flex: 1,
    marginBottom: 24
  },
  optionCard: {
    backgroundColor: "#2A2A2A",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    borderWidth: 2,
    borderColor: "transparent"
  },
  optionCardSelected: {
    borderColor: "#00D4AA"
  },
  greenSection: {
    backgroundColor: "#00D4AA",
    height: 40,
    justifyContent: "center",
    alignItems: "flex-end",
    paddingRight: 16
  },
  checkIcon: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  optionContent: {
    padding: 16
  },
  optionContentSelected: {
    paddingTop: 16
  },
  optionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  installmentText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },
  feeBadge: {
    backgroundColor: "#FF6B6B",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4
  },
  feeText: {
    fontSize: 12,
    color: "#FFFFFF",
    fontWeight: "bold",
    fontFamily: "Ubuntu"
  },
  optionDetails: {
    gap: 4
  },
  totalText: {
    fontSize: 16,
    color: "#CCCCCC",
    fontFamily: "Ubuntu"
  },
  feePercentageText: {
    fontSize: 14,
    color: "#FF6B6B",
    fontFamily: "Ubuntu"
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
    paddingBottom: 20
  },
  backButton: {
    flex: 1
  },
  nextButton: {
    flex: 1
  }
});
