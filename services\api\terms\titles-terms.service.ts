/**
 * Titles Terms Service
 * GET /api/app/titles/term -> retorna lista de termos (ApiTerm[])
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiTerm} from "@/models/api/terms.models";

export class TitlesTermsService {
  private static readonly BASE_PATH = "/api/app/titles/term";

  /**
   * Buscar termos exigidos para o fluxo de título
   */
  static async getTitleTerms(): Promise<ApiTerm[]> {
    try {
      ApiLogger.info("Buscando termos do título");
      const response = await firstValueFrom(
        apiClient.get<ApiTerm | ApiTerm[]>(this.BASE_PATH)
      );
      const normalized: ApiTerm[] = Array.isArray(response)
        ? response
        : response
        ? [response]
        : [];
      ApiLogger.info(`Encontrados ${normalized.length} termos do título`);
      return normalized;
    } catch (error) {
      ApiLogger.error("Erro ao buscar termos do título", error as Error);
      throw error;
    }
  }
}

export default TitlesTermsService;
