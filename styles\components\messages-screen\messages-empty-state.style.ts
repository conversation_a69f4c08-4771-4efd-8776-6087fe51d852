import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    flex: 1
  },
  iconContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 12,
    marginBottom: 8,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#EAECF0",
    alignItems: "center",
    justifyContent: "center"
  },
  title: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: "#DFE9F0",
    textAlign: "center",
    marginTop: 8
  },
  description: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    color: "#DFE9F0",
    textAlign: "center",
    marginBottom: 12,
    paddingHorizontal: 8
  },
  button: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    paddingHorizontal: 32,
    paddingVertical: 12,
    minHeight: 44,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#0F7C4D",
    alignSelf: "center",
    width: 295
  },
  buttonText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    color: "#FCFCFD",
    textAlign: "center"
  },

  // Guest register button styles
  guestRegisterButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    paddingHorizontal: 32,
    paddingVertical: 12,
    minHeight: 44,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  guestRegisterButtonText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: "#FCFCFD",
    textAlign: "center"
  }
});

export default styles;
