import React, {useMemo, useEffect, useState, useCallback} from "react";
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  StyleSheet
} from "react-native";
import {useLocalSearchPara<PERSON>, useRouter} from "expo-router";
import {useTranslation} from "react-i18next";
import {Image} from "expo-image";
import Avatar from "@/components/user/avatar";
import {useAuth} from "@/contexts/AuthContext";
import stylesConstants from "@/styles/styles-constants";
import {useOpportunity, useOpportunities} from "@/hooks/api/use-opportunities";
import {useErrorHandling} from "@/hooks/use-error-handling";
import {useCreateChatWithMessage} from "@/hooks/api/use-chats";
import {useUserById} from "@/hooks/api/use-users";
import ChevronLeftIcon from "@/components/icons/chevron-left-icon";
import ShoppingCartIcon from "@/components/icons/shopping-cart-icon";
import MarkerPinIcon from "@/components/icons/marker-pin-icon";
import ChevronRightIcon from "@/components/icons/chevron-right-icon";
import SendIcon from "@/components/icons/send-icon";

import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";
import UpsellDrawer from "@/components/modals/upsell-drawer";
import {
  getOpportunityImageUrl,
  isValidImageUrl
} from "@/utils/opportunity-image";
import {apiClient} from "@/services/api/base/api-client";
import {firstValueFrom} from "rxjs";

import {
  showErrorToast,
  showSuccessToast,
  showWarningToast
} from "@/utils/error-handler";

// Constants
const CONSTANTS = {
  HTTP_STATUS: {
    NOT_FOUND: 404
  },
  CURRENCY: {
    DIVISOR: 100,
    LOCALE: "pt-BR"
  },
  RETRY: {
    MAX_ATTEMPTS: 2
  },
  DESCRIPTION: {
    MAX_LENGTH: 80
  },
  SIZES: {
    ICON_SMALL: 16,
    ICON_MEDIUM: 20,
    ICON_LARGE: 24,
    AVATAR_SIZE: 40,
    INPUT_HEIGHT: 48,
    BUTTON_SIZE: 48,
    PLACEHOLDER_SIZE: 60,
    SIMILAR_ITEM_WIDTH: 320,
    SIMILAR_IMAGE_SIZE: 100,
    HEADER_HEIGHT: 200,
    SCROLL_PADDING_TOP: 180
  },
  SPACING: {
    XS: 4,
    SM: 8,
    MD: 12,
    LG: 16,
    XL: 20,
    XXL: 24,
    XXXL: 32,
    HEADER_TOP: 40
  },
  BORDER: {
    RADIUS_SM: 4,
    RADIUS_MD: 8,
    RADIUS_LG: 12,
    RADIUS_XL: 24,
    RADIUS_ROUND: 30,
    WIDTH: 1,
    WIDTH_THICK: 1.5
  },
  STRINGS: {
    DEFAULT_MESSAGE: "Olá, estou interessado nessa oportunidade",
    NOT_INFORMED: "Não informado",
    NOT_AVAILABLE: "Não disponível",
    CURRENCY_CODE: "BRL",
    CURRENCY_STYLE: "currency",
    BUILDING_EMOJI: "🏢",
    SHARE_EMOJI: "📤",
    ADVERTISER_INFO_TITLE: "Informações do anunciante",
    DETAILS_TITLE: "Detalhes da oportunidade",
    SIMILAR_TITLE: "Oportunidades semelhantes",
    INVESTMENT_VALUE_TITLE: "Valor do investimento",
    DATE_LABEL: "Data/hora do anúncio",
    SEGMENT_LABEL: "Segmento",
    STAGE_LABEL: "Estágio atual",
    TARGET_MARKET_LABEL: "Mercado alvo"
  }
};

// Helper function to format currency
const formatCurrency = (value: number | undefined): string => {
  if (!value) return CONSTANTS.STRINGS.NOT_INFORMED;
  return (value / CONSTANTS.CURRENCY.DIVISOR).toLocaleString(
    CONSTANTS.CURRENCY.LOCALE,
    {
      style: "currency" as const,
      currency: CONSTANTS.STRINGS.CURRENCY_CODE
    }
  );
};

// Helper function to format date
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return CONSTANTS.STRINGS.NOT_INFORMED;
  const date = new Date(dateString);
  return date.toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
};

// Helper function to format location (concise display like create-opportunity screen)
const formatLocation = (opportunity: any): string => {
  if (!opportunity) return CONSTANTS.STRINGS.NOT_INFORMED;

  // Show only the state that was selected during creation (like create-opportunity screen)
  if (opportunity.addressState) {
    // Find the full state name from the state code
    const brazilianStates = [
      {id: "AC", label: "Acre"},
      {id: "AL", label: "Alagoas"},
      {id: "AP", label: "Amapá"},
      {id: "AM", label: "Amazonas"},
      {id: "BA", label: "Bahia"},
      {id: "CE", label: "Ceará"},
      {id: "DF", label: "Distrito Federal"},
      {id: "ES", label: "Espírito Santo"},
      {id: "GO", label: "Goiás"},
      {id: "MA", label: "Maranhão"},
      {id: "MT", label: "Mato Grosso"},
      {id: "MS", label: "Mato Grosso do Sul"},
      {id: "MG", label: "Minas Gerais"},
      {id: "PA", label: "Pará"},
      {id: "PB", label: "Paraíba"},
      {id: "PR", label: "Paraná"},
      {id: "PE", label: "Pernambuco"},
      {id: "PI", label: "Piauí"},
      {id: "RJ", label: "Rio de Janeiro"},
      {id: "RN", label: "Rio Grande do Norte"},
      {id: "RS", label: "Rio Grande do Sul"},
      {id: "RO", label: "Rondônia"},
      {id: "RR", label: "Roraima"},
      {id: "SC", label: "Santa Catarina"},
      {id: "SP", label: "São Paulo"},
      {id: "SE", label: "Sergipe"},
      {id: "TO", label: "Tocantins"}
    ];

    const selectedState = brazilianStates.find(
      (state) => state.id === opportunity.addressState
    );

    return selectedState ? selectedState.label : opportunity.addressState;
  }

  return CONSTANTS.STRINGS.NOT_INFORMED;
};

// Helper function to format complete address (detailed display)
const formatCompleteAddress = (opportunity: any): string => {
  if (!opportunity) return CONSTANTS.STRINGS.NOT_INFORMED;

  const parts = [];

  // Street and number
  if (opportunity.addressStreet) {
    let streetPart = opportunity.addressStreet;
    if (opportunity.addressNumber) {
      streetPart += `, ${opportunity.addressNumber}`;
    }
    parts.push(streetPart);
  }

  // Complement
  if (opportunity.addressComplement) {
    parts.push(opportunity.addressComplement);
  }

  // Neighborhood
  if (opportunity.addressNeighborhood) {
    parts.push(opportunity.addressNeighborhood);
  }

  // City and state
  if (opportunity.addressCity || opportunity.addressState) {
    let cityState = "";
    if (opportunity.addressCity) {
      cityState = opportunity.addressCity;
    }
    if (opportunity.addressState) {
      cityState += cityState
        ? ` - ${opportunity.addressState}`
        : opportunity.addressState;
    }
    if (cityState) {
      parts.push(cityState);
    }
  }

  // ZIP code
  if (opportunity.addressZip) {
    parts.push(`CEP: ${opportunity.addressZip}`);
  }

  return parts.length > 0 ? parts.join(", ") : CONSTANTS.STRINGS.NOT_INFORMED;
};

const OpportunityDetails: React.FC = () => {
  const {t} = useTranslation();
  const {handleError} = useErrorHandling();
  const {id} = useLocalSearchParams<{id: string}>();
  const router = useRouter();
  const {
    isVisible: isUpsellDrawerVisible,
    config: upsellConfig,
    interceptGuestAction,
    hideUpsellDrawer
  } = useUpsellDrawer();

  // State for message input
  const [message, setMessage] = useState(CONSTANTS.STRINGS.DEFAULT_MESSAGE);

  // State for opportunity image
  const [imageUri, setImageUri] = useState<string | undefined>(undefined);
  const [isImageLoading, setIsImageLoading] = useState(false);

  // Chat mutation for sending messages
  const createChatWithMessage = useCreateChatWithMessage({
    onSuccess: () => {
      showSuccessToast(
        t("opportunities.details.message.title", "Mensagem enviada"),
        t(
          "opportunities.details.message.success",
          "Sua mensagem foi enviada com sucesso! O chat foi criado e você pode continuar a conversa."
        )
      );
      setMessage(CONSTANTS.STRINGS.DEFAULT_MESSAGE);
    },
    onError: (error) => {
      console.error("Chat creation error:", error);

      // Provide more specific error messages
      let errorMessage = t(
        "opportunities.details.message.error",
        "Erro ao enviar mensagem. Tente novamente."
      );

      if (
        error?.message?.includes("advertiser") ||
        error?.message?.includes("user")
      ) {
        errorMessage = t(
          "opportunities.details.message.advertiser_error",
          "Não foi possível identificar o anunciante desta oportunidade."
        );
      } else if (
        error?.message?.includes("network") ||
        error?.message?.includes("connection")
      ) {
        errorMessage = t(
          "opportunities.details.message.network_error",
          "Erro de conexão. Verifique sua internet e tente novamente."
        );
      }

      handleError(error, {
        customMessage: errorMessage
      });
    }
  });

  // Convert id to number
  const opportunityId = useMemo(() => {
    const numId = parseInt(id || "0", 10);
    return isNaN(numId) ? 0 : numId;
  }, [id]);

  // Fetch opportunity data
  const {
    data: opportunity,
    isLoading,
    error,
    refetch
  } = useOpportunity(opportunityId, {
    enabled: opportunityId > 0, // Only fetch if we have a valid ID
    retry: (failureCount: number, error: any) => {
      // Don't retry for 404 errors (invalid ID)
      if (error?.status === CONSTANTS.HTTP_STATUS.NOT_FOUND) {
        return false;
      }
      return failureCount < CONSTANTS.RETRY.MAX_ATTEMPTS;
    }
  } as any);

  // Get userId from opportunity
  const userId = useMemo(() => {
    return (opportunity as any)?.userId || 0;
  }, [(opportunity as any)?.userId]);

  // Fetch user data for the opportunity owner (since API doesn't include user details)
  const {data: opportunityUser} = useUserById(userId, {
    enabled: userId > 0 // Only fetch if we have a valid userId
  } as any);

  // Current logged-in user
  const {user} = useAuth();

  // Determine if the current user is the owner of this opportunity
  const isOwner = useMemo(() => {
    const currentUserId = user?.id ? parseInt(String(user.id), 10) : 0;
    const advertiserIdRaw =
      (opportunity?.user?.id as any) ??
      (opportunity as any)?.userId ??
      userId ??
      0;
    const advertiserId = parseInt(String(advertiserIdRaw), 10) || 0;
    return (
      currentUserId > 0 && advertiserId > 0 && currentUserId === advertiserId
    );
  }, [user?.id, opportunity?.user?.id, (opportunity as any)?.userId, userId]);

  // Handle errors
  useEffect(() => {
    if (error) {
      handleError(error, {
        customMessage:
          error?.status === CONSTANTS.HTTP_STATUS.NOT_FOUND
            ? t(
                "opportunities.details.error.notFound",
                "Oportunidade não encontrada"
              )
            : t(
                "opportunities.details.error.loadFailed",
                "Erro ao carregar detalhes da oportunidade"
              )
      });
    }
  }, [error, handleError, t]);

  // Fetch general opportunities (not filtered by segment)
  const {data: similarOpportunitiesData, isLoading: isLoadingSimilar} =
    useOpportunities({
      pageSize: 5
    });

  // Similar opportunities list
  const similarOpportunities = useMemo(() => {
    if (!similarOpportunitiesData?.data) return [];
    return similarOpportunitiesData.data.filter(
      (item) => item.id !== opportunityId
    );
  }, [similarOpportunitiesData, opportunityId]);

  // Format currency value
  const formattedValue = useMemo(() => {
    const value = formatCurrency(opportunity?.value);
    return typeof value === "string" ? value : "Valor não disponível";
  }, [opportunity?.value]);

  // Format date
  const formattedDate = useMemo(() => {
    const date = formatDate(opportunity?.createdAt);
    return typeof date === "string" ? date : "Data não disponível";
  }, [opportunity?.createdAt]);

  // Format location (concise display)
  const formattedLocation = useMemo(() => {
    const location = formatLocation(opportunity);
    return typeof location === "string"
      ? location
      : "Localização não disponível";
  }, [opportunity]);

  // Format complete address (detailed display)
  const formattedCompleteAddress = useMemo(() => {
    const address = formatCompleteAddress(opportunity);
    return typeof address === "string" ? address : "Endereço não disponível";
  }, [opportunity]);

  // Format description with truncation
  const formattedDescription = useMemo(() => {
    if (!opportunity?.description) {
      return "Descrição não disponível";
    }

    if (opportunity.description.length > CONSTANTS.DESCRIPTION.MAX_LENGTH) {
      return {
        text: opportunity.description.substring(
          0,
          CONSTANTS.DESCRIPTION.MAX_LENGTH
        ),
        truncated: true
      };
    }

    return {
      text: opportunity.description,
      truncated: false
    };
  }, [opportunity?.description]);

  // Handle share action
  const handleShare = useCallback(() => {
    showWarningToast(
      t("opportunities.details.share.title", "Compartilhar"),
      t(
        "opportunities.details.share.message",
        "Funcionalidade de compartilhamento será implementada em breve"
      )
    );
  }, [t]);

  // Handle send message
  const handleSendMessage = useCallback(() => {
    if (!message.trim()) {
      showWarningToast(
        t("opportunities.details.message.title", "Mensagem"),
        t(
          "opportunities.details.message.empty",
          "Por favor, digite uma mensagem antes de enviar."
        )
      );
      return;
    }

    // Interceptar ação para usuários guest
    const wasIntercepted = interceptGuestAction(
      () => {
        // Get advertiser ID from multiple possible sources
        const advertiserId =
          opportunity?.user?.id || // Nested user object (preferred)
          (opportunity as any)?.userId || // Direct userId field (fallback)
          userId || // From separate user fetch
          0;

        // Debug logging to help identify data structure issues
        if (__DEV__) {
          console.log("Advertiser ID resolution:", {
            "opportunity?.user?.id": opportunity?.user?.id,
            "(opportunity as any)?.userId": (opportunity as any)?.userId,
            userId: userId,
            "final advertiserId": advertiserId,
            "opportunity keys": opportunity
              ? Object.keys(opportunity).join(", ")
              : "no opportunity"
          });
        }

        if (!advertiserId || advertiserId === 0) {
          showErrorToast(
            t("opportunities.details.message.title", "Erro"),
            t(
              "opportunities.details.message.advertiser_error",
              "Não foi possível identificar o anunciante desta oportunidade."
            )
          );
          return;
        }

        // Send message using the chat API
        createChatWithMessage.mutate({
          participantUserId: advertiserId,
          message: message.trim()
        });
      },
      {
        title: "Quase lá!",
        description:
          "Faça seu cadastro e torne-se membro para enviar mensagens e outros diversos benefícios no aplicativo."
      }
    );

    if (wasIntercepted) {
      console.log(
        "🚫 [OPPORTUNITY-DETAILS] Envio de mensagem interceptado para usuário guest"
      );
    }
  }, [
    message,
    opportunity?.user?.id,
    opportunity,
    userId,
    createChatWithMessage,
    t,
    interceptGuestAction
  ]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  // Handle user profile navigation
  const handleUserPress = useCallback(() => {
    // Get user ID from multiple possible sources
    const userIdToNavigate =
      opportunity?.user?.id || // Nested user object (preferred)
      (opportunity as any)?.userId || // Direct userId field (fallback)
      opportunityUser?.id || // From separate user fetch
      0;

    if (userIdToNavigate && userIdToNavigate !== 0) {
      router.push(`/(main)/user-profile/${userIdToNavigate}`);
    }
  }, [opportunity?.user?.id, opportunity, opportunityUser?.id, router]);

  // Get user display name
  const getUserDisplayName = useCallback(() => {
    // Try nested user object first, then separate user fetch, then fallback
    const name = opportunity?.user?.name || opportunityUser?.name || "Usuário";
    // Ensure we return a string, not an object
    return typeof name === "string" ? name : "Usuário";
  }, [opportunity?.user?.name, opportunityUser?.name]);

  // Determine the image URL to use
  const opportunityImageUrl = useMemo(() => {
    return getOpportunityImageUrl({
      imageUrl: opportunity?.imageUrl,
      imageId: opportunity?.imageId
    });
  }, [opportunity?.imageUrl, opportunity?.imageId]);

  // Function to fetch image with auth headers
  const fetchImageWithAuth = useCallback(async (imageUrl: string) => {
    try {
      setIsImageLoading(true);

      // Extract the endpoint from the full URL
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl || !imageUrl.startsWith(baseUrl)) {
        // If it's not our API URL, use it directly
        setImageUri(imageUrl);
        return;
      }

      const endpoint = imageUrl.replace(baseUrl, "");

      // Use apiClient to fetch with auth headers
      const response = await firstValueFrom(
        apiClient.request<ArrayBuffer>("GET", endpoint, undefined, {
          responseType: "arraybuffer"
        })
      );

      // Convert ArrayBuffer to base64
      const base64 = btoa(
        new Uint8Array(response).reduce(
          (data, byte) => data + String.fromCharCode(byte),
          ""
        )
      );

      // Create data URI
      const dataUri = `data:image/webp;base64,${base64}`;
      setImageUri(dataUri);
    } catch (error) {
      console.error("Error loading opportunity image:", error);
      // Fallback to direct URL
      setImageUri(imageUrl);
    } finally {
      setIsImageLoading(false);
    }
  }, []);

  // Effect to load image when opportunityImageUrl changes
  useEffect(() => {
    if (opportunityImageUrl && isValidImageUrl(opportunityImageUrl)) {
      fetchImageWithAuth(opportunityImageUrl);
    } else {
      setImageUri(undefined);
    }
  }, [opportunityImageUrl, fetchImageWithAuth]);

  // Render similar opportunities section
  const renderSimilarOpportunities = () => {
    if (isLoadingSimilar) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={stylesConstants.colors.brand.primary}
          />
          <Text style={styles.loadingText}>
            {t(
              "opportunities.details.loadingSimilar",
              "Carregando oportunidades similares..."
            )}
          </Text>
        </View>
      );
    }

    if (similarOpportunities.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {t(
              "opportunities.details.noSimilar",
              "Nenhuma oportunidade similar encontrada"
            )}
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.horizontalScrollContent}
        style={styles.horizontalScrollStyle}
      >
        <View style={styles.similarItemsContainer}>
          {similarOpportunities.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.similarItem}
              onPress={() =>
                router.push(`/(business)/opportunities/${item.id}`)
              }
            >
              {/* Top section: Image + Texts side by side */}
              <View style={styles.similarItemRow}>
                {/* Image */}
                {item.imageUrl ? (
                  <Image
                    source={{uri: item.imageUrl}}
                    style={styles.similarImage}
                    contentFit="cover"
                  />
                ) : (
                  <View style={styles.similarImagePlaceholder}>
                    <Text style={styles.buildingIcon}>🏢</Text>
                  </View>
                )}

                {/* Texts container - side */}
                <View style={styles.similarItemTexts}>
                  {/* Author */}
                  <Text style={styles.authorText}>
                    Publicado por:{" "}
                    {typeof item.user?.name === "string"
                      ? item.user.name
                      : "Usuário"}
                  </Text>

                  {/* Title */}
                  <Text style={styles.titleText} numberOfLines={2}>
                    {typeof item.title === "string"
                      ? item.title
                      : typeof item.description === "string"
                      ? item.description
                      : "Título não disponível"}
                  </Text>

                  {/* Price - top */}
                  <Text style={styles.priceText}>
                    {formatCurrency(item.value)}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    );
  };

  // Show loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingStateContainer}>
        <StatusBar
          barStyle="light-content"
          backgroundColor={stylesConstants.colors.mainBackground}
        />

        {/* Header */}
        <View style={styles.loadingStateHeader}>
          <TouchableOpacity onPress={handleBack}>
            <ChevronLeftIcon
              width={CONSTANTS.SIZES.ICON_LARGE}
              height={CONSTANTS.SIZES.ICON_LARGE}
            />
          </TouchableOpacity>

          <View style={styles.headerRow}>
            <TouchableOpacity style={styles.headerButton}>
              <ShoppingCartIcon
                width={CONSTANTS.SIZES.ICON_MEDIUM}
                height={CONSTANTS.SIZES.ICON_MEDIUM}
                replaceColor={stylesConstants.colors.white}
              />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleShare}>
              <Text style={styles.shareText}>📤</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.loadingStateContent}>
          <ActivityIndicator
            size="large"
            color={stylesConstants.colors.brand.primary}
          />
          <Text style={styles.loadingStateText}>
            {t("opportunities.details.loading", "Carregando detalhes...")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state for invalid ID
  if (opportunityId <= 0) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <StatusBar
          barStyle="light-content"
          backgroundColor={stylesConstants.colors.mainBackground}
        />

        {/* Header */}
        <View style={styles.errorHeaderRow}>
          <TouchableOpacity onPress={handleBack}>
            <ChevronLeftIcon
              width={CONSTANTS.SIZES.ICON_LARGE}
              height={CONSTANTS.SIZES.ICON_LARGE}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.errorContent}>
          <Text style={styles.errorTitle}>
            {t("opportunities.details.error.invalidId", "ID inválido")}
          </Text>
          <Text style={styles.errorMessage}>
            {t(
              "opportunities.details.error.invalidIdMessage",
              "O ID da oportunidade fornecido não é válido."
            )}
          </Text>
          <TouchableOpacity style={styles.errorButton} onPress={handleBack}>
            <Text style={styles.errorButtonText}>
              {t("opportunities.details.error.goBack", "Voltar")}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (error || (!isLoading && !opportunity)) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <StatusBar
          barStyle="light-content"
          backgroundColor={stylesConstants.colors.mainBackground}
        />

        {/* Header */}
        <View style={styles.errorHeaderContainer}>
          <TouchableOpacity onPress={handleBack}>
            <ChevronLeftIcon
              width={CONSTANTS.SIZES.ICON_LARGE}
              height={CONSTANTS.SIZES.ICON_LARGE}
            />
          </TouchableOpacity>

          <View style={styles.headerRow}>
            <TouchableOpacity style={styles.headerButton}>
              <ShoppingCartIcon
                width={CONSTANTS.SIZES.ICON_MEDIUM}
                height={CONSTANTS.SIZES.ICON_MEDIUM}
                replaceColor={stylesConstants.colors.white}
              />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleShare}>
              <Text style={styles.shareText}>📤</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.errorMainContent}>
          <Text style={styles.errorTitleText}>
            {t("opportunities.details.error.title", "Erro ao carregar")}
          </Text>
          <Text style={styles.errorMessageText}>
            {t(
              "opportunities.details.error.message",
              "Não foi possível carregar os detalhes da oportunidade."
            )}
          </Text>
          <TouchableOpacity
            style={styles.errorRetryButton}
            onPress={() => refetch()}
          >
            <Text style={styles.errorRetryButtonText}>
              {t("opportunities.details.error.retry", "Tentar novamente")}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Main content
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={stylesConstants.colors.mainBackground}
      />

      {/* Header with back button */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          onPress={handleBack}
          accessibilityRole="button"
          accessibilityLabel={t("common.back", "Voltar")}
        >
          <ChevronLeftIcon
            width={CONSTANTS.SIZES.ICON_LARGE}
            height={CONSTANTS.SIZES.ICON_LARGE}
          />
        </TouchableOpacity>
      </View>

      {/* Background with gradient effect */}
      <View style={styles.mainContainer}>
        {/* Background image */}
        <View style={styles.backgroundImage}>
          {imageUri ? (
            <Image
              source={{uri: imageUri}}
              style={styles.opportunityImage}
              contentFit="cover"
            />
          ) : isImageLoading ? (
            <View style={styles.placeholderContainer}>
              <ActivityIndicator
                size="small"
                color={stylesConstants.colors.brand.primary}
              />
              <Text style={styles.loadingImageText}>Carregando...</Text>
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <Text style={styles.buildingIcon}>🏢</Text>
            </View>
          )}
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Main opportunity card */}
          <View style={styles.opportunityCard}>
            {/* Title */}
            <Text style={styles.opportunityTitle}>
              {typeof opportunity?.title === "string"
                ? opportunity.title
                : "Título não disponível"}
            </Text>

            {/* Location */}
            <View style={styles.locationRow}>
              <MarkerPinIcon
                width={CONSTANTS.SIZES.ICON_SMALL}
                height={CONSTANTS.SIZES.ICON_SMALL}
                replaceColor={stylesConstants.colors.white}
              />
              <Text style={styles.locationText}>{formattedLocation}</Text>
            </View>

            {/* Description with "Ver mais" */}
            <Text style={styles.descriptionText}>
              {typeof formattedDescription === "string" ? (
                formattedDescription
              ) : (
                <>
                  {formattedDescription.text}
                  {formattedDescription.truncated && (
                    <>
                      ... <Text style={styles.seeMoreText}>Ver mais</Text>
                    </>
                  )}
                </>
              )}
            </Text>

            {/* Advertiser info section title */}
            <Text style={styles.sectionTitle}>
              {CONSTANTS.STRINGS.ADVERTISER_INFO_TITLE}
            </Text>

            {/* Advertiser info */}
            <TouchableOpacity
              style={styles.advertiserContainer}
              onPress={handleUserPress}
              activeOpacity={0.7}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel={`Ver perfil de ${getUserDisplayName()}`}
            >
              <View style={styles.advertiserInfoContainer}>
                <Avatar
                  size={40}
                  borderSize={2}
                  user={opportunity?.user || opportunityUser}
                />
                <View>
                  <Text style={styles.advertiserName}>
                    {getUserDisplayName()}
                  </Text>
                  <Text style={styles.advertiserContact}>
                    {t("opportunities.details.contactInfo", "Entre em contato")}
                  </Text>
                </View>
              </View>
              <ChevronRightIcon
                width={CONSTANTS.SIZES.ICON_MEDIUM}
                height={CONSTANTS.SIZES.ICON_MEDIUM}
              />
            </TouchableOpacity>

            {/* Details card */}
            <View style={styles.detailsSection}>
              <Text style={styles.detailsSectionTitle}>
                {CONSTANTS.STRINGS.DETAILS_TITLE}
              </Text>

              {/* Details rows with alternating backgrounds */}
              <View style={styles.detailsContainer}>
                {/* Date/time row */}
                <View style={styles.detailsRow}>
                  <Text style={styles.detailsLabel}>
                    {CONSTANTS.STRINGS.DATE_LABEL}
                  </Text>
                  <Text style={styles.detailsValue}>{formattedDate}</Text>
                </View>

                {/* Segment row */}
                <View style={styles.detailsRowAlt}>
                  <Text style={styles.detailsLabelText}>
                    {CONSTANTS.STRINGS.SEGMENT_LABEL}
                  </Text>
                  <Text style={styles.detailsValueText}>
                    {typeof opportunity?.segment?.name === "string"
                      ? opportunity.segment.name
                      : CONSTANTS.STRINGS.NOT_INFORMED}
                  </Text>
                </View>

                {/* Current stage row */}
                <View style={styles.detailsRowDark}>
                  <Text style={styles.detailsLabelText}>Estágio atual</Text>
                  <Text style={styles.detailsValueText}>
                    {typeof opportunity?.currentStage?.name === "string"
                      ? opportunity.currentStage.name
                      : "Não informado"}
                  </Text>
                </View>

                {/* Target market row */}
                <View style={styles.detailsRowLight}>
                  <Text style={styles.detailsLabelText}>Mercado alvo</Text>
                  <Text style={styles.detailsValueText}>
                    {typeof (opportunity as any)?.targetMarket === "string"
                      ? (opportunity as any).targetMarket
                      : CONSTANTS.STRINGS.NOT_INFORMED}
                  </Text>
                </View>

                {/* Reason row - if available */}
                {(opportunity as any)?.reason && (
                  <View style={styles.detailsRowDark}>
                    <Text style={styles.detailsLabelText}>Motivo</Text>
                    <Text style={styles.detailsValueText}>
                      {typeof (opportunity as any).reason === "string"
                        ? (opportunity as any).reason
                        : "Não informado"}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Similar opportunities section */}
            <Text style={styles.similarOpportunitiesTitle}>
              Oportunidades semelhantes
            </Text>

            {/* Similar opportunities horizontal scroll */}
            {renderSimilarOpportunities()}
          </View>
        </ScrollView>

        {/* Fixed footer */}
        {!isOwner && (
          <View style={styles.messageSection}>
            {/* Investment value */}
            <View style={styles.messageHeaderContainer}>
              <Text style={styles.messageHeaderTitle}>
                Valor do investimento
              </Text>
              <Text style={styles.messageHeaderPrice}>{formattedValue}</Text>
            </View>

            {/* Message input and send button */}
            <View style={styles.messageInputRow}>
              <View style={styles.messageInputWrapper}>
                <TextInput
                  style={styles.messageTextInput}
                  value={message}
                  onChangeText={setMessage}
                  placeholder="Olá, estou interessado nessa oportunidade"
                  placeholderTextColor={stylesConstants.colors.gray100}
                  multiline={false}
                />
              </View>
              <TouchableOpacity
                style={[
                  styles.messageSendButtonFull,
                  createChatWithMessage.isPending &&
                    styles.messageSendButtonDisabled
                ]}
                onPress={handleSendMessage}
                disabled={createChatWithMessage.isPending}
                accessibilityRole="button"
                accessibilityLabel={t(
                  "opportunities.details.sendMessage",
                  "Enviar mensagem"
                )}
              >
                {createChatWithMessage.isPending ? (
                  <ActivityIndicator
                    size="small"
                    color={stylesConstants.colors.white}
                  />
                ) : (
                  <SendIcon
                    width={CONSTANTS.SIZES.ICON_MEDIUM}
                    height={CONSTANTS.SIZES.ICON_MEDIUM}
                    replaceColor={stylesConstants.colors.white}
                  />
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Upsell Drawer for Guest Users */}
      <UpsellDrawer
        visible={isUpsellDrawerVisible}
        onClose={hideUpsellDrawer}
        title={upsellConfig.title}
        description={upsellConfig.description}
      />
    </SafeAreaView>
  );
};

// Base styles to reduce duplication
const baseTextStyles = {
  primaryText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans
  },
  grayText: {
    color: stylesConstants.colors.gray100,
    fontFamily: stylesConstants.fonts.openSans
  },
  whiteText: {
    color: stylesConstants.colors.white,
    fontFamily: stylesConstants.fonts.openSans
  }
};

const baseSizeStyles = {
  text12: {fontSize: 12, lineHeight: 18},
  text14: {fontSize: 14, lineHeight: 20},
  text16: {fontSize: 16, lineHeight: 22},
  text18: {fontSize: 18, lineHeight: 24}
};

const baseLayoutStyles = {
  flexRow: {flexDirection: "row" as const},
  flexColumn: {flexDirection: "column" as const},
  centerItems: {alignItems: "center" as const},
  spaceBetween: {justifyContent: "space-between" as const},
  centerContent: {justifyContent: "center" as const}
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  headerRow: {
    flexDirection: "row",
    gap: 16
  },
  headerButton: {
    padding: 6
  },
  shareText: {
    fontSize: 16,
    color: stylesConstants.colors.gray100
  },
  buildingIcon: {
    fontSize: 24
  },
  scrollContent: {
    paddingTop: 180,
    paddingBottom: 20
  },
  detailsContainer: {
    gap: 0
  },
  horizontalScrollContent: {
    paddingRight: 24
  },
  horizontalScrollStyle: {
    marginHorizontal: -24
  },
  similarItemsContainer: {
    paddingLeft: 24,
    flexDirection: "row",
    gap: 12
  },
  similarItemRow: {
    flexDirection: "row",
    gap: 16
  },
  similarItemTexts: {
    flex: 1,
    gap: 4
  },

  loadingContainer: {
    height: 120,
    justifyContent: "center",
    alignItems: "center"
  },
  loadingText: {
    marginTop: 8,
    color: stylesConstants.colors.gray100,
    fontSize: 12,
    fontFamily: stylesConstants.fonts.openSans
  },
  emptyContainer: {
    height: 80,
    justifyContent: "center",
    alignItems: "center"
  },
  emptyText: {
    color: stylesConstants.colors.gray100,
    fontSize: 14,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  similarItem: {
    width: 320,
    gap: 8
  },
  similarImage: {
    width: 100,
    height: 100,
    borderRadius: 8
  },
  similarImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.gray700,
    justifyContent: "center",
    alignItems: "center"
  },
  authorText: {
    color: stylesConstants.colors.gray100,
    fontSize: 10,
    lineHeight: 14,
    fontFamily: stylesConstants.fonts.openSans
  },
  titleText: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text12,
    lineHeight: 16
  },
  priceText: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text16,
    fontWeight: "700",
    marginTop: 4
  },

  errorContainer: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  errorHeaderRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  errorContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.white,
    textAlign: "center",
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  errorMessage: {
    fontSize: 14,
    color: stylesConstants.colors.gray25,
    textAlign: "center",
    marginBottom: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  errorButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  errorButtonText: {
    color: stylesConstants.colors.white,
    fontWeight: "600",
    fontFamily: stylesConstants.fonts.openSans
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    position: "absolute",
    top: 40,
    left: 0,
    right: 0,
    zIndex: 10
  },
  mainContainer: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    position: "relative"
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: stylesConstants.colors.gray800,
    justifyContent: "center",
    alignItems: "center"
  },
  placeholderContainer: {
    width: 60,
    height: 60,
    backgroundColor: stylesConstants.colors.gray700,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center"
  },
  opportunityImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%"
  },
  loadingImageText: {
    marginTop: 8,
    color: stylesConstants.colors.gray100,
    fontSize: 12,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  loadingStateContainer: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  loadingStateHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  loadingStateContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32
  },
  loadingStateText: {
    marginTop: 16,
    color: stylesConstants.colors.gray100,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  opportunityCard: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderWidth: 1.5,
    borderColor: stylesConstants.colors.mainBackgroundBorder,
    borderBottomWidth: 0,
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 24
  },
  opportunityTitle: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text18,
    fontWeight: "700",
    marginBottom: 8
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginBottom: 16
  },
  locationText: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text12
  },
  descriptionText: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text14,
    fontWeight: "400",
    marginBottom: 24
  },
  seeMoreText: {
    color: stylesConstants.colors.secondaryBrand200,
    fontWeight: "700"
  },
  sectionTitle: {
    ...baseTextStyles.primaryText,
    ...baseSizeStyles.text12,
    fontWeight: "400",
    marginBottom: 12
  },

  // Error state styles
  errorHeaderContainer: {
    ...baseLayoutStyles.flexRow,
    ...baseLayoutStyles.spaceBetween,
    ...baseLayoutStyles.centerItems,
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  errorMainContent: {
    flex: 1,
    ...baseLayoutStyles.centerContent,
    ...baseLayoutStyles.centerItems,
    padding: 32
  },
  errorTitleText: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.white,
    textAlign: "center",
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  errorMessageText: {
    fontSize: 14,
    color: stylesConstants.colors.gray25,
    textAlign: "center",
    marginBottom: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  errorRetryButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  errorRetryButtonText: {
    color: stylesConstants.colors.white,
    fontWeight: "600",
    fontFamily: stylesConstants.fonts.openSans
  },
  // Advertiser info styles
  advertiserContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24
  },
  advertiserInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  advertiserName: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.openSans
  },
  advertiserContact: {
    color: stylesConstants.colors.gray100,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    fontFamily: stylesConstants.fonts.openSans
  },
  // Details section styles
  detailsSection: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.mainBackgroundBorder,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 24
  },
  detailsSectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginBottom: 16,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  detailsRow: {
    backgroundColor: stylesConstants.colors.gray700,
    borderRadius: 4,
    padding: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  detailsRowAlt: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  detailsLabel: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    fontFamily: stylesConstants.fonts.openSans
  },
  detailsValue: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    lineHeight: 18,
    textAlign: "right",
    fontFamily: stylesConstants.fonts.openSans
  },
  // Similar opportunities section styles
  similarSectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  // Message section styles
  messageSection: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderStrokesLines,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24
  },
  messageSectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  messageSectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageSectionPrice: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageInputContainer: {
    flexDirection: "row",
    gap: 12,
    alignItems: "center",
    marginBottom: 24
  },
  messageInput: {
    flex: 1,
    height: 48,
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.gray100,
    paddingHorizontal: 16,
    justifyContent: "center"
  },
  messageInputText: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageSendButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    padding: 8,
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center"
  },
  // Additional missing styles
  detailsLabelText: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    fontFamily: stylesConstants.fonts.openSans
  },
  detailsValueText: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    lineHeight: 18,
    textAlign: "right",
    fontFamily: stylesConstants.fonts.openSans
  },
  detailsRowDark: {
    backgroundColor: stylesConstants.colors.gray700,
    borderRadius: 4,
    padding: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  detailsRowLight: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  similarOpportunitiesTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  messageHeaderTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageHeaderPrice: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageInputRow: {
    flexDirection: "row",
    gap: 12,
    alignItems: "center",
    marginBottom: 24
  },
  messageInputWrapper: {
    flex: 1,
    height: 48,
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.gray100,
    paddingHorizontal: 16,
    justifyContent: "center"
  },
  messageTextInput: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.openSans
  },
  messageSendButtonFull: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    padding: 8,
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center"
  },
  messageSendButtonDisabled: {
    backgroundColor: stylesConstants.colors.gray100,
    opacity: 0.6
  }
});

export default OpportunityDetails;
