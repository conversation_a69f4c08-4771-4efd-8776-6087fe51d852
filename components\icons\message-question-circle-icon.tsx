import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MessageQuestionCircleIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const MessageQuestionCircleIcon: React.FC<MessageQuestionCircleIconProps> = (
  props
) => {
  const color = useMemo(() => props.replaceColor ?? "#DFE9F0", [props.replaceColor]);

  return (
    <Svg width={props.width ?? 12} height={props.height ?? 12} viewBox="0 0 12 12" fill="none" {...props}>
      <Path
        d="M5.05401 9.1Q4.1489 9.1 3.31089 8.77715Q3.26018 8.75762 3.23604 8.74859Q3.21576 8.75053 3.17899 8.75433L0.618476 9.01901Q0.377596 9.04391 0.280159 9.04504Q0.00278072 9.04829 -0.193854 8.92834Q-0.53002 8.72328 -0.590969 8.33426Q-0.626619 8.1067 -0.517569 7.85164Q-0.479261 7.76203 -0.364156 7.54898L0.453672 6.03521Q0.48647 5.9745 0.503891 5.94119Q0.489502 5.896 0.457126 5.79995Q0.204012 5.04901 0.204012 4.25Q0.204012 3.26367 0.585395 2.36198Q0.953576 1.4915 1.62454 0.820532Q2.29551 0.149564 3.16599 -0.218617Q4.06768 -0.6 5.05401 -0.600001Q6.04034 -0.6 6.94204 -0.218617Q7.81251 0.149563 8.48348 0.820532Q9.15445 1.4915 9.52263 2.36198Q9.90401 3.26367 9.90401 4.25Q9.90401 5.23633 9.52263 6.13802Q9.15445 7.0085 8.48348 7.67947Q7.81251 8.35044 6.94203 8.71862Q6.04034 9.1 5.05401 9.1ZM5.05401 7.9Q5.797 7.9 6.47457 7.61341Q7.12949 7.33641 7.63495 6.83094Q8.14042 6.32547 8.41742 5.67056Q8.70401 4.99299 8.70401 4.25Q8.70401 3.50701 8.41742 2.82944Q8.14042 2.17453 7.63495 1.66906Q7.12948 1.16359 6.47457 0.886588Q5.797 0.6 5.05401 0.6Q4.31102 0.6 3.63345 0.886589Q2.97854 1.16359 2.47307 1.66906Q1.9676 2.17453 1.6906 2.82944Q1.40401 3.50701 1.40401 4.25Q1.40401 4.85221 1.59427 5.41666Q1.69912 5.72774 1.70986 5.86338Q1.72422 6.04495 1.6793 6.22145Q1.64575 6.35331 1.50944 6.6056L0.871475 7.78646L3.0556 7.56069Q3.21412 7.5443 3.29187 7.54727Q3.39566 7.55124 3.49688 7.57457Q3.57269 7.59204 3.74229 7.65738Q4.37205 7.9 5.05401 7.9ZM3.48801 2.80201Q3.69726 2.20719 4.24087 1.88771Q4.78448 1.56822 5.40595 1.67482Q6.02742 1.78142 6.4335 2.2638Q6.83957 2.74619 6.83863 3.37673Q6.83863 4.16535 6.00937 4.71819Q5.66147 4.95012 5.30421 5.06921C4.98984 5.174 4.65005 5.0041 4.54526 4.68974C4.44047 4.37537 4.61037 4.03558 4.92473 3.93079Q5.12955 3.86252 5.34373 3.71973Q5.63863 3.52313 5.63863 3.37495Q5.63892 3.18325 5.51547 3.0366Q5.39202 2.88995 5.20309 2.85755Q5.01415 2.82514 4.84889 2.92227Q4.68362 3.01939 4.62001 3.20022C4.51005 3.51281 4.1675 3.67708 3.85491 3.56712C3.54232 3.45716 3.37805 3.1146 3.48801 2.80201ZM5.12896 6.6C4.7976 6.6 4.52896 6.33137 4.52896 6C4.52896 5.66863 4.7976 5.4 5.12896 5.4L5.12896 6.6ZM5.73396 6C5.73396 5.66863 5.46533 5.4 5.13396 5.4L5.13396 6.6C5.46533 6.6 5.73396 6.33137 5.73396 6Z"
        fill={color as string}
        transform="translate(1.1958, 1.5)"
      />
    </Svg>
  );
};

export default MessageQuestionCircleIcon;

