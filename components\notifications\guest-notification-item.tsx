import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useRouter} from "expo-router";
import GeneralNotificationsIcon from "@/components/icons/general-notifications-icon";
import {useGuestUser} from "@/contexts/guest-user-context";
import styles from "@/styles/notifications/notifications.style";

interface GuestNotificationItemProps {
  showSeparator?: boolean;
}

const GuestNotificationItem: React.FC<GuestNotificationItemProps> = ({
  showSeparator = true
}) => {
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    // Clear guest user data (logout from upsell) and navigate to login
    await clearGuestUser();
    router.push("/(auth)/login");
  };

  return (
    <View style={styles.notificationItem}>
      <View style={styles.notificationRow}>
        <View style={styles.avatarContainer}>
          <View style={styles.iconContainer}>
            <GeneralNotificationsIcon width={20} height={20} />
          </View>
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.notificationText}>
            E ai, curtindo o app? Aproveite muito mais! Faça seu cadastro e
            desfrute de todos os recursos do Club M.
          </Text>
        </View>

        <View style={styles.unreadIndicator} />
      </View>

      <View style={styles.actionButtonContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleRegisterPress}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>Cadastre-se agora!</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.timestampContainer}>
        <Text style={styles.timestampText}>Enviado agora</Text>
      </View>

      {showSeparator && <View style={styles.separator} />}
    </View>
  );
};

export default GuestNotificationItem;
