{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-71:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5007,5111,5255,5377,5482,5620,5748,5859,6091,6228,6332,6482,6604,6743,6889,6953,7019", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "5106,5250,5372,5477,5615,5743,5854,5956,6223,6327,6477,6599,6738,6884,6948,7014,7098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,153,217,279,352,403,459,524", "endColumns": "53,43,63,61,72,50,55,64,55", "endOffsets": "104,148,212,274,347,398,454,519,575"}, "to": {"startLines": "76,100,101,102,103,104,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7316,9936,9980,10044,10106,10179,15550,15606,15671", "endColumns": "53,43,63,61,72,50,55,64,55", "endOffsets": "7365,9975,10039,10101,10174,10225,15601,15666,15722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,40,41,42,43,44,52,53,54,78,79,80,105,108,110,111,112,113,114,115,116,117,118,119,120,121,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3503,3581,3657,3741,3833,4628,4729,4848,7460,7519,7582,10230,10445,10579,10679,10742,10807,10868,10936,10998,11056,11170,11230,11291,11348,11421,11771,11852,11944,12051,12149,12229,12377,12458,12539,12667,12756,12832,12885,12939,13005,13083,13163,13234,13316,13388,13462,13535,13605,13714,13805,13876,13966,14061,14135,14218,14311,14360,14441,14510,14596,14681,14743,14807,14870,14939,15048,15158,15255,15355,15412,15470,16248,16327,16402", "endLines": "9,40,41,42,43,44,52,53,54,78,79,80,105,108,110,111,112,113,114,115,116,117,118,119,120,121,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,181,182,183", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,3576,3652,3736,3828,3911,4724,4843,4920,7514,7577,7668,10294,10507,10674,10737,10802,10863,10931,10993,11051,11165,11225,11286,11343,11416,11539,11847,11939,12046,12144,12224,12372,12453,12534,12662,12751,12827,12880,12934,13000,13078,13158,13229,13311,13383,13457,13530,13600,13709,13800,13871,13961,14056,14130,14213,14306,14355,14436,14505,14591,14676,14738,14802,14865,14934,15043,15153,15250,15350,15407,15465,15545,16322,16397,16473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "75,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "7216,7906,8004,8112", "endColumns": "99,97,107,101", "endOffsets": "7311,7999,8107,8209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "64", "startColumns": "4", "startOffsets": "5961", "endColumns": "129", "endOffsets": "6086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "45,46,47,48,49,50,51,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3916,4009,4111,4206,4309,4412,4514,17101", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4004,4106,4201,4304,4407,4509,4623,17197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,278,346,424,491,561,639,718,799,887,965,1045,1129,1203,1281,1358,1433,1512,1584,1668,1738,1824,1893", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,201,273,341,419,486,556,634,713,794,882,960,1040,1124,1198,1276,1353,1428,1507,1579,1663,1733,1819,1888,1966"}, "to": {"startLines": "37,55,99,106,107,109,123,124,125,175,176,177,179,184,185,186,187,188,189,190,191,193,194,195,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3169,4925,9864,10299,10367,10512,11544,11614,11692,15727,15808,15896,16086,16478,16562,16636,16714,16791,16866,16945,17017,17202,17272,17358,17427", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "3233,5002,9931,10362,10440,10574,11609,11687,11766,15803,15891,15969,16161,16557,16631,16709,16786,16861,16940,17012,17096,17267,17353,17422,17500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,16166", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,16243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,320,433,523,654,756,866,980,1100,1243,1388,1510,1644,1750,1894,2009,2149,2296,2406,2518,2621,2753,2840,2956,3056,3185", "endColumns": "146,117,112,89,130,101,109,113,119,142,144,121,133,105,143,114,139,146,109,111,102,131,86,115,99,128,98", "endOffsets": "197,315,428,518,649,751,861,975,1095,1238,1383,1505,1639,1745,1889,2004,2144,2291,2401,2513,2616,2748,2835,2951,3051,3180,3279"}, "to": {"startLines": "38,39,74,77,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,178,197,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3238,3385,7103,7370,7673,7804,8214,8324,8438,8558,8701,8846,8968,9102,9208,9352,9467,9607,9754,15974,17505,17608,17740,17827,17943,18043,18172", "endColumns": "146,117,112,89,130,101,109,113,119,142,144,121,133,105,143,114,139,146,109,111,102,131,86,115,99,128,98", "endOffsets": "3380,3498,7211,7455,7799,7901,8319,8433,8553,8696,8841,8963,9097,9203,9347,9462,9602,9749,9859,16081,17603,17735,17822,17938,18038,18167,18266"}}]}]}