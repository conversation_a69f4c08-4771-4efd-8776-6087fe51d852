import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: stylesConstants.colors.brand.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  avatarInitials: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center"
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: stylesConstants.colors.gray800,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  content: {
    flex: 1,
    justifyContent: "center"
  },
  title: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    marginBottom: 2
  },
  subtitle: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 2
  },
  description: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16,
    marginTop: 2
  },
  removeButton: {
    padding: 8,
    marginLeft: 8
  }
});

export default styles;
