/**
 * Subscriptions Service
 * POST /api/app/subscriptions -> cria assinatura/pagamento do plano
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiResponse} from "../base/api-types";

export interface CreateSubscriptionRequest {
  entity: number;
  entityId: number;
  paymentType: number; // 0=CreditCard, 1=Boleto, 2=PIX
  creditCardId?: number;
  creditCard?: {
    holderName: string;
    number: string;
    expiryMonth: string;
    expiryYear: string;
    ccv: string;
    name: string;
    cpfCnpj: string;
    postalCode: string;
    addressNumber: string;
    addressComplement: string;
    phoneNumber: string;
  };
  day: number;
}

export interface Subscription {
  id: string;
  entity: number;
  entityId: number;
  paymentType: number;
  creditCardId?: number;
  day: number;
  status: number;
  userId: number;
  value?: number;
  createdAt: string;
  updatedAt: string;
}

export class SubscriptionsService {
  private static readonly BASE_PATH = "/api/app/subscriptions";

  /**
   * Criar nova assinatura/pagamento do plano
   */
  static async createSubscription(
    subscriptionData: CreateSubscriptionRequest
  ): Promise<ApiResponse<Subscription>> {
    try {
      console.log("🌐 [SUBSCRIPTIONS-SERVICE] Criando assinatura");
      console.log("📤 [SUBSCRIPTIONS-SERVICE] URL:", this.BASE_PATH);
      console.log("📋 [SUBSCRIPTIONS-SERVICE] Payload:", subscriptionData);

      const response = await firstValueFrom(
        apiClient.post<Subscription>(this.BASE_PATH, subscriptionData)
      );

      console.log("📥 [SUBSCRIPTIONS-SERVICE] Resposta da API:", response);
      ApiLogger.info("Assinatura criada com sucesso", {subscriptionId: response.id});
      
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error("❌ [SUBSCRIPTIONS-SERVICE] Erro ao criar assinatura:", error);
      ApiLogger.error("Erro ao criar assinatura", error as Error);
      throw error;
    }
  }
}

export default SubscriptionsService;
