{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-71:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "72,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7015,7722,7822,7928", "endColumns": "90,99,105,101", "endOffsets": "7101,7817,7923,8025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4838,4941,5095,5220,5324,5463,5588,5700,5921,6057,6161,6306,6429,6563,6708,6768,6828", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4936,5090,5215,5319,5458,5583,5695,5798,6052,6156,6301,6424,6558,6703,6763,6823,6904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "42,43,44,45,46,47,48,187", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3777,3871,3973,4070,4167,4268,4368,16503", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3866,3968,4065,4162,4263,4363,4469,16599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,15571", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,15648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,326,432,523,667,781,890,1008,1137,1261,1392,1506,1633,1727,1878,1999,2142,2284,2397,2517,2615,2742,2833,2953,3051,3178", "endColumns": "150,119,105,90,143,113,108,117,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "201,321,427,518,662,776,885,1003,1132,1256,1387,1501,1628,1722,1873,1994,2137,2279,2392,2512,2610,2737,2828,2948,3046,3173,3270"}, "to": {"startLines": "35,36,71,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,173,192,193,194,195,196,197,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3086,3237,6909,7160,7464,7608,8030,8139,8257,8386,8510,8641,8755,8882,8976,9127,9248,9391,9533,15373,16898,16996,17123,17214,17334,17432,17559", "endColumns": "150,119,105,90,143,113,108,117,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "3232,3352,7010,7246,7603,7717,8134,8252,8381,8505,8636,8750,8877,8971,9122,9243,9386,9528,9641,15488,16991,17118,17209,17329,17427,17554,17651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,75,76,77,102,105,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3357,3434,3509,3586,3686,4474,4567,4680,7251,7311,7376,10004,10224,10355,10447,10510,10570,10629,10692,10753,10807,10909,10966,11025,11079,11147,11482,11563,11638,11725,11805,11887,12019,12090,12163,12287,12375,12451,12504,12558,12624,12697,12773,12844,12922,12992,13067,13149,13217,13318,13403,13473,13563,13654,13728,13801,13890,13941,14022,14089,14171,14256,14318,14382,14445,14513,14607,14702,14792,14889,14946,15004,15653,15735,15810", "endLines": "6,37,38,39,40,41,49,50,51,75,76,77,102,105,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,176,177,178", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "356,3429,3504,3581,3681,3772,4562,4675,4755,7306,7371,7459,10069,10282,10442,10505,10565,10624,10687,10748,10802,10904,10961,11020,11074,11142,11253,11558,11633,11720,11800,11882,12014,12085,12158,12282,12370,12446,12499,12553,12619,12692,12768,12839,12917,12987,13062,13144,13212,13313,13398,13468,13558,13649,13723,13796,13885,13936,14017,14084,14166,14251,14313,14377,14440,14508,14602,14697,14787,14884,14941,14999,15074,15730,15805,15881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,109,153,217,278,345,397", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "104,148,212,273,340,392,454"}, "to": {"startLines": "73,97,98,99,100,101,169", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7106,9716,9760,9824,9885,9952,15079", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "7155,9755,9819,9880,9947,9999,15136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "34,52,96,103,104,106,120,121,122,170,171,172,174,179,180,181,182,183,184,185,186,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3017,4760,9646,10074,10143,10287,11258,11326,11404,15141,15223,15302,15493,15886,15966,16039,16119,16197,16272,16344,16416,16604,16675,16754,16823", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3081,4833,9711,10138,10219,10350,11321,11399,11477,15218,15297,15368,15566,15961,16034,16114,16192,16267,16339,16411,16498,16670,16749,16818,16893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5803", "endColumns": "117", "endOffsets": "5916"}}]}]}