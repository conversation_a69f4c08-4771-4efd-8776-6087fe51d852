import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ShareIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const ShareIcon: React.FC<ShareIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#DFE9F0",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        stroke={color}
        d="M16.6667 6.66667C18.0474 6.66667 19.1667 5.54738 19.1667 4.16667C19.1667 2.78595 18.0474 1.66667 16.6667 1.66667C15.286 1.66667 14.1667 2.78595 14.1667 4.16667C14.1667 5.54738 15.286 6.66667 16.6667 6.66667Z"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M3.33333 12.5C4.71405 12.5 5.83333 11.3807 5.83333 10C5.83333 8.61929 4.71405 7.5 3.33333 7.5C1.95262 7.5 0.833333 8.61929 0.833333 10C0.833333 11.3807 1.95262 12.5 3.33333 12.5Z"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M16.6667 18.3333C18.0474 18.3333 19.1667 17.214 19.1667 15.8333C19.1667 14.4526 18.0474 13.3333 16.6667 13.3333C15.286 13.3333 14.1667 14.4526 14.1667 15.8333C14.1667 17.214 15.286 18.3333 16.6667 18.3333Z"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M5.65 11.15L14.35 16.5167"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        stroke={color}
        d="M14.35 3.48333L5.65 8.85"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ShareIcon;
