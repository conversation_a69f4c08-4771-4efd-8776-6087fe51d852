/**
 * Serviço para gerenciamento de objetivos/badges do usuário
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "@/services/api/base/api-client";
import {ApiLogger} from "@/services/api/base/api-logger";
import {
  Objective,
  UpdateObjectiveRequest,
  ObjectivesParams,
  ObjectivesResponse
} from "@/models/api/objectives.models";

export default class ObjectivesService {
  private static readonly BASE_PATH = "/api/app/objectives";

  /**
   * Buscar objetivos do usuário
   */
  static async getObjectives(
    params?: ObjectivesParams
  ): Promise<ObjectivesResponse> {
    try {
      ApiLogger.info("Buscando objetivos do usuário", {params});

      const queryParams = new URLSearchParams();
      if (params?.search) queryParams.append("search", params.search);
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.pageSize)
        queryParams.append("pageSize", params.pageSize.toString());

      const url = `${this.BASE_PATH}${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await firstValueFrom(
        apiClient.get<ObjectivesResponse>(url)
      );

      // Log status da request no console
      console.log(`✅ API Status: GET ${url} - 200 OK`);
      console.log(
        `📊 Objectives Data: ${
          response.data?.length || 0
        } objetivos encontrados`
      );

      ApiLogger.info("Objetivos carregados com sucesso", {
        count: response.data?.length || 0,
        totalCount: response.totalCount
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar objetivos", error as Error);
      console.log(
        `❌ API Status: GET ${this.BASE_PATH} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Criar novo objetivo
   */
  static async createObjective(data: {
    name?: string;
    title?: string;
    description?: string;
  }): Promise<Objective> {
    try {
      const name = (data.name ?? data.title ?? "").trim();
      const description = (data.description ?? name).trim();

      ApiLogger.info("Criando novo objetivo", {name});

      const response = await firstValueFrom(
        apiClient.post<Objective>(this.BASE_PATH, {name, description})
      );

      // Log status da request no console
      console.log(`✅ API Status: POST ${this.BASE_PATH} - 200 OK`);
      console.log(`📊 Objective Created: ${response.title || response.name}`);

      ApiLogger.info("Objetivo criado com sucesso", {
        id: response.id,
        title: response.title,
        name: (response as any).name
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao criar objetivo", error as Error);
      console.log(
        `❌ API Status: POST ${this.BASE_PATH} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Buscar objetivo específico por ID
   */
  static async getObjectiveById(id: string | number): Promise<Objective> {
    try {
      ApiLogger.info("Buscando objetivo por ID", {id});

      const response = await firstValueFrom(
        apiClient.get<Objective>(`${this.BASE_PATH}/${id}`)
      );

      console.log(`✅ API Status: GET ${this.BASE_PATH}/${id} - 200 OK`);

      ApiLogger.info("Objetivo encontrado", {
        id: response.id,
        title: response.title
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar objetivo", error as Error, {id});
      console.log(
        `❌ API Status: GET ${this.BASE_PATH}/${id} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Atualizar objetivo
   */
  static async updateObjective(
    id: string | number,
    data: UpdateObjectiveRequest
  ): Promise<Objective> {
    try {
      ApiLogger.info("Atualizando objetivo", {id, data});

      const response = await firstValueFrom(
        apiClient.put<Objective>(`${this.BASE_PATH}/${id}`, data)
      );

      console.log(`✅ API Status: PUT ${this.BASE_PATH}/${id} - 200 OK`);

      ApiLogger.info("Objetivo atualizado com sucesso", {
        id: response.id,
        title: response.title
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao atualizar objetivo", error as Error, {id});
      console.log(
        `❌ API Status: PUT ${this.BASE_PATH}/${id} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Deletar objetivo
   */
  static async deleteObjective(id: string | number): Promise<void> {
    try {
      ApiLogger.info("Deletando objetivo", {id});

      await firstValueFrom(apiClient.delete(`${this.BASE_PATH}/${id}`));

      console.log(`✅ API Status: DELETE ${this.BASE_PATH}/${id} - 200 OK`);

      ApiLogger.info("Objetivo deletado com sucesso", {id});
    } catch (error: any) {
      ApiLogger.error("Erro ao deletar objetivo", error as Error, {id});
      console.log(
        `❌ API Status: DELETE ${this.BASE_PATH}/${id} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }
}
