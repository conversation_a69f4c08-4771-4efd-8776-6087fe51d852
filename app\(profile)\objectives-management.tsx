import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert
} from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useObjectives } from "@/hooks/api/use-objectives";
import { Objective } from "@/services/api/objectives/objectives.service";
import ObjectivesManager from "@/components/user/objectives-manager";
import styles from "@/styles/profile/objectives-management.style";

const ObjectivesManagement: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);

  // Fetch objectives from API
  const {
    data: objectivesData,
    isLoading,
    error,
    refetch
  } = useObjectives({
    pageSize: 50, // Get more objectives for management
    page: 1
  });

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const handleObjectiveCreated = useCallback((objective: Objective) => {
    // Refresh the list to show the new objective
    refetch();
  }, [refetch]);

  const handleObjectiveUpdated = useCallback((objective: Objective) => {
    // Refresh the list to show the updated objective
    refetch();
  }, [refetch]);

  const handleObjectiveDeleted = useCallback((objectiveId: string | number) => {
    // Refresh the list to remove the deleted objective
    refetch();
  }, [refetch]);

  if (isLoading && !objectivesData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>
            {t("objectives.loading", "Carregando objetivos...")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error && !objectivesData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>
            {t("objectives.error.title", "Erro")}
          </Text>
          <Text style={styles.errorMessage}>
            {error.message || t("objectives.error.loadFailed", "Erro ao carregar objetivos")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const objectives = objectivesData?.data || [];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#007AFF"
            colors={["#007AFF"]}
          />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>
            {t("objectives.management.title", "Gerenciar Objetivos")}
          </Text>
          <Text style={styles.subtitle}>
            {t("objectives.management.subtitle", "Crie e gerencie seus objetivos pessoais")}
          </Text>
        </View>

        <ObjectivesManager
          objectives={objectives}
          onObjectiveCreated={handleObjectiveCreated}
          onObjectiveUpdated={handleObjectiveUpdated}
          onObjectiveDeleted={handleObjectiveDeleted}
        />

        {objectives.length === 0 && !isLoading && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyTitle}>
              {t("objectives.empty.title", "Nenhum objetivo encontrado")}
            </Text>
            <Text style={styles.emptyMessage}>
              {t("objectives.empty.message", "Comece criando seu primeiro objetivo!")}
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ObjectivesManagement;
