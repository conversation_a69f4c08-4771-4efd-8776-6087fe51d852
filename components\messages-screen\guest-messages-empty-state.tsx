import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import LogoutIcon from "@/components/icons/logout-icon";
import {useGuestUser} from "@/contexts/guest-user-context";
import styles from "@/styles/components/messages-screen/messages-empty-state.style";
import stylesConstants from "@/styles/styles-constants";

const GuestMessagesEmptyState: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    // Clear guest user data (logout from upsell) and navigate to login
    await clearGuestUser();
    router.push("/(auth)/login");
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <LogoutIcon
          width={48}
          height={48}
          fill={stylesConstants.colors.gray400}
        />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.title}>Conecte-se com outros membros!</Text>
        <Text style={styles.description}>
          Faça seu cadastro e comece a trocar mensagens com outros membros do
          Club M. Networking nunca foi tão fácil!
        </Text>
      </View>

      <TouchableOpacity
        style={styles.guestRegisterButton}
        onPress={handleRegisterPress}
        activeOpacity={0.8}
      >
        <Text style={styles.guestRegisterButtonText}>Cadastre-se agora!</Text>
      </TouchableOpacity>
    </View>
  );
};

export default GuestMessagesEmptyState;
