import {useState, useCallback} from "react";
import {useGuestUser} from "@/contexts/guest-user-context";

export interface UpsellDrawerConfig {
  title?: string;
  description?: string;
}

export function useUpsellDrawer() {
  const [isVisible, setIsVisible] = useState(false);
  const [config, setConfig] = useState<UpsellDrawerConfig>({});
  const {isGuest} = useGuestUser();

  const showUpsellDrawer = useCallback(
    (drawerConfig?: UpsellDrawerConfig) => {
      if (isGuest) {
        setConfig(drawerConfig || {});
        setIsVisible(true);
        return true; // Indicates that the drawer was shown
      }
      return false; // Indicates that the drawer was not shown (user is not guest)
    },
    [isGuest]
  );

  const hideUpsellDrawer = useCallback(() => {
    setIsVisible(false);
    setConfig({});
  }, []);

  const interceptGuestAction = useCallback(
    (action: () => void, drawerConfig?: UpsellDrawerConfig) => {
      if (isGuest) {
        showUpsellDrawer(drawerConfig);
        return true; // Action was intercepted
      } else {
        action();
        return false; // Action was executed normally
      }
    },
    [isGuest, showUpsellDrawer]
  );

  return {
    isVisible,
    config,
    isGuest,
    showUpsellDrawer,
    hideUpsellDrawer,
    interceptGuestAction
  };
}
