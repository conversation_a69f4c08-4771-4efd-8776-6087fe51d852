import React, {useState, useCallback, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Clipboard
} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import {useCreatePayment, usePaymentBarCode} from "@/hooks/api/use-payments";
import {
  PaymentEntity,
  PaymentType,
  CreatePaymentRequest
} from "@/models/api/payments.models";
import {useCurrentUser} from "@/hooks/api/use-auth";

const BoletoPayment: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [isCreatingPayment, setIsCreatingPayment] = useState(false);

  const {data: currentUser} = useCurrentUser();
  const createPaymentMutation = useCreatePayment();

  // Buscar código de barras do boleto apenas se o pagamento foi criado
  const {
    data: boletoDetails,
    isLoading: isLoadingBoleto,
    error: boletoError
  } = usePaymentBarCode(paymentId || "");

  const formatCurrency = useCallback((value: string) => {
    const numValue = parseFloat(value) / 100;
    return `R$ ${numValue.toFixed(2).replace(".", ",")}`;
  }, []);

  const createTitlePayment = useCallback(async () => {
    if (!currentUser) {
      Alert.alert(
        t("boletoPayment.error.title", "Erro"),
        t("boletoPayment.error.userNotFound", "Usuário não encontrado")
      );
      return;
    }

    setIsCreatingPayment(true);

    try {
      const paymentRequest: CreatePaymentRequest = {
        entity: PaymentEntity.Title,
        entityId: currentUser.id, // enviar ID do usuário atual
        type: PaymentType.Boleto,
        boleto: {
          installmentCount: parseInt(params.selectedInstallments as string) || 1
        }
      };

      const response = await createPaymentMutation.mutateAsync(paymentRequest);
      setPaymentId(response.id);
    } catch (error) {
      console.error("❌ [BOLETO-PAYMENT] Erro ao criar pagamento:", error);
      Alert.alert(
        t("boletoPayment.error.title", "Erro"),
        t(
          "boletoPayment.error.createPayment",
          "Erro ao criar pagamento. Tente novamente."
        )
      );
    } finally {
      setIsCreatingPayment(false);
    }
  }, [currentUser, params, createPaymentMutation, t]);

  const copyBoletoCode = useCallback(async () => {
    if (boletoDetails?.barCode) {
      await Clipboard.setString(boletoDetails.barCode);
      Alert.alert(
        t("boletoPayment.success.title", "Sucesso"),
        t(
          "boletoPayment.success.copied",
          "Código de barras copiado para a área de transferência"
        )
      );
    }
  }, [boletoDetails, t]);

  const handleNext = useCallback(() => {
    router.push({
      pathname: "/(registration)/plan-payment-selection",
      params: {
        ...params,
        titlePaymentId: paymentId
      }
    });
  }, [router, params, paymentId]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  useEffect(() => {
    if (!paymentId && !isCreatingPayment) {
      createTitlePayment();
    }
  }, [paymentId, isCreatingPayment, createTitlePayment]);

  if (isCreatingPayment || isLoadingBoleto) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A"
          }}
        >
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text
            style={{
              marginTop: 16,
              fontSize: 16,
              color: "#FFFFFF",
              textAlign: "center"
            }}
          >
            {isCreatingPayment
              ? t("boletoPayment.creating", "Gerando boleto...")
              : t("boletoPayment.loading", "Carregando código de barras...")}
          </Text>
        </View>
      </Screen>
    );
  }

  if (boletoError) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A",
            paddingHorizontal: 20
          }}
        >
          <Text
            style={{
              fontSize: 16,
              color: "#FF6B6B",
              textAlign: "center",
              marginBottom: 24
            }}
          >
            {t(
              "boletoPayment.error.loadBoleto",
              "Erro ao carregar boleto. Tente novamente."
            )}
          </Text>
          <FullSizeButton
            text={t("common.tryAgain", "Tentar novamente")}
            onPress={createTitlePayment}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <ScrollView
        style={{flex: 1, backgroundColor: "#1A1A1A"}}
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 60,
          paddingBottom: 20
        }}
      >
        <BackButton />

        <View style={{marginBottom: 32}}>
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8
            }}
          >
            {t("boletoPayment.createAccount", "Criar conta")}
          </Text>
          <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 16}}>
            {t("boletoPayment.stepProgress", "4 / 7 Pagamento do título")}
          </Text>
          <View
            style={{height: 4, backgroundColor: "#333333", borderRadius: 2}}
          >
            <View
              style={{
                height: "100%",
                backgroundColor: "#00D4AA",
                borderRadius: 2,
                width: "56%"
              }}
            />
          </View>
        </View>

        <View style={{marginBottom: 24}}>
          <Text
            style={{
              fontSize: 20,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8
            }}
          >
            {t("boletoPayment.title", "Pagamento Boleto")}
          </Text>
          <Text style={{fontSize: 16, color: "#CCCCCC", lineHeight: 24}}>
            {t(
              "boletoPayment.description",
              "Copie o código de barras para efetuar o pagamento."
            )}
          </Text>
        </View>

        <View
          style={{
            backgroundColor: "#2A2A2A",
            borderRadius: 12,
            padding: 16,
            marginBottom: 24
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 12
            }}
          >
            {t("boletoPayment.summary", "Resumo do pagamento")}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text style={{fontSize: 14, color: "#CCCCCC"}}>
              {t("boletoPayment.item", "Item:")}
            </Text>
            <Text style={{fontSize: 14, fontWeight: "bold", color: "#FFFFFF"}}>
              {t("boletoPayment.titleAdhesion", "Título de Adesão")}
            </Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text style={{fontSize: 14, color: "#CCCCCC"}}>
              {t("boletoPayment.total", "Total:")}
            </Text>
            <Text style={{fontSize: 16, fontWeight: "bold", color: "#00D4AA"}}>
              {formatCurrency(params.totalValue as string)}
            </Text>
          </View>
        </View>

        {boletoDetails && (
          <View
            style={{
              backgroundColor: "#2A2A2A",
              borderRadius: 12,
              padding: 16,
              marginBottom: 24
            }}
          >
            <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
              {t("boletoPayment.barCode", "Código de barras:")}
            </Text>
            <View
              style={{
                backgroundColor: "#333333",
                borderRadius: 8,
                padding: 12,
                marginBottom: 12
              }}
            >
              <Text
                style={{
                  fontSize: 12,
                  color: "#FFFFFF",
                  fontFamily: "monospace"
                }}
                numberOfLines={3}
              >
                {boletoDetails.barCode}
              </Text>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: "#00D4AA",
                borderRadius: 8,
                padding: 12,
                alignItems: "center"
              }}
              onPress={copyBoletoCode}
            >
              <Text
                style={{fontSize: 14, fontWeight: "bold", color: "#FFFFFF"}}
              >
                {t("boletoPayment.copyCode", "Copiar código")}
              </Text>
            </TouchableOpacity>

            {boletoDetails.identificationField && (
              <View style={{marginTop: 16}}>
                <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
                  {t(
                    "boletoPayment.identificationField",
                    "Campo de identificação:"
                  )}
                </Text>
                <Text style={{fontSize: 14, color: "#FFFFFF"}}>
                  {boletoDetails.identificationField}
                </Text>
              </View>
            )}

            <View style={{marginTop: 24}}>
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "bold",
                  color: "#FFFFFF",
                  marginBottom: 12
                }}
              >
                {t("boletoPayment.instructions.title", "Como pagar:")}
              </Text>
              <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
                {t(
                  "boletoPayment.instructions.step1",
                  "1. Acesse o app ou site do seu banco"
                )}
              </Text>
              <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
                {t(
                  "boletoPayment.instructions.step2",
                  "2. Escolha a opção 'Pagar boleto'"
                )}
              </Text>
              <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
                {t(
                  "boletoPayment.instructions.step3",
                  "3. Cole o código de barras"
                )}
              </Text>
              <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 8}}>
                {t(
                  "boletoPayment.instructions.step4",
                  "4. Confirme o pagamento"
                )}
              </Text>
            </View>
          </View>
        )}

        <View style={{flexDirection: "row", gap: 12, paddingTop: 20}}>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("boletoPayment.continue", "Continuar")}
              onPress={handleNext}
              disabled={!boletoDetails}
            />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
};

export default BoletoPayment;
