import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface MessageNotificationCircleIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const MessageNotificationCircleIcon: React.FC<MessageNotificationCircleIconProps> = (
  props
) => {
  const color = useMemo(() => props.replaceColor ?? "#DFE9F0", [props.replaceColor]);

  return (
    <Svg width={props.width ?? 12} height={props.height ?? 12} viewBox="0 0 12 12" fill="none" {...props}>
      <Path
        // Motiff: message-notification-circle
        d="M9.28894 0.0150758Q9.59076 0.316901 9.7502 0.710072Q9.90401 1.08937 9.90401 1.5Q9.90401 1.91062 9.7502 2.28993Q9.59076 2.6831 9.28894 2.98492Q8.98711 3.28675 8.59394 3.44619Q8.21464 3.6 7.80401 3.6Q7.39338 3.6 7.01408 3.44619Q6.62091 3.28675 6.31909 2.98492Q6.01726 2.6831 5.85782 2.28993Q5.70401 1.91063 5.70401 1.5Q5.70401 1.08937 5.85782 0.710072Q6.01726 0.316901 6.31909 0.0150754Q6.62091 -0.28675 7.01408 -0.446187Q7.39338 -0.6 7.80401 -0.6Q8.21464 -0.6 8.59394 -0.446187Q8.98711 -0.28675 9.28894 0.0150758ZM4.60245 -0.579217C4.93241 -0.609729 5.22464 -0.366974 5.25515 -0.0370142C5.28566 0.292946 5.04291 0.585172 4.71295 0.615685Q4.02719 0.6791 3.41377 0.988275Q2.81644 1.28935 2.36262 1.78442Q1.90717 2.28126 1.65989 2.90436Q1.40401 3.54914 1.40401 4.25Q1.40401 4.85221 1.59427 5.41666Q1.69912 5.72774 1.70986 5.86338Q1.72422 6.04495 1.6793 6.22145Q1.64575 6.35331 1.50944 6.6056L0.871475 7.78646L3.05567 7.56068Q3.21415 7.5443 3.29187 7.54727Q3.39567 7.55124 3.49688 7.57457Q3.57269 7.59204 3.74229 7.65738Q4.37205 7.9 5.05401 7.9Q5.75545 7.9 6.40068 7.64371Q7.02421 7.39604 7.5212 6.93992Q8.01644 6.48541 8.31722 5.88729Q8.62613 5.27302 8.68875 4.58647C8.71885 4.25647 9.01077 4.01335 9.34077 4.04345C9.67076 4.07355 9.91389 4.36547 9.88379 4.69547Q9.80049 5.60874 9.38929 6.42642Q8.98976 7.22089 8.3326 7.82401Q7.67271 8.42965 6.84366 8.75895Q5.98505 9.1 5.05401 9.1Q4.14889 9.1 3.31089 8.77715Q3.26022 8.75764 3.23604 8.74859Q3.21583 8.75052 3.17903 8.75432L0.618476 9.01901Q0.377596 9.04391 0.280159 9.04504Q0.00278072 9.04829 -0.193854 8.92834Q-0.530021 8.72328 -0.590969 8.33426Q-0.626619 8.1067 -0.517569 7.85164Q-0.479261 7.76203 -0.364156 7.54898L0.453672 6.03521Q0.486476 5.97449 0.503891 5.94119Q0.4895 5.896 0.457126 5.79995Q0.204012 5.04901 0.204013 4.25Q0.204012 3.31973 0.544516 2.46172Q0.873294 1.63326 1.47804 0.97354Q2.08025 0.316599 2.87367 -0.0833067Q3.6902 -0.494858 4.60245 -0.579217ZM8.44041 0.863604Q8.1768 0.6 7.80401 0.6Q7.43122 0.6 7.16762 0.863603Q6.90401 1.12721 6.90401 1.5Q6.90401 1.87279 7.16762 2.1364Q7.43122 2.4 7.80401 2.4Q8.1768 2.4 8.44041 2.1364Q8.70401 1.87279 8.70401 1.5Q8.70401 1.12721 8.44041 0.863604Z"
        fill={color as string}
        transform="translate(1.1958, 1.5)"
      />
    </Svg>
  );
};

export default MessageNotificationCircleIcon;

