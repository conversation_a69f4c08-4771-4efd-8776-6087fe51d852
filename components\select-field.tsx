import React, {useState, useCallback} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  TextInput
} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "@/styles/components/select-field.style";
import ChevronDownIcon from "@/components/icons/chevron-down-icon";
import CloseIcon from "@/components/icons/close-icon";
import CheckIcon from "@/components/icons/check-icon";
import SearchIcon from "@/components/icons/search-icon";

export interface SelectOption {
  value: string;
  label: string;
}

interface SelectFieldProps {
  label: string;
  value?: string;
  placeholder?: string;
  options: SelectOption[];
  onSelect: (value: string) => void;
  error?: string;
  disabled?: boolean;
  loading?: boolean;
  searchable?: boolean;
}

const SelectField: React.FC<SelectFieldProps> = ({
  label,
  value,
  placeholder = "Selecione uma opção",
  options,
  onSelect,
  error,
  disabled = false,
  loading = false,
  searchable = true
}) => {
  const {t} = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const selectedOption = options.find(option => option.value === value);

  const filteredOptions = searchable && searchTerm
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  const handleOpen = useCallback(() => {
    if (!disabled && !loading) {
      setIsVisible(true);
      setSearchTerm("");
    }
  }, [disabled, loading]);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    setSearchTerm("");
  }, []);

  const handleSelect = useCallback((optionValue: string) => {
    onSelect(optionValue);
    handleClose();
  }, [onSelect, handleClose]);

  return (
    <>
      <View style={styles.container}>
        <Text style={styles.label}>{label}</Text>
        
        <TouchableOpacity
          style={[
            styles.selectButton,
            error && styles.selectButtonError,
            disabled && styles.selectButtonDisabled
          ]}
          onPress={handleOpen}
          disabled={disabled || loading}
          activeOpacity={0.7}
        >
          <Text
            style={[
              styles.selectText,
              !selectedOption && styles.selectTextPlaceholder,
              disabled && styles.selectTextDisabled
            ]}
          >
            {selectedOption ? selectedOption.label : placeholder}
          </Text>
          
          {loading ? (
            <ActivityIndicator size="small" color="#F2F4F7" />
          ) : (
            <ChevronDownIcon
              width={20}
              height={20}
              style={[styles.chevronIcon, isVisible && styles.chevronIconRotated]}
            />
          )}
        </TouchableOpacity>

        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>

      <Modal
        visible={isVisible}
        transparent
        animationType="slide"
        onRequestClose={handleClose}
      >
        <View style={styles.overlay}>
          <SafeAreaView style={styles.modal}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>{label}</Text>
              <TouchableOpacity
                onPress={handleClose}
                style={styles.closeButton}
                activeOpacity={0.7}
              >
                <CloseIcon width={24} height={24} />
              </TouchableOpacity>
            </View>

            {/* Search */}
            {searchable && (
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <SearchIcon width={20} height={20} />
                  <TextInput
                    style={styles.searchInput}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                    placeholder={t("selectField.search", "Buscar...")}
                    placeholderTextColor="#9CA3AF"
                    autoCapitalize="words"
                  />
                </View>
              </View>
            )}

            {/* Options */}
            <ScrollView style={styles.optionsList} showsVerticalScrollIndicator={false}>
              {filteredOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionItem,
                    option.value === value && styles.optionItemSelected
                  ]}
                  onPress={() => handleSelect(option.value)}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.optionText,
                      option.value === value && styles.optionTextSelected
                    ]}
                  >
                    {option.label}
                  </Text>
                  
                  {option.value === value && (
                    <CheckIcon width={20} height={20} />
                  )}
                </TouchableOpacity>
              ))}
              
              {filteredOptions.length === 0 && (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t("selectField.noOptions", "Nenhuma opção encontrada")}
                  </Text>
                </View>
              )}
            </ScrollView>
          </SafeAreaView>
        </View>
      </Modal>
    </>
  );
};

export default SelectField;
