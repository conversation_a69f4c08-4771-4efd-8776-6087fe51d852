/**
 * Deep Link Context
 * Manages deep link state, pending navigation, and authentication integration
 */

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { DeepLinkService, DeepLinkRoute, ParsedDeepLink } from '@/services/deep-link.service';
import { ApiLogger } from '@/services/api/base/api-logger';

interface DeepLinkContextType {
  // State
  pendingDeepLink: DeepLinkRoute | null;
  isProcessingDeepLink: boolean;
  lastProcessedUrl: string | null;
  
  // Actions
  handleDeepLink: (url: string) => Promise<void>;
  clearPendingDeepLink: () => void;
  processPendingDeepLink: () => Promise<void>;
  
  // Utilities
  canNavigateToRoute: (route: DeepLinkRoute) => boolean;
}

const DeepLinkContext = createContext<DeepLinkContextType | undefined>(undefined);

interface DeepLinkProviderProps {
  children: ReactNode;
}

export function DeepLinkProvider({ children }: DeepLinkProviderProps) {
  const [pendingDeepLink, setPendingDeepLink] = useState<DeepLinkRoute | null>(null);
  const [isProcessingDeepLink, setIsProcessingDeepLink] = useState(false);
  const [lastProcessedUrl, setLastProcessedUrl] = useState<string | null>(null);

  /**
   * Check if we can navigate to a route based on authentication requirements
   */
  const canNavigateToRoute = useCallback((route: DeepLinkRoute): boolean => {
    // For now, we'll handle auth checks in the hook that uses this context
    // This allows for more flexible integration with different auth states
    return true;
  }, []);

  /**
   * Handle incoming deep link URL
   */
  const handleDeepLink = useCallback(async (url: string): Promise<void> => {
    try {
      setIsProcessingDeepLink(true);
      
      // Avoid processing the same URL multiple times
      if (url === lastProcessedUrl) {
        ApiLogger.info('Deep link already processed, skipping', { url });
        return;
      }

      ApiLogger.info('Processing deep link', { url });
      
      const parsed: ParsedDeepLink = DeepLinkService.parseDeepLink(url);
      
      if (!parsed.isValid || !parsed.route) {
        ApiLogger.warn('Invalid deep link', { url, error: parsed.error });
        return;
      }

      const { route } = parsed;
      
      // Check if route requires authentication
      if (route.requiresAuth) {
        ApiLogger.info('Deep link requires authentication, storing as pending', { route });
        setPendingDeepLink(route);
        setLastProcessedUrl(url);
      } else {
        // Navigate immediately for routes that don't require auth
        ApiLogger.info('Navigating to deep link route immediately', { route });
        DeepLinkService.navigateToRoute(route);
        setLastProcessedUrl(url);
      }
    } catch (error) {
      ApiLogger.error('Error handling deep link', error as Error);
    } finally {
      setIsProcessingDeepLink(false);
    }
  }, [lastProcessedUrl]);

  /**
   * Process pending deep link (called after successful authentication)
   */
  const processPendingDeepLink = useCallback(async (): Promise<void> => {
    if (!pendingDeepLink) {
      return;
    }

    try {
      setIsProcessingDeepLink(true);
      ApiLogger.info('Processing pending deep link', { route: pendingDeepLink });
      
      DeepLinkService.navigateToRoute(pendingDeepLink);
      
      // Clear pending deep link after successful navigation
      setPendingDeepLink(null);
    } catch (error) {
      ApiLogger.error('Error processing pending deep link', error as Error);
      // Don't clear pending deep link on error, allow retry
    } finally {
      setIsProcessingDeepLink(false);
    }
  }, [pendingDeepLink]);

  /**
   * Clear pending deep link
   */
  const clearPendingDeepLink = useCallback(() => {
    ApiLogger.info('Clearing pending deep link');
    setPendingDeepLink(null);
  }, []);

  /**
   * Set up deep link listeners on mount
   */
  useEffect(() => {
    let linkingListener: (() => void) | null = null;

    const setupDeepLinking = async () => {
      try {
        // Handle initial URL (app opened via deep link)
        const initialUrl = await DeepLinkService.getInitialURL();
        if (initialUrl) {
          ApiLogger.info('Processing initial deep link URL', { initialUrl });
          await handleDeepLink(initialUrl);
        }

        // Set up listener for deep links while app is running
        linkingListener = DeepLinkService.addLinkingListener(async (url: string) => {
          ApiLogger.info('Received deep link while app running', { url });
          await handleDeepLink(url);
        });
      } catch (error) {
        ApiLogger.error('Error setting up deep linking', error as Error);
      }
    };

    setupDeepLinking();

    // Cleanup listener on unmount
    return () => {
      if (linkingListener) {
        linkingListener();
      }
    };
  }, [handleDeepLink]);

  const value: DeepLinkContextType = {
    // State
    pendingDeepLink,
    isProcessingDeepLink,
    lastProcessedUrl,
    
    // Actions
    handleDeepLink,
    clearPendingDeepLink,
    processPendingDeepLink,
    
    // Utilities
    canNavigateToRoute
  };

  return (
    <DeepLinkContext.Provider value={value}>
      {children}
    </DeepLinkContext.Provider>
  );
}

/**
 * Hook to use deep link context
 */
export function useDeepLink() {
  const context = useContext(DeepLinkContext);
  if (context === undefined) {
    throw new Error('useDeepLink must be used within a DeepLinkProvider');
  }
  return context;
}

export default DeepLinkContext;
