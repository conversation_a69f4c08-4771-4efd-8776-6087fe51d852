/**
 * Serviço de Áreas de Interesse para ClubM
 * Implementa operações para gerenciamento de áreas de interesse
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {PaginationResponse} from "@/models/api/common.models";

// Interfaces para áreas de interesse
export interface AreaInterest {
  id: number;
  name: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateAreaInterestRequest {
  name: string;
  description?: string;
}

export interface AreaInterestsListParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export class AreaInterestsService {
  private static readonly BASE_PATH = "/api/app/area-interests";

  /**
   * Buscar lista de áreas de interesse disponíveis
   */
  static async getAreaInterests(
    params?: AreaInterestsListParams
  ): Promise<PaginationResponse<AreaInterest>> {
    try {
      ApiLogger.info("Buscando lista de áreas de interesse", params);

      const response = await firstValueFrom(
        apiClient.get<PaginationResponse<AreaInterest>>(this.BASE_PATH, {
          params: {
            Search: params?.search,
            Page: params?.page || 1,
            PageSize: params?.pageSize || 50
          }
        })
      );

      ApiLogger.info(`Encontradas ${response.data.length} áreas de interesse`);
      console.log(`✅ API Status: GET ${this.BASE_PATH} - 200 OK`);
      console.log(`📊 Area Interests Data: ${response.data.length} áreas de interesse encontradas`);

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar áreas de interesse", error as Error);
      console.log(`❌ API Status: GET ${this.BASE_PATH} - ${(error as any)?.status || "Unknown"} Error`);
      throw error;
    }
  }

  /**
   * Criar nova área de interesse
   */
  static async createAreaInterest(
    data: CreateAreaInterestRequest
  ): Promise<AreaInterest> {
    try {
      ApiLogger.info("Criando nova área de interesse", data);

      const response = await firstValueFrom(
        apiClient.post<AreaInterest>(this.BASE_PATH, data)
      );

      ApiLogger.info("Área de interesse criada com sucesso", {
        id: response.id,
        name: response.name
      });
      console.log(`✅ API Status: POST ${this.BASE_PATH} - 200 OK`);
      console.log(`📊 Area Interest Created: ${response.name} (ID: ${response.id})`);

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao criar área de interesse", error as Error);
      console.log(`❌ API Status: POST ${this.BASE_PATH} - ${(error as any)?.status || "Unknown"} Error`);
      throw error;
    }
  }
}

export default AreaInterestsService;
