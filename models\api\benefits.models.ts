/**
 * Modelos de dados para Benefícios baseados na API ClubM
 * Baseado na especificação OpenAPI do Swagger
 */

import { z } from "zod";
import { PaginationRequest, PaginationResponse } from "./common.models";

// Enums baseados na API
export enum BenefitStatus {
  Inactive = 0,
  Active = 1
}

export enum BenefitInteractionType {
  View = "view",
  Click = "click", 
  Redeem = "redeem"
}

// Interface principal para Benefit (baseada na API)
export interface Benefit {
  id: number;
  partnerId: number;
  title: string;
  description: string;
  terms?: string;
  imageUrl?: string;
  validUntil?: string;
  status: BenefitStatus;
  createdAt: string;
  updatedAt?: string;
  // Campos adicionais que podem vir da API
  discountPercentage?: number;
  originalPrice?: number;
  discountedPrice?: number;
  category?: string;
  tags?: string[];
  usageLimit?: number;
  usageCount?: number;
  isExclusive?: boolean;
  priority?: number;
}

// Parâmetros para listar benefícios
export interface BenefitListParams extends PaginationRequest {
  search?: string;
  status?: BenefitStatus;
  partnerId?: number;
  category?: string;
  validOnly?: boolean; // Apenas benefícios válidos (não expirados)
}

// Resposta da lista de benefícios
export interface BenefitListResponse extends PaginationResponse<Benefit> {}

// Resposta de detalhes do benefício
export interface BenefitDetailsResponse {
  benefit: Benefit;
  partner?: {
    id: number;
    name: string;
    logoUrl?: string;
  };
  relatedBenefits?: Benefit[];
}

// Request para criar benefício (admin)
export interface CreateBenefitRequest {
  partnerId: number;
  title: string;
  description: string;
  terms?: string;
  imageUrl?: string;
  validUntil?: string;
  discountPercentage?: number;
  originalPrice?: number;
  discountedPrice?: number;
  category?: string;
  tags?: string[];
  usageLimit?: number;
  isExclusive?: boolean;
  priority?: number;
  status?: BenefitStatus;
}

// Request para atualizar benefício (admin)
export interface UpdateBenefitRequest extends Partial<CreateBenefitRequest> {}

// Request para interação com benefício
export interface BenefitInteractionRequest {
  partnerId: number;
  benefitId: number;
  interactionType: BenefitInteractionType;
}

// Resposta da interação com benefício
export interface BenefitInteractionResponse {
  success: boolean;
  message?: string;
  data?: {
    interactionId?: string;
    points?: number;
    rewards?: any[];
  };
}

// Schemas de validação com Zod
export const CreateBenefitSchema = z.object({
  partnerId: z.number().positive("ID do parceiro deve ser positivo"),
  title: z.string().min(1, "Título é obrigatório").max(200, "Título muito longo"),
  description: z.string().min(1, "Descrição é obrigatória").max(1000, "Descrição muito longa"),
  terms: z.string().max(2000, "Termos muito longos").optional(),
  imageUrl: z.string().url("URL da imagem inválida").optional(),
  validUntil: z.string().datetime("Data de validade inválida").optional(),
  discountPercentage: z.number().min(0).max(100, "Desconto deve estar entre 0 e 100%").optional(),
  originalPrice: z.number().min(0, "Preço original não pode ser negativo").optional(),
  discountedPrice: z.number().min(0, "Preço com desconto não pode ser negativo").optional(),
  category: z.string().max(100, "Categoria muito longa").optional(),
  tags: z.array(z.string()).optional(),
  usageLimit: z.number().min(1, "Limite de uso deve ser positivo").optional(),
  isExclusive: z.boolean().optional(),
  priority: z.number().min(0, "Prioridade não pode ser negativa").optional(),
  status: z.nativeEnum(BenefitStatus).optional()
});

export const UpdateBenefitSchema = CreateBenefitSchema.partial();

export const BenefitListParamsSchema = z.object({
  page: z.number().min(1, "Página deve ser maior que 0").optional(),
  pageSize: z.number().min(1, "Tamanho da página deve ser maior que 0").max(100, "Tamanho da página muito grande").optional(),
  search: z.string().optional(),
  status: z.nativeEnum(BenefitStatus).optional(),
  partnerId: z.number().positive("ID do parceiro deve ser positivo").optional(),
  category: z.string().optional(),
  validOnly: z.boolean().optional()
});

export const BenefitInteractionSchema = z.object({
  partnerId: z.number().positive("ID do parceiro deve ser positivo"),
  benefitId: z.number().positive("ID do benefício deve ser positivo"),
  interactionType: z.nativeEnum(BenefitInteractionType)
});

// Tipos derivados dos schemas
export type CreateBenefitData = z.infer<typeof CreateBenefitSchema>;
export type UpdateBenefitData = z.infer<typeof UpdateBenefitSchema>;
export type BenefitListParamsData = z.infer<typeof BenefitListParamsSchema>;
export type BenefitInteractionData = z.infer<typeof BenefitInteractionSchema>;

// Funções de validação
export const validateCreateBenefit = (data: any): string[] => {
  const result = CreateBenefitSchema.safeParse(data);
  if (result.success) return [];
  
  return result.error.errors.map(err => err.message);
};

export const validateUpdateBenefit = (data: any): string[] => {
  const result = UpdateBenefitSchema.safeParse(data);
  if (result.success) return [];
  
  return result.error.errors.map(err => err.message);
};

export const validateBenefitInteraction = (data: any): string[] => {
  const result = BenefitInteractionSchema.safeParse(data);
  if (result.success) return [];
  
  return result.error.errors.map(err => err.message);
};
