/**
 * Hook for fetching terms by ID
 * Used for events and products that have termId
 */

import {useQuery} from "@tanstack/react-query";
import {TermsService} from "@/services/api/terms/terms.service";
import {ApiTerm} from "@/models/api/terms.models";

/**
 * Hook para buscar um termo específico por ID
 */
export const useTermById = (termId: number | null | undefined) => {
  return useQuery({
    queryKey: ["term", termId],
    queryFn: () => TermsService.getTermById(termId!),
    enabled: !!termId && termId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook para buscar termo de evento baseado no termId do evento
 */
export const useEventTerm = (event: any) => {
  const termId = event?.termId;
  
  return useTermById(termId);
};

/**
 * Hook para buscar termo de produto baseado no termId do produto
 */
export const useProductTerm = (product: any) => {
  const termId = product?.termId;
  
  return useTermById(termId);
};
