/**
 * Serviço de Usuários para ClubM
 * Implementa operações para gerenciamento de perfil e dados do usuário
 */

import {apiClient} from "../base/api-client";
import {
  UserInfo,
  UserPreferences,
  NotificationPreferences
} from "@/models/api/auth.models";
import {
  UserBadgesResponse,
  UserBadgesParams,
  UserBadgeStats
} from "@/models/api/user-badges.models";
import {PaginationResponse} from "@/models/api/common.models";
import {ApiLogger} from "../base/api-logger";
import {FormDataClient} from "../base/form-data-client";
import {firstValueFrom} from "rxjs";

// Interfaces específicas para usuários
export interface UpdateUserProfileRequest {
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
  specializations?: number[];
  areaInterests?: number[];
  objectives?: number[];
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateNotificationPreferencesRequest {
  email?: boolean;
  push?: boolean;
  sms?: boolean;
  marketing?: boolean;
  events?: boolean;
  chats?: boolean;
}

export interface UserAddress {
  id?: string;
  type: "home" | "work" | "other";
  label?: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

export interface UserPaymentMethod {
  id: string;
  type: "credit_card" | "debit_card" | "pix" | "bank_transfer";
  label: string;
  lastFourDigits?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
}

export interface UserActivity {
  id: string;
  type:
    | "login"
    | "logout"
    | "profile_update"
    | "password_change"
    | "event_registration"
    | "purchase";
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

export interface UserStats {
  eventsAttended: number;
  eventsRegistered: number;
  totalPurchases: number;
  totalSpent: number;
  memberSince: string;
  lastActivity: string;
}

export interface DeleteAccountRequest {
  password: string;
  reason?: string;
  feedback?: string;
}

export interface UsersListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export interface CreateUserRequest {
  // Campos obrigatórios da API
  Name: string;
  Document: string;
  BirthDate: string; // ISO date string
  Sex: number; // 0 = Feminino, 1 = Masculino, 2 = Outro
  FranchiseId: number; // Obtido via GET /api/app/plans/{id}/franchises
  PlanId: number; // Obtido via GET /api/app/plans
  Profession: string;
  CompanyName: string;
  CompanyRole: string;
  CompanySegmentId: number; // Obtido via GET /api/app/opportunity-segment
  AddressState: string;
  AddressCity: string;
  AddressNeighborhood: string;
  AddressStreet: string;
  AddressNumber: string;
  AddressZip: string;
  AddressComplement: string;
  Email: string;
  PhoneNumber: string;
  WhereMet: number; // Enum value
  Facebook: string;
  Instagram: string;
  LinkedIn: string;
  Website: string;
  documents?: any[];
}

export class UsersService {
  private static readonly BASE_PATH = "/api/users";
  private static readonly APP_BASE_PATH = "/api/app/users";

  /**
   * Buscar perfil do usuário atual
   */
  static async getCurrentUser(): Promise<UserInfo> {
    try {
      ApiLogger.info("Buscando perfil do usuário atual");

      // Usar o endpoint da API app que contém os dados completos do perfil
      const response = await firstValueFrom(
        apiClient.get<UserInfo>(`${this.APP_BASE_PATH}/@me`)
      );

      ApiLogger.info("Perfil do usuário carregado", {
        userId: response.id,
        name: response.name,
        specializations: response.specializations?.length || 0,
        areaInterests: response.areaInterests?.length || 0,
        objectives: response.objectives?.length || 0
      });

      // Log status da request no console
      console.log(`✅ API Status: GET /api/app/users/@me - 200 OK`);
      console.log(`📊 User Data: ${response.name} (ID: ${response.id})`);
      console.log(`📊 Profile Data:`, {
        specializations: response.specializations,
        areaInterests: response.areaInterests,
        objectives: response.objectives
      });

      return response;
    } catch (error: any) {
      // Se for erro 401, não fazer log de erro (é esperado quando não autenticado)
      if (error?.status === 401) {
        ApiLogger.warn("Usuário não autenticado");
        console.log(`⚠️ API Status: GET /api/app/users/@me - 401 Unauthorized`);
      } else {
        ApiLogger.error("Erro ao buscar perfil do usuário", error as Error);
        console.log(
          `❌ API Status: GET /api/app/users/@me - ${
            error?.status || "Unknown"
          } Error`
        );
      }
      throw error;
    }
  }

  /**
   * Criar novo usuário
   */
  static async createUser(userData: CreateUserRequest): Promise<UserInfo> {
    try {
      ApiLogger.info("Criando novo usuário", {
        name: userData.Name,
        email: userData.Email,
        document: userData.Document
      });

      const formData = new FormData();

      const {documents, ...userFields} = userData;

      Object.entries(userFields).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(
            key,
            typeof value === "number" ? value.toString() : value
          );
        }
      });

      if (documents && documents.length > 0) {
        documents.forEach((document, index) => {
          const finalUri = document.processedUri || document.uri;
          const finalType = document.processedUri
            ? "image/webp"
            : document.type;

          const documentFile = {
            uri: finalUri,
            type: finalType,
            name: document.name
          };

          formData.append("documents", documentFile as any);
        });
      }

      const response = await FormDataClient.post<UserInfo>(
        this.APP_BASE_PATH,
        formData
      );

      ApiLogger.info("Usuário criado com sucesso", {
        userId: response.data.id,
        name: response.data.name,
        email: response.data.email
      });

      return response.data;
    } catch (error) {
      ApiLogger.error("Erro ao criar usuário", error as Error, userData);
      throw error;
    }
  }

  /**
   * Buscar lista de usuários
   */
  static async getUsers(
    params?: UsersListParams
  ): Promise<PaginationResponse<UserInfo>> {
    try {
      ApiLogger.info("Buscando lista de usuários", params);

      const apiParams = {
        Search: params?.search,
        Page: params?.page,
        PageSize: params?.pageSize
      };

      const response = await firstValueFrom(
        apiClient.get<PaginationResponse<UserInfo>>(
          this.APP_BASE_PATH,
          apiParams
        )
      );

      ApiLogger.info(`Encontrados ${response.data.length} usuários`);

      // Log status da request no console
      console.log(`✅ API Status: GET /api/app/users - 200 OK`);
      console.log(
        `📊 Users Data: ${response.data.length} usuários encontrados`
      );

      return response;
    } catch (error: any) {
      ApiLogger.error(
        "Erro ao buscar lista de usuários",
        error as Error,
        params
      );

      // Log error status no console
      console.log(
        `❌ API Status: GET /api/app/users - ${
          error?.status || "Unknown"
        } Error`
      );
      console.log(`📊 Error: ${error?.message || "Unknown error"}`);

      throw error;
    }
  }

  /**
   * Buscar perfil de um usuário específico por ID
   */
  static async getUserById(userId: string | number): Promise<UserInfo> {
    try {
      ApiLogger.info("Buscando perfil do usuário por ID", {userId});

      const response = await firstValueFrom(
        apiClient.get<UserInfo>(`${this.APP_BASE_PATH}/${userId}`)
      );

      ApiLogger.info("Perfil do usuário carregado", {
        userId: response.id,
        name: response.name
      });

      // Log status da request no console
      console.log(`✅ API Status: GET /api/app/users/${userId} - 200 OK`);
      console.log(`📊 User Data: ${response.name} (ID: ${response.id})`);

      return response;
    } catch (error: any) {
      ApiLogger.error(
        "Erro ao buscar perfil do usuário por ID",
        error as Error,
        {userId}
      );
      console.log(
        `❌ API Status: GET /api/app/users/${userId} - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Atualizar perfil do usuário
   */
  static async updateProfile(
    updates: UpdateUserProfileRequest
  ): Promise<UserInfo> {
    try {
      ApiLogger.info("Atualizando perfil do usuário", updates);

      // Criar FormData para enviar como multipart/form-data
      // No React Native, precisamos usar uma implementação específica
      const formData = new FormData();

      // Adicionar campos obrigatórios do perfil ao FormData
      if (updates.name !== undefined && updates.name.trim() !== "") {
        formData.append("name", updates.name.trim());
      }
      if (updates.email !== undefined && updates.email.trim() !== "") {
        formData.append("email", updates.email.trim());
      }

      // Campos opcionais - só incluir se não estiverem vazios
      if (updates.phone !== undefined && updates.phone.trim() !== "") {
        formData.append("phone", updates.phone.trim());
      }

      if (updates.avatar !== undefined && updates.avatar.trim() !== "") {
        const avatarFile = {
          uri: `data:image/webp;base64,${updates.avatar.trim()}`,
          type: "image/webp",
          name: "avatar.webp"
        };

        console.log("🖼️ [USERS-SERVICE] Preparando avatar para envio:", {
          hasBase64: !!updates.avatar,
          base64Length: updates.avatar.length,
          fileType: avatarFile.type,
          fileName: avatarFile.name
        });

        formData.append("avatar", avatarFile as any);
      }

      // Campos de perfil profissional
      if (updates.specializations !== undefined) {
        console.log(
          "🔧 [USERS-SERVICE] Adicionando specializations ao FormData:",
          updates.specializations
        );
        // API espera objetos com a propriedade id (ex.: specializations[0].id = 4)
        updates.specializations.forEach((id, index) => {
          formData.append(`specializations[${index}].id`, id.toString());
          console.log(`📝 FormData: specializations[${index}].id = ${id}`);
        });
      }

      if (updates.areaInterests !== undefined) {
        console.log(
          "🔧 [USERS-SERVICE] Adicionando areaInterests ao FormData:",
          updates.areaInterests
        );
        // API espera objetos com a propriedade id (ex.: areaInterests[0].id = 4)
        updates.areaInterests.forEach((id, index) => {
          formData.append(`areaInterests[${index}].id`, id.toString());
          console.log(`📝 FormData: areaInterests[${index}].id = ${id}`);
        });
      }

      if (updates.objectives !== undefined) {
        console.log(
          "🔧 [USERS-SERVICE] Adicionando objectives ao FormData:",
          updates.objectives
        );
        // API espera objetos com a propriedade id (ex.: objectives[0].id = 4)
        updates.objectives.forEach((id, index) => {
          formData.append(`objectives[${index}].id`, id.toString());
          console.log(`📝 FormData: objectives[${index}].id = ${id}`);
        });
      }

      const response = await FormDataClient.put<UserInfo>(
        `${this.APP_BASE_PATH}/@me`,
        formData
      );

      ApiLogger.info("Perfil atualizado com sucesso", {
        userId: response.data.id
      });

      // Log status da request no console
      console.log(`✅ API Status: PUT /api/app/users/@me - 200 OK`);
      console.log(
        `📊 Profile Updated: ${response.data.name} (ID: ${response.data.id})`
      );
      console.log("📊 Profile Response Data:", {
        specializations: response.data.specializations,
        areaInterests: response.data.areaInterests,
        objectives: response.data.objectives
      });

      return response.data;
    } catch (error: any) {
      ApiLogger.error("Erro ao atualizar perfil", error as Error);

      // Log error status no console
      console.log(
        `❌ API Status: PUT /api/app/users/@me - ${
          error?.status || "Unknown"
        } Error`
      );
      console.log(`📊 Error: ${error?.message || "Unknown error"}`);

      throw error;
    }
  }

  /**
   * Alterar senha do usuário
   * Como não existe endpoint específico para alterar senha quando logado,
   * vamos usar o fluxo de reset de senha
   */
  static async changePassword(_request: ChangePasswordRequest): Promise<void> {
    try {
      ApiLogger.info("Alterando senha do usuário");

      // Como não existe endpoint /auth/change-password na API,
      // vamos implementar uma solução alternativa usando o fluxo de reset
      // Por enquanto, vamos lançar um erro informativo
      throw new Error(
        "Funcionalidade de alteração de senha não está disponível. Use a opção 'Esqueci minha senha' para redefinir sua senha."
      );
    } catch (error) {
      ApiLogger.error("Erro ao alterar senha", error as Error);
      throw error;
    }
  }

  /**
   * Buscar preferências de notificação
   */
  static async getNotificationPreferences(): Promise<NotificationPreferences> {
    try {
      ApiLogger.info("Buscando preferências de notificação");

      const response = await firstValueFrom(
        apiClient.get<NotificationPreferences>(
          `${this.BASE_PATH}/@me/notifications`
        )
      );

      ApiLogger.info("Preferências de notificação carregadas");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar preferências de notificação",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Atualizar preferências de notificação
   */
  static async updateNotificationPreferences(
    preferences: UpdateNotificationPreferencesRequest
  ): Promise<NotificationPreferences> {
    try {
      ApiLogger.info("Atualizando preferências de notificação", preferences);

      const response = await firstValueFrom(
        apiClient.put<NotificationPreferences>(
          `${this.BASE_PATH}/@me/notifications`,
          preferences
        )
      );

      ApiLogger.info("Preferências de notificação atualizadas");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao atualizar preferências de notificação",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Buscar endereços do usuário
   */
  static async getUserAddresses(): Promise<UserAddress[]> {
    try {
      ApiLogger.info("Buscando endereços do usuário");

      const response = await firstValueFrom(
        apiClient.get<UserAddress[]>(`${this.BASE_PATH}/@me/addresses`)
      );

      ApiLogger.info(`Encontrados ${response.length} endereços`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar endereços", error as Error);
      throw error;
    }
  }

  /**
   * Adicionar novo endereço
   */
  static async addAddress(
    address: Omit<UserAddress, "id">
  ): Promise<UserAddress> {
    try {
      ApiLogger.info("Adicionando novo endereço", address);

      const response = await firstValueFrom(
        apiClient.post<UserAddress>(`${this.BASE_PATH}/@me/addresses`, address)
      );

      ApiLogger.info("Endereço adicionado com sucesso", {
        addressId: response.id
      });
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao adicionar endereço", error as Error);
      throw error;
    }
  }

  /**
   * Atualizar endereço existente
   */
  static async updateAddress(
    addressId: string,
    updates: Partial<UserAddress>
  ): Promise<UserAddress> {
    try {
      ApiLogger.info("Atualizando endereço", {addressId, updates});

      const response = await firstValueFrom(
        apiClient.put<UserAddress>(
          `${this.BASE_PATH}/@me/addresses/${addressId}`,
          updates
        )
      );

      ApiLogger.info("Endereço atualizado com sucesso", {addressId});
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao atualizar endereço", error as Error, {
        addressId
      });
      throw error;
    }
  }

  /**
   * Remover endereço
   */
  static async deleteAddress(addressId: string): Promise<void> {
    try {
      ApiLogger.info("Removendo endereço", {addressId});

      await firstValueFrom(
        apiClient.delete(`${this.BASE_PATH}/@me/addresses/${addressId}`)
      );

      ApiLogger.info("Endereço removido com sucesso", {addressId});
    } catch (error) {
      ApiLogger.error("Erro ao remover endereço", error as Error, {addressId});
      throw error;
    }
  }

  /**
   * Buscar métodos de pagamento do usuário
   */
  static async getPaymentMethods(): Promise<UserPaymentMethod[]> {
    try {
      ApiLogger.info("Buscando métodos de pagamento");

      const response = await firstValueFrom(
        apiClient.get<UserPaymentMethod[]>(
          `${this.BASE_PATH}/@me/payment-methods`
        )
      );

      ApiLogger.info(`Encontrados ${response.length} métodos de pagamento`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar métodos de pagamento", error as Error);
      throw error;
    }
  }

  /**
   * Remover método de pagamento
   */
  static async deletePaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      ApiLogger.info("Removendo método de pagamento", {paymentMethodId});

      await firstValueFrom(
        apiClient.delete(
          `${this.BASE_PATH}/@me/payment-methods/${paymentMethodId}`
        )
      );

      ApiLogger.info("Método de pagamento removido", {paymentMethodId});
    } catch (error) {
      ApiLogger.error("Erro ao remover método de pagamento", error as Error, {
        paymentMethodId
      });
      throw error;
    }
  }

  /**
   * Buscar atividades do usuário
   */
  static async getUserActivity(params?: {
    page?: number;
    pageSize?: number;
    type?: string;
  }): Promise<{activities: UserActivity[]; totalCount: number}> {
    try {
      ApiLogger.info("Buscando atividades do usuário", params);

      const response = await firstValueFrom(
        apiClient.get<{activities: UserActivity[]; totalCount: number}>(
          `${this.BASE_PATH}/@me/activity`,
          params
        )
      );

      ApiLogger.info(`Encontradas ${response.activities.length} atividades`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar atividades", error as Error);
      throw error;
    }
  }

  /**
   * Buscar estatísticas do usuário
   * NOTA: Endpoint /stats não existe na API, retornando dados mock
   */
  static async getUserStats(): Promise<UserStats> {
    try {
      ApiLogger.info("Calculando estatísticas do usuário (dados mock)");

      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Retornar dados mock já que o endpoint não existe
      const mockStats: UserStats = {
        eventsAttended: 0,
        eventsRegistered: 0,
        totalPurchases: 0,
        totalSpent: 0,
        memberSince: new Date().toISOString(),
        lastActivity: new Date().toISOString()
      };

      ApiLogger.warn(
        "Endpoint /api/users/@me/stats não existe na API, retornando dados mock"
      );
      console.log(
        "⚠️ API Status: GET /api/users/@me/stats - 404 Not Found (Endpoint não implementado)"
      );

      return mockStats;
    } catch (error) {
      ApiLogger.error("Erro ao buscar estatísticas", error as Error);
      throw error;
    }
  }

  /**
   * Upload de avatar
   */
  static async uploadAvatar(file: FormData): Promise<{avatarUrl: string}> {
    try {
      ApiLogger.info("Fazendo upload de avatar");

      // Use FormDataClient for proper FormData handling in React Native
      const response = await FormDataClient.post<{avatarUrl: string}>(
        `${this.BASE_PATH}/@me/avatar`,
        file
      );

      ApiLogger.info("Avatar atualizado com sucesso", {
        avatarUrl: response.data.avatarUrl
      });
      return response.data;
    } catch (error) {
      ApiLogger.error("Erro ao fazer upload de avatar", error as Error);
      throw error;
    }
  }

  /**
   * Verificar se usuário existe por documento
   */
  static async checkUserExists(document: string): Promise<{exists: boolean}> {
    try {
      ApiLogger.info("Verificando se usuário existe", {document});

      // Usar endpoint do app para verificar usuário por documento
      const response = await firstValueFrom(
        apiClient.get<{exists: boolean}>(`${this.APP_BASE_PATH}/${document}`)
      );

      // O endpoint retorna {exists: boolean} diretamente na response
      const exists = response.exists || false;
      ApiLogger.info("Verificação de usuário concluída", {
        document,
        exists
      });
      return {exists};
    } catch (error: any) {
      // Se for 404, o usuário não existe
      if (error?.status === 404) {
        ApiLogger.info("Usuário não encontrado", {document});
        return {exists: false};
      }

      // Para outros erros, propagar
      ApiLogger.error("Erro ao verificar usuário", error as Error, {document});
      throw error;
    }
  }

  /**
   * Solicitar exclusão de conta
   */
  static async deleteAccount(request: DeleteAccountRequest): Promise<void> {
    try {
      ApiLogger.info("Solicitando exclusão de conta");

      await firstValueFrom(apiClient.delete(`${this.BASE_PATH}/@me`, request));

      ApiLogger.info("Solicitação de exclusão enviada");
    } catch (error) {
      ApiLogger.error("Erro ao solicitar exclusão de conta", error as Error);
      throw error;
    }
  }

  /**
   * Buscar badges/títulos do usuário a partir dos dados do perfil
   * Utiliza o endpoint /api/app/users/@me que já retorna badges do usuário
   */
  static async getUserBadges(
    params?: UserBadgesParams
  ): Promise<UserBadgesResponse> {
    try {
      ApiLogger.info("Buscando badges do usuário a partir do perfil");

      // Buscar dados do usuário que já incluem badges
      const userInfo = await this.getCurrentUser();

      // Log status da request no console
      console.log(
        `✅ API Status: GET /api/app/users/@me - 200 OK (via getUserBadges)`
      );

      // Extrair badges dos dados do usuário
      const badges = userInfo.badges || [];

      const transformedBadges = badges.map((badge: any, index: number) => ({
        id: badge.id || `badge-${index}`,
        title: badge.name || badge.title || `Título ${index + 1}`,
        description: badge.description || "Título conquistado",
        category: "achievement" as const,
        type: badge.type ?? 0, // Manter o tipo original da API (padrão 0 = Bronze)
        level: badge.level ?? 0, // Manter o nível original da API (padrão 0 = Bronze)
        earnedAt: badge.createdAt || badge.earnedAt || new Date().toISOString(),
        imageUrl: badge.imageUrl,
        iconUrl: badge.iconUrl
      }));

      ApiLogger.info("Badges do usuário carregados com sucesso", {
        count: transformedBadges.length
      });
      console.log(
        `📊 Badges Data: ${transformedBadges.length} badges encontrados no perfil do usuário`
      );

      return {
        data: transformedBadges,
        totalCount: transformedBadges.length,
        currentPage: 1,
        pageSize: params?.pageSize || 10,
        hasNextPage: false,
        hasPreviousPage: false
      };
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar badges do usuário", error as Error);
      console.log(
        `❌ API Status: GET /api/app/users/@me - ${
          error?.status || "Unknown"
        } Error`
      );

      // Retornar resposta vazia em caso de erro
      return {
        data: [],
        totalCount: 0,
        currentPage: 1,
        pageSize: params?.pageSize || 10,
        hasNextPage: false,
        hasPreviousPage: false
      };
    }
  }

  /**
   * Buscar objetivos do usuário a partir dos dados do perfil
   * Utiliza o endpoint /api/app/users/@me que já retorna objetivos do usuário
   */
  static async getUserObjectives(): Promise<any[]> {
    try {
      ApiLogger.info("Buscando objetivos do usuário a partir do perfil");

      // Buscar dados do usuário que já incluem objetivos
      const userInfo = await this.getCurrentUser();

      // Log status da request no console
      console.log(
        `✅ API Status: GET /api/app/users/@me - 200 OK (via getUserObjectives)`
      );

      // Extrair objetivos dos dados do usuário
      const objectives = userInfo.objectives || [];

      ApiLogger.info("Objetivos do usuário carregados com sucesso", {
        count: objectives.length
      });
      console.log(
        `📊 Objectives Data: ${objectives.length} objetivos encontrados no perfil do usuário`
      );

      return objectives;
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar objetivos do usuário", error as Error);
      console.log(
        `❌ API Status: GET /api/app/users/@me - ${
          error?.status || "Unknown"
        } Error`
      );

      // Retornar array vazio em caso de erro
      return [];
    }
  }

  /**
   * Buscar estatísticas de badges do usuário baseadas nos dados reais
   * Calcula estatísticas a partir dos badges obtidos da API
   */
  static async getUserBadgeStats(): Promise<UserBadgeStats> {
    try {
      // Buscar badges primeiro
      const badgesResponse = await this.getUserBadges();
      const badges = badgesResponse.data;

      ApiLogger.info("Calculando estatísticas de badges", {
        totalBadges: badges.length
      });

      // Calcular estatísticas por categoria
      const badgesByCategory = badges.reduce((acc, badge) => {
        acc[badge.category] = (acc[badge.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Calcular estatísticas por tipo
      const badgesByType = badges.reduce((acc, badge) => {
        acc[badge.type] = (acc[badge.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Encontrar o badge mais recente
      const sortedBadges = [...badges].sort(
        (a, b) =>
          new Date(b.earnedAt).getTime() - new Date(a.earnedAt).getTime()
      );
      const latestBadge = sortedBadges[0];

      console.log(`✅ Badge Stats: ${badges.length} badges calculados`);

      return {
        totalBadges: badges.length,
        badgesByCategory,
        badgesByType,
        latestBadge
      };
    } catch (error: any) {
      ApiLogger.error(
        "Erro ao calcular estatísticas de badges",
        error as Error
      );
      console.log(`❌ Badge Stats Error: ${error?.message || "Unknown"}`);

      // Retornar estatísticas vazias em caso de erro
      return {
        totalBadges: 0,
        badgesByCategory: {} as any,
        badgesByType: {} as any
      };
    }
  }
}

export default UsersService;
