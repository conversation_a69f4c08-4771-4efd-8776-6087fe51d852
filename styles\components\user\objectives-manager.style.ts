import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: "#1a1a1a"
  },
  
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20
  },
  
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#ffffff"
  },
  
  addButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8
  },
  
  addButtonText: {
    color: "#ffffff",
    fontWeight: "600",
    fontSize: 16
  },
  
  form: {
    backgroundColor: "#2a2a2a",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20
  },
  
  formTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 16
  },
  
  input: {
    backgroundColor: "#3a3a3a",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    color: "#ffffff",
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#4a4a4a"
  },
  
  textArea: {
    height: 80,
    textAlignVertical: "top"
  },
  
  formActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16
  },
  
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 4
  },
  
  cancelButton: {
    backgroundColor: "#6a6a6a"
  },
  
  cancelButtonText: {
    color: "#ffffff",
    fontWeight: "600",
    fontSize: 16
  },
  
  saveButton: {
    backgroundColor: "#007AFF"
  },
  
  saveButtonText: {
    color: "#ffffff",
    fontWeight: "600",
    fontSize: 16
  },
  
  objectivesList: {
    flex: 1
  },
  
  objectiveItem: {
    backgroundColor: "#2a2a2a",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start"
  },
  
  objectiveContent: {
    flex: 1,
    marginRight: 12
  },
  
  objectiveTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 4
  },
  
  objectiveDescription: {
    fontSize: 14,
    color: "#cccccc",
    marginBottom: 8,
    lineHeight: 20
  },
  
  objectiveValue: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500"
  },
  
  objectiveActions: {
    flexDirection: "column",
    gap: 8
  },
  
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    minWidth: 60,
    alignItems: "center"
  },
  
  editButton: {
    backgroundColor: "#007AFF"
  },
  
  editButtonText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600"
  },
  
  deleteButton: {
    backgroundColor: "#FF3B30"
  },
  
  deleteButtonText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600"
  }
});
