/**
 * Deep Link Handler Component
 * Handles deep link integration with authentication flow
 */

import React, { useEffect } from 'react';
import { useDeepLinkAuth } from '@/hooks/use-deep-link-auth';
import { ApiLogger } from '@/services/api/base/api-logger';

interface DeepLinkHandlerProps {
  children?: React.ReactNode;
}

/**
 * Component that handles deep link authentication flow
 * Should be placed high in the component tree, after AuthProvider and DeepLinkProvider
 */
export function DeepLinkHandler({ children }: DeepLinkHandlerProps) {
  const {
    pendingDeepLink,
    hasPendingDeepLink,
    needsBiometricAuth,
    needsLogin,
    canProcessDeepLink,
    triggerBiometricAuth,
    cancelPendingDeepLink
  } = useDeepLinkAuth({
    autoProcessPending: true,
    redirectToLogin: true,
    onDeepLinkProcessed: (route) => {
      ApiLogger.info('Deep link processed successfully', { route: route.type });
    },
    onAuthRequired: (pendingRoute) => {
      ApiLogger.info('Deep link requires authentication', { route: pendingRoute.type });
    }
  });

  /**
   * Log deep link state changes for debugging
   */
  useEffect(() => {
    if (hasPendingDeepLink) {
      ApiLogger.info('Deep link handler state', {
        routeType: pendingDeepLink?.type,
        needsBiometricAuth,
        needsLogin,
        canProcessDeepLink
      });
    }
  }, [hasPendingDeepLink, pendingDeepLink, needsBiometricAuth, needsLogin, canProcessDeepLink]);

  // This component doesn't render anything visible
  // It just handles the deep link authentication flow
  return <>{children}</>;
}

export default DeepLinkHandler;
