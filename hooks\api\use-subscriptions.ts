import {useMutation} from "@tanstack/react-query";
import SubscriptionsService, {
  CreateSubscriptionRequest,
  Subscription
} from "@/services/api/subscriptions/subscriptions.service";

export const useCreateSubscription = () => {
  return useMutation({
    mutationFn: (data: CreateSubscriptionRequest) =>
      SubscriptionsService.createSubscription(data)
  });
};

export type {CreateSubscriptionRequest, Subscription};
