import {useState, useCallback, useMemo} from "react";
import {z} from "zod";
import {validateRegistrationStep} from "@/utils/registration-validation";

interface UseRegistrationValidationProps<T> {
  schema: z.ZodSchema<T>;
  initialData: T;
  onValidSubmit?: (data: T) => void | Promise<void>;
}

interface UseRegistrationValidationReturn<T> {
  data: T;
  errors: Record<string, string>;
  isValid: boolean;
  hasErrors: boolean;
  isSubmitting: boolean;
  updateField: (field: keyof T, value: any) => void;
  updateData: (newData: Partial<T>) => void;
  validate: () => boolean;
  validateField: (field: keyof T) => boolean;
  clearError: (field: keyof T) => void;
  clearAllErrors: () => void;
  handleSubmit: () => boolean;
  handleSubmitAsync: () => Promise<boolean>;
  reset: () => void;
}

export function useRegistrationValidation<T>({
  schema,
  initialData,
  onValidSubmit
}: UseRegistrationValidationProps<T>): UseRegistrationValidationReturn<T> {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update a single field
  const updateField = useCallback(
    (field: keyof T, value: any) => {
      setData((prev) => ({...prev, [field]: value}));

      // Mark field as touched
      setTouched((prev) => ({...prev, [field as string]: true}));

      // Clear error for this field when user starts typing
      if (errors[field as string]) {
        setErrors((prev) => {
          const newErrors = {...prev};
          delete newErrors[field as string];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Update multiple fields at once
  const updateData = useCallback((newData: Partial<T>) => {
    setData((prev) => ({...prev, ...newData}));

    // Mark updated fields as touched
    const updatedFields = Object.keys(newData);
    setTouched((prev) => {
      const newTouched = {...prev};
      updatedFields.forEach((field) => {
        newTouched[field] = true;
      });
      return newTouched;
    });
  }, []);

  // Validate all fields
  const validate = useCallback((): boolean => {
    const validation = validateRegistrationStep(schema, data);
    setErrors(validation.errors);
    return validation.isValid;
  }, [schema, data]);

  // Validate a single field
  const validateField = useCallback(
    (field: keyof T): boolean => {
      try {
        // Create a partial schema for just this field
        const fieldValue = data[field];
        const fieldSchema = schema.shape?.[field as string];

        if (fieldSchema) {
          fieldSchema.parse(fieldValue);
          // Clear error if validation passes
          setErrors((prev) => {
            const newErrors = {...prev};
            delete newErrors[field as string];
            return newErrors;
          });
          return true;
        }
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Use a default Portuguese message since this hook doesn't have access to translation
          const errorMessage = error.errors[0]?.message || "Campo inválido";
          setErrors((prev) => ({...prev, [field as string]: errorMessage}));
          return false;
        }
        return false;
      }
    },
    [schema, data]
  );

  // Clear error for a specific field
  const clearError = useCallback((field: keyof T) => {
    setErrors((prev) => {
      const newErrors = {...prev};
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Handle form submission
  const handleSubmit = useCallback((): boolean => {
    const isValid = validate();
    if (isValid && onValidSubmit) {
      // fire-and-forget for backward compatibility
      Promise.resolve(onValidSubmit(data)).catch(() => {});
    }
    return isValid;
  }, [validate, onValidSubmit, data]);

  const handleSubmitAsync = useCallback(async (): Promise<boolean> => {
    const isValid = validate();
    if (!isValid) return false;
    if (!onValidSubmit) return true;
    setIsSubmitting(true);
    try {
      await Promise.resolve(onValidSubmit(data));
      return true;
    } catch (e) {
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [validate, onValidSubmit, data]);

  // Reset form to initial state
  const reset = useCallback(() => {
    setData(initialData);
    setErrors({});
    setTouched({});
  }, [initialData]);

  // Computed values
  const isValid = useMemo(() => {
    const validation = validateRegistrationStep(schema, data);
    return validation.isValid;
  }, [schema, data]);

  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  return {
    data,
    errors,
    isValid,
    hasErrors,
    isSubmitting,
    updateField,
    updateData,
    validate,
    validateField,
    clearError,
    clearAllErrors,
    handleSubmit,
    handleSubmitAsync,
    reset
  };
}

// Specialized hooks for each registration step
export const useSubscriptionValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    SubscriptionRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: SubscriptionRegistrationSchema,
    initialData,
    onValidSubmit
  });
};

export const usePersonalDataValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    PersonalDataRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: PersonalDataRegistrationSchema,
    initialData,
    onValidSubmit
  });
};

export const useProfessionalValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    ProfessionalRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: ProfessionalRegistrationSchema,
    initialData,
    onValidSubmit
  });
};

export const useAddressValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    AddressRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: AddressRegistrationSchema,
    initialData,
    onValidSubmit
  });
};

export const useContactsSocialValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    ContactsSocialRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: ContactsSocialRegistrationSchema,
    initialData,
    onValidSubmit
  });
};

export const useDocumentUploadTermsValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    DocumentUploadTermsSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: DocumentUploadTermsSchema,
    initialData,
    onValidSubmit
  });
};

export const usePaymentCardValidation = (
  initialData: any,
  onValidSubmit?: (data: any) => void
) => {
  const {
    PaymentCardRegistrationSchema
  } = require("@/utils/registration-validation");
  return useRegistrationValidation({
    schema: PaymentCardRegistrationSchema,
    initialData,
    onValidSubmit
  });
};
