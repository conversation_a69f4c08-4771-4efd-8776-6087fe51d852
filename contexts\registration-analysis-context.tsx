import React, {createContext, useCallback, useContext, useEffect, useMemo, useState} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Persistent key to remember that the registration finished and the account is under analysis
const STORAGE_KEY = "@club_m_registration_under_analysis";

export interface RegistrationAnalysisContextType {
  isUnderAnalysis: boolean;
  activate: () => Promise<void>;
  deactivate: () => Promise<void>;
  isLoading: boolean;
}

const RegistrationAnalysisContext = createContext<RegistrationAnalysisContextType | undefined>(
  undefined
);

export const RegistrationAnalysisProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [isUnderAnalysis, setIsUnderAnalysis] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const load = async () => {
      try {
        const stored = await AsyncStorage.getItem(STORAGE_KEY);
        setIsUnderAnalysis(stored === "1");
      } catch (e) {
        // noop
      } finally {
        setIsLoading(false);
      }
    };
    load();
  }, []);

  const activate = useCallback(async () => {
    setIsUnderAnalysis(true);
    try {
      await AsyncStorage.setItem(STORAGE_KEY, "1");
    } catch {}
  }, []);

  const deactivate = useCallback(async () => {
    setIsUnderAnalysis(false);
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch {}
  }, []);

  const value = useMemo(
    () => ({isUnderAnalysis, activate, deactivate, isLoading}),
    [isUnderAnalysis, activate, deactivate, isLoading]
  );

  return (
    <RegistrationAnalysisContext.Provider value={value}>
      {children}
    </RegistrationAnalysisContext.Provider>
  );
};

export const useRegistrationAnalysis = () => {
  const ctx = useContext(RegistrationAnalysisContext);
  if (!ctx) throw new Error("useRegistrationAnalysis must be used within RegistrationAnalysisProvider");
  return ctx;
};

