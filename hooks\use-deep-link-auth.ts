/**
 * Deep Link Authentication Hook
 * Integrates deep linking with authentication flow and biometric verification
 */

import {useEffect, useCallback} from "react";
import {useAuth} from "@/contexts/AuthContext";
import {useDeepLink} from "@/contexts/DeepLinkContext";
import {useRouter} from "expo-router";
import {ApiLogger} from "@/services/api/base/api-logger";

interface UseDeepLinkAuthOptions {
  /**
   * Whether to automatically process pending deep links after authentication
   * @default true
   */
  autoProcessPending?: boolean;

  /**
   * Whether to redirect to login if deep link requires authentication
   * @default true
   */
  redirectToLogin?: boolean;

  /**
   * Custom callback when deep link requires authentication but user is not logged in
   */
  onAuthRequired?: (pendingRoute: any) => void;

  /**
   * Custom callback when deep link is processed successfully
   */
  onDeepLinkProcessed?: (route: any) => void;
}

/**
 * Hook that manages deep link integration with authentication
 */
export function useDeepLinkAuth(options: UseDeepLinkAuthOptions = {}) {
  const {
    autoProcessPending = true,
    redirectToLogin = true,
    onAuthRequired,
    onDeepLinkProcessed
  } = options;

  const {
    isAuthenticated,
    isLoading: authLoading,
    requiresBiometricAuth,
    authenticateWithBiometrics,
    setOnAuthSuccessCallback
  } = useAuth();

  const {
    pendingDeepLink,
    isProcessingDeepLink,
    processPendingDeepLink,
    clearPendingDeepLink
  } = useDeepLink();

  const router = useRouter();

  /**
   * Handle authentication requirement for deep links
   */
  const handleAuthRequired = useCallback(() => {
    if (!pendingDeepLink) return;

    ApiLogger.info("Deep link requires authentication", {
      route: pendingDeepLink,
      isAuthenticated,
      requiresBiometricAuth
    });

    // Set up callback to process deep link after successful authentication
    setOnAuthSuccessCallback(async () => {
      ApiLogger.info("Processing deep link after authentication via callback");
      await processPendingDeepLink();
      if (onDeepLinkProcessed) {
        onDeepLinkProcessed(pendingDeepLink);
      }
    });

    // Custom callback
    if (onAuthRequired) {
      onAuthRequired(pendingDeepLink);
      return;
    }

    // Default behavior: redirect to login
    if (redirectToLogin && !isAuthenticated && !requiresBiometricAuth) {
      ApiLogger.info("Redirecting to login for deep link authentication");
      router.push("/(auth)/login");
    }
  }, [
    pendingDeepLink,
    isAuthenticated,
    requiresBiometricAuth,
    onAuthRequired,
    redirectToLogin,
    router,
    setOnAuthSuccessCallback,
    processPendingDeepLink,
    onDeepLinkProcessed
  ]);

  /**
   * Process pending deep link after successful authentication
   */
  const handleAuthenticatedDeepLink = useCallback(async () => {
    if (!pendingDeepLink || !autoProcessPending) return;

    try {
      ApiLogger.info("Processing deep link after authentication", {
        route: pendingDeepLink
      });

      await processPendingDeepLink();

      if (onDeepLinkProcessed) {
        onDeepLinkProcessed(pendingDeepLink);
      }
    } catch (error) {
      ApiLogger.error(
        "Error processing authenticated deep link",
        error as Error
      );
    }
  }, [
    pendingDeepLink,
    autoProcessPending,
    processPendingDeepLink,
    onDeepLinkProcessed
  ]);

  /**
   * Handle biometric authentication for deep links
   */
  const handleBiometricDeepLink = useCallback(async () => {
    if (!pendingDeepLink || !requiresBiometricAuth) return;

    try {
      ApiLogger.info("Attempting biometric authentication for deep link");

      const biometricSuccess = await authenticateWithBiometrics();

      if (biometricSuccess) {
        ApiLogger.info(
          "Biometric authentication successful, processing deep link"
        );
        await handleAuthenticatedDeepLink();
      } else {
        ApiLogger.warn("Biometric authentication failed for deep link");
        // Keep pending deep link for retry
      }
    } catch (error) {
      ApiLogger.error(
        "Error during biometric authentication for deep link",
        error as Error
      );
    }
  }, [
    pendingDeepLink,
    requiresBiometricAuth,
    authenticateWithBiometrics,
    handleAuthenticatedDeepLink
  ]);

  /**
   * Main effect to handle deep link authentication flow
   */
  useEffect(() => {
    // Don't process while auth is loading
    if (authLoading || isProcessingDeepLink) {
      return;
    }

    // No pending deep link to process
    if (!pendingDeepLink) {
      return;
    }

    ApiLogger.info("Evaluating deep link authentication state", {
      isAuthenticated,
      requiresBiometricAuth,
      pendingRoute: pendingDeepLink.type
    });

    if (requiresBiometricAuth) {
      // User is authenticated but needs biometric verification
      handleBiometricDeepLink();
    } else if (isAuthenticated) {
      // User is fully authenticated, process deep link
      handleAuthenticatedDeepLink();
    } else {
      // User is not authenticated, handle auth requirement
      handleAuthRequired();
    }
  }, [
    authLoading,
    isProcessingDeepLink,
    pendingDeepLink,
    isAuthenticated,
    requiresBiometricAuth,
    handleBiometricDeepLink,
    handleAuthenticatedDeepLink,
    handleAuthRequired
  ]);

  /**
   * Manual trigger for biometric authentication
   */
  const triggerBiometricAuth = useCallback(async (): Promise<boolean> => {
    if (!requiresBiometricAuth) {
      ApiLogger.warn("Biometric authentication not required");
      return false;
    }

    try {
      const success = await authenticateWithBiometrics();
      if (success && pendingDeepLink) {
        await handleAuthenticatedDeepLink();
      }
      return success;
    } catch (error) {
      ApiLogger.error(
        "Error triggering biometric authentication",
        error as Error
      );
      return false;
    }
  }, [
    requiresBiometricAuth,
    authenticateWithBiometrics,
    pendingDeepLink,
    handleAuthenticatedDeepLink
  ]);

  /**
   * Manual trigger to clear pending deep link
   */
  const cancelPendingDeepLink = useCallback(() => {
    ApiLogger.info("Manually canceling pending deep link");
    clearPendingDeepLink();
  }, [clearPendingDeepLink]);

  return {
    // State
    pendingDeepLink,
    isProcessingDeepLink,
    requiresBiometricAuth,
    isAuthenticated,

    // Actions
    triggerBiometricAuth,
    cancelPendingDeepLink,
    processPendingDeepLink,

    // Status
    hasPendingDeepLink: !!pendingDeepLink,
    canProcessDeepLink:
      isAuthenticated && !requiresBiometricAuth && !authLoading,
    needsBiometricAuth: requiresBiometricAuth && !!pendingDeepLink,
    needsLogin: !isAuthenticated && !!pendingDeepLink
  };
}

export default useDeepLinkAuth;
