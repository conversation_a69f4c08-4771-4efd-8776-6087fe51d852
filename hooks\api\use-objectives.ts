/**
 * React Query hooks para gerenciamento de objetivos/badges
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import ObjectivesService from "@/services/api/objectives/objectives.service";
import {
  Objective,
  UpdateObjectiveRequest,
  ObjectivesParams,
  ObjectivesResponse
} from "@/models/api/objectives.models";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";

// Chaves de query para cache
export const objectivesKeys = {
  all: ["objectives"] as const,
  lists: () => [...objectivesKeys.all, "list"] as const,
  list: (params?: ObjectivesParams) =>
    [...objectivesKeys.lists(), params] as const,
  details: () => [...objectivesKeys.all, "detail"] as const,
  detail: (id: string | number) => [...objectivesKeys.details(), id] as const
};

/**
 * Hook para buscar objetivos do usuário
 */
export const useObjectives = (
  params?: ObjectivesParams,
  options?: UseQueryOptions<ObjectivesResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: objectivesKeys.list(params),
    queryFn: () => ObjectivesService.getObjectives(params),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error) => {
      // Não tentar novamente para erros 401 (não autenticado)
      if (error?.status === 401) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para buscar objetivo específico por ID
 */
export const useObjective = (
  id: string | number,
  options?: UseQueryOptions<Objective, BaseApiError>
) => {
  return useQuery({
    queryKey: objectivesKeys.detail(id),
    queryFn: () => ObjectivesService.getObjectiveById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 404) {
        return false;
      }
      return failureCount < 2;
    },
    ...options
  });
};

/**
 * Hook para criar novo objetivo
 */
export const useCreateObjective = (
  options?: UseMutationOptions<
    Objective,
    BaseApiError,
    {name: string; description?: string}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {name: string; description?: string}) =>
      ObjectivesService.createObjective({
        name: data.name,
        // Backend requer descrição não vazia; usar o próprio nome como fallback
        description:
          data.description && data.description.trim() !== ""
            ? data.description
            : data.name
      }),
    onSuccess: (data) => {
      // Invalidar listas de objetivos para recarregar dados
      queryClient.invalidateQueries({queryKey: objectivesKeys.lists()});

      // Adicionar o novo objetivo ao cache
      queryClient.setQueryData(objectivesKeys.detail(data.id), data);

      ApiLogger.info("Objetivo criado e cache atualizado", {
        id: data.id,
        title: data.title
      });
    },
    onError: (error) => {
      ApiLogger.error("Erro ao criar objetivo", error);
    },
    ...options
  });
};

/**
 * Hook para atualizar objetivo
 */
export const useUpdateObjective = (
  options?: UseMutationOptions<
    Objective,
    BaseApiError,
    {id: string | number; data: UpdateObjectiveRequest}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({id, data}) => ObjectivesService.updateObjective(id, data),
    onSuccess: (data, variables) => {
      // Atualizar cache do objetivo específico
      queryClient.setQueryData(objectivesKeys.detail(variables.id), data);

      // Invalidar listas para recarregar
      queryClient.invalidateQueries({queryKey: objectivesKeys.lists()});

      ApiLogger.info("Objetivo atualizado e cache atualizado", {
        id: data.id,
        title: data.title
      });
    },
    onError: (error, variables) => {
      ApiLogger.error("Erro ao atualizar objetivo", error, {id: variables.id});
    },
    ...options
  });
};

/**
 * Hook para deletar objetivo
 */
export const useDeleteObjective = (
  options?: UseMutationOptions<void, BaseApiError, string | number>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => ObjectivesService.deleteObjective(id),
    onSuccess: (_, id) => {
      // Remover objetivo do cache
      queryClient.removeQueries({queryKey: objectivesKeys.detail(id)});

      // Invalidar listas para recarregar
      queryClient.invalidateQueries({queryKey: objectivesKeys.lists()});

      ApiLogger.info("Objetivo deletado e cache atualizado", {id});
    },
    onError: (error, id) => {
      ApiLogger.error("Erro ao deletar objetivo", error, {id});
    },
    ...options
  });
};

/**
 * Hook para buscar objetivos com paginação infinita (se necessário no futuro)
 */
export const useInfiniteObjectives = (
  params?: Omit<ObjectivesParams, "page">,
  options?: any
) => {
  // Implementação futura se necessário
  // Similar ao useInfiniteOpportunities
  return null;
};

// Hook de compatibilidade com implementação existente
export const useUserObjectivesNew = (params?: ObjectivesParams) => {
  return useObjectives(params);
};
