/**
 * Serviço de Chat/Mensagens para ClubM
 * Implementa operações para chat e mensagens usando a API real do Swagger
 */

import {apiClient} from "../base/api-client";
import {
  SafeChatViewModel,
  SafeChatMessageViewModel,
  SafeChatViewModelPaginateViewModel,
  SafeChatMessageViewModelPaginateViewModel,
  CreateChatViewModel,
  CreateChatMessageViewModel,
  ChatsListApiParams,
  MessagesListApiParams,
  ChatListItem,
  ChatMessage
} from "@/models/api/chats-api.models";

import {PaginationResponse} from "@/models/api/common.models";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {chatApiAdapter, adaptPaginationResponse} from "./chats-adapters";

// Legacy imports for compatibility with existing hooks
import {
  Chat,
  Message,
  CreateChatRequest,
  SendMessageRequest,
  ChatType,
  MessageType,
  ChatStatus,
  MessageStatus
} from "@/models/api/chats.models";

export interface ChatsListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  userId?: number;
}

export interface MessagesListParams {
  page?: number;
  pageSize?: number;
}

export class ChatsService {
  private static readonly BASE_PATH = "/api/app/chats";
  private static currentUserId: number = 1; // This should come from auth context

  /**
   * Set current user ID for adapters
   */
  static setCurrentUserId(userId: number) {
    this.currentUserId = userId;
  }

  /**
   * Buscar lista de chats do usuário
   */
  static async getChats(
    params?: ChatsListParams
  ): Promise<PaginationResponse<Chat>> {
    try {
      ApiLogger.info("Buscando chats do usuário", params);

      const apiParams: ChatsListApiParams = {
        Search: params?.search,
        UserId: params?.userId,
        Page: params?.page,
        PageSize: params?.pageSize
      };

      const response = await firstValueFrom(
        apiClient.get<SafeChatViewModelPaginateViewModel>(
          this.BASE_PATH,
          apiParams
        )
      );

      // Convert API response to legacy format for compatibility
      const adaptedResponse = adaptPaginationResponse(
        response,
        (chat: SafeChatViewModel) =>
          this.adaptChatToLegacy(
            chatApiAdapter.chatToListItem(chat, this.currentUserId)
          )
      );

      ApiLogger.info(`Encontrados ${adaptedResponse.data.length} chats`);
      return adaptedResponse;
    } catch (error: any) {
      // Se for 404, retornar resposta vazia em vez de erro
      if (error?.status === 404) {
        ApiLogger.warn(
          "Endpoint de chats não encontrado, retornando resposta vazia"
        );
        return {
          data: [],
          pagination: {
            currentPage: 1,
            pageSize: 20,
            totalCount: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false
          }
        };
      }

      ApiLogger.error("Erro ao buscar chats", error as Error);
      throw error;
    }
  }

  /**
   * Buscar chat específico por ID
   */
  static async getChatById(id: string): Promise<Chat> {
    try {
      ApiLogger.info("Buscando chat por ID", {id});

      const response = await firstValueFrom(
        apiClient.get<SafeChatViewModel>(`${this.BASE_PATH}/${id}`)
      );

      const chatListItem = chatApiAdapter.chatToListItem(
        response,
        this.currentUserId
      );
      const adaptedChat = this.adaptChatToLegacy(chatListItem);

      ApiLogger.info("Chat encontrado", {id, name: adaptedChat.name});
      return adaptedChat;
    } catch (error) {
      ApiLogger.error("Erro ao buscar chat", error as Error, {id});
      throw error;
    }
  }

  /**
   * Convert ChatListItem to legacy Chat format
   */
  private static adaptChatToLegacy(chatItem: ChatListItem): Chat {
    return {
      id: chatItem.id.toString(),
      type: "DIRECT" as ChatType, // Default type, could be enhanced
      status: "ACTIVE" as ChatStatus,
      name: chatItem.name,
      avatarUrl: chatItem.avatar, // propagate avatar resolved from participants
      isPrivate: true,
      createdBy: chatItem.participants[0]?.userId.toString() || "1",
      lastActivity: chatItem.createdAt,
      memberCount: chatItem.participants.length,
      unreadCount: chatItem.unreadCount,
      createdAt: chatItem.createdAt,
      updatedAt: chatItem.createdAt
    };
  }

  /**
   * Criar novo chat
   */
  static async createChat(chat: CreateChatRequest): Promise<Chat> {
    try {
      // Validate input
      if (!chat.memberIds || chat.memberIds.length === 0) {
        throw new Error("Cannot create chat: no members specified");
      }

      // Validate member IDs
      const validMemberIds = chat.memberIds.filter((id) => {
        const numId = parseInt(id);
        return !isNaN(numId) && numId > 0;
      });

      if (validMemberIds.length === 0) {
        throw new Error("Cannot create chat: no valid member IDs provided");
      }

      ApiLogger.info("Criando novo chat", {
        type: chat.type,
        memberCount: validMemberIds.length,
        memberIds: validMemberIds
      });

      const apiChat: CreateChatViewModel = {
        userIds: validMemberIds.map((id) => parseInt(id))
      };

      const response = await firstValueFrom(
        apiClient.post<SafeChatViewModel>(this.BASE_PATH, apiChat)
      );

      if (!response || !response.id) {
        throw new Error("Invalid response from chat creation API");
      }

      const chatListItem = chatApiAdapter.chatToListItem(
        response,
        this.currentUserId
      );

      if (!chatListItem) {
        throw new Error("Failed to convert chat response to UI format");
      }

      const adaptedChat = this.adaptChatToLegacy(chatListItem);

      ApiLogger.info("Chat criado com sucesso", {
        id: adaptedChat.id,
        name: adaptedChat.name
      });
      return adaptedChat;
    } catch (error) {
      ApiLogger.error("Erro ao criar chat", error as Error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (
          error.message.includes("403") ||
          error.message.includes("Forbidden")
        ) {
          throw new Error(
            "Permission denied: you don't have permission to create chats with this user"
          );
        } else if (
          error.message.includes("404") ||
          error.message.includes("Not Found")
        ) {
          throw new Error("User not found: the specified user does not exist");
        } else if (
          error.message.includes("400") ||
          error.message.includes("Bad Request")
        ) {
          throw new Error(
            "Invalid request: please check the user information and try again"
          );
        }
      }

      throw error;
    }
  }

  // Note: Update and Delete chat methods are not available in the current API
  // These would need to be implemented when the API supports them

  /**
   * Buscar mensagens de um chat
   */
  static async getMessages(
    chatId: string,
    params?: MessagesListParams
  ): Promise<PaginationResponse<Message>> {
    try {
      ApiLogger.info("Buscando mensagens do chat", {chatId, params});

      const apiParams: MessagesListApiParams = {
        Page: params?.page,
        PageSize: params?.pageSize
      };

      const response = await firstValueFrom(
        apiClient.get<SafeChatMessageViewModelPaginateViewModel>(
          `${this.BASE_PATH}/${chatId}/message`,
          apiParams
        )
      );

      // Convert API response to legacy format for compatibility
      const adaptedResponse = adaptPaginationResponse(
        response,
        (message: SafeChatMessageViewModel) =>
          this.adaptMessageToLegacy(
            chatApiAdapter.messageToChatMessage(message, this.currentUserId)
          )
      );

      ApiLogger.info(`Encontradas ${adaptedResponse.data.length} mensagens`, {
        chatId
      });
      return adaptedResponse;
    } catch (error: any) {
      // Se for 404, retornar resposta vazia em vez de erro
      if (error?.status === 404) {
        ApiLogger.warn(
          "Endpoint de mensagens não encontrado, retornando resposta vazia"
        );
        return {
          data: [],
          pagination: {
            currentPage: 1,
            pageSize: 50,
            totalCount: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false
          }
        };
      }

      ApiLogger.error("Erro ao buscar mensagens", error as Error, {chatId});
      throw error;
    }
  }

  /**
   * Enviar mensagem
   */
  static async sendMessage(
    chatId: string,
    message: SendMessageRequest
  ): Promise<Message> {
    try {
      ApiLogger.info("Enviando mensagem", {chatId, content: message.content});

      const apiMessage: CreateChatMessageViewModel = {
        message: message.content
      };

      const response = await firstValueFrom(
        apiClient.post<SafeChatMessageViewModel>(
          `${this.BASE_PATH}/${chatId}/message`,
          apiMessage,
          {timeout: 60000} // 60 segundos para envio de mensagens
        )
      );

      const chatMessage = chatApiAdapter.messageToChatMessage(
        response,
        this.currentUserId
      );

      if (!chatMessage) {
        throw new Error("Failed to convert message response to UI format");
      }

      const adaptedMessage = this.adaptMessageToLegacy(chatMessage);

      ApiLogger.info("Mensagem enviada com sucesso", {
        chatId,
        messageId: adaptedMessage.id
      });
      return adaptedMessage;
    } catch (error) {
      ApiLogger.error("Erro ao enviar mensagem", error as Error, {chatId});
      throw error;
    }
  }

  /**
   * Convert ChatMessage to legacy Message format
   */
  private static adaptMessageToLegacy(
    chatMessage: ChatMessage
  ): Message & {user?: {name?: string; avatarUrl?: string}} {
    return {
      id: chatMessage.id.toString(),
      chatId: chatMessage.chatId.toString(),
      senderId: chatMessage.userId.toString(),
      type: "TEXT" as MessageType,
      status:
        chatMessage.deliveryStatus === "read"
          ? MessageStatus.READ
          : MessageStatus.SENT,
      content: chatMessage.message,
      createdAt: chatMessage.createdAt,
      updatedAt: chatMessage.createdAt,
      // Include user information for avatar display
      user: {
        name: chatMessage.senderName,
        avatarUrl: chatMessage.avatar
      }
    };
  }

  // Placeholder methods for hooks compatibility
  // These methods are not available in the current API but are expected by the hooks

  static async updateChat(id: string, chat: any): Promise<Chat> {
    throw new Error("Update chat not implemented in current API");
  }

  static async deleteChat(id: string): Promise<void> {
    throw new Error("Delete chat not implemented in current API");
  }

  static async updateMessage(
    chatId: string,
    messageId: string,
    message: any
  ): Promise<Message> {
    throw new Error("Update message not implemented in current API");
  }

  static async deleteMessage(chatId: string, messageId: string): Promise<void> {
    throw new Error("Delete message not implemented in current API");
  }

  static async markMessagesAsRead(
    chatId: string,
    messageIds: string[]
  ): Promise<{success: boolean}> {
    // This could be implemented as a local state update for now
    ApiLogger.info("Marcando mensagens como lidas (local)", {
      chatId,
      count: messageIds.length
    });
    return {success: true};
  }

  // Simplified placeholder methods for hooks compatibility
  static async getChatMembers(chatId: string, params?: any): Promise<any> {
    // Return empty data for now - this would need to extract from chat.participants
    return {
      data: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        totalCount: 0,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      }
    };
  }

  static async addChatMembers(
    chatId: string,
    request: any
  ): Promise<{success: boolean}> {
    throw new Error("Add chat members not implemented in current API");
  }

  static async leaveChat(chatId: string): Promise<{success: boolean}> {
    throw new Error("Leave chat not implemented in current API");
  }

  static async uploadFile(request: any): Promise<any> {
    throw new Error("File upload not implemented in current API");
  }

  static async startCall(request: any): Promise<any> {
    throw new Error("Start call not implemented in current API");
  }

  static async getChatStats(): Promise<any> {
    return {
      totalChats: 0,
      activeChats: 0,
      totalMessages: 0,
      unreadMessages: 0,
      averageResponseTime: 0,
      mostActiveChat: null,
      recentActivity: []
    };
  }

  static async getUnreadChats(): Promise<Chat[]> {
    // Filter chats with unreadCount > 0
    const chatsResponse = await this.getChats({pageSize: 50});
    return chatsResponse.data.filter((chat) => chat.unreadCount > 0);
  }

  // Additional placeholder methods for compatibility
  static async updateChatMember(
    chatId: string,
    userId: string,
    request: any
  ): Promise<any> {
    throw new Error("Update chat member not implemented in current API");
  }

  static async removeChatMember(
    chatId: string,
    userId: string
  ): Promise<{success: boolean}> {
    throw new Error("Remove chat member not implemented in current API");
  }

  // Additional methods not available in current API
  static async archiveChat(chatId: string): Promise<Chat> {
    throw new Error("Archive chat not implemented in current API");
  }

  static async muteChat(chatId: string, duration?: number): Promise<Chat> {
    throw new Error("Mute chat not implemented in current API");
  }
}

export default ChatsService;
