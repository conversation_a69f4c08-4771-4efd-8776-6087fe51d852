import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 40,
    minHeight: 300
  },
  iconContainer: {
    alignItems: "center",
    marginBottom: 24
  },
  textContainer: {
    alignItems: "center",
    marginBottom: 32,
    paddingHorizontal: 16
  },
  title: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 18,
    fontWeight: "bold",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    marginBottom: 12
  },
  description: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 20
  },
  registerButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    paddingHorizontal: 32,
    paddingVertical: 12,
    minHeight: 44,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  registerButtonText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    color: "#FCFCFD",
    textAlign: "center"
  }
});

export default styles;
