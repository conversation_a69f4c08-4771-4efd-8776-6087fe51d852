import React, {useMemo, useState, use<PERSON><PERSON>back, useEffect} from "react";
import {Text, View, ScrollView, TouchableOpacity, Share} from "react-native";
import {useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import {Image} from "expo-image";
import {firstValueFrom} from "rxjs";
import {apiClient} from "@/services/api/base/api-client";
import ScreenWithHeader from "@/components/screen-with-header";
import {usePartner, usePartnerBenefits} from "@/hooks/api/use-partners";
import {imageCache} from "@/services/cache/image-cache.service";
import LoadingSpinner from "@/components/loading-spinner";
import ErrorMessage from "@/components/error-message";
import ReferalIcon from "@/components/icons/referal-icon";
import BookIcon from "@/components/icons/book-icon";
import AcquireButton from "@/components/product-page/acquire-button";
import Pill from "@/components/pill";
import DetailsCard from "@/components/product-page/details-card";
import PaymentMethods from "@/components/product-page/payment-methods";
import SimilarProducts from "@/components/product-page/similar-products";

import styles from "@/styles/partners/partner-detail.style";
import TermsModal from "@/components/modals/terms-modal";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
  RadialGradient,
  Rect,
  Circle
} from "react-native-svg";

// Define BenefitPreview type if not imported
interface BenefitPreview {
  id: number;
  title: string;
  description: string;
}

/**
 * Partner detail page component that displays comprehensive information about a partner
 * including their benefits, contact information, and navigation to benefits.
 *
 * Features:
 * - Partner information display
 * - Benefits preview
 * - Navigation to partner benefits
 * - Error handling with retry
 * - Loading states
 * - Responsive design
 */
const PartnerDetailPage: React.FC = () => {
  const {t} = useTranslation();

  const params = useLocalSearchParams();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isTermsModalVisible, setIsTermsModalVisible] = useState(false);
  const [logoImageUri, setLogoImageUri] = useState<string | undefined>(
    undefined
  );
  const [hasLogoError, setHasLogoError] = useState(false);

  // Ensure id is string or number
  const partnerId = Array.isArray(params.id) ? params.id[0] : params.id;

  // Fetch partner data
  const {
    data: partner,
    isLoading: isLoadingPartner,
    error: partnerError,
    refetch: refetchPartner
  } = usePartner(partnerId);

  // Fetch partner benefits preview (first 3)
  const {data: benefitsData, isLoading: isLoadingBenefits} = usePartnerBenefits(
    partnerId,
    {
      page: 1,
      pageSize: 3
    }
  );

  // Function to generate logo URL from logoId
  const getLogoUrl = useCallback((logoId?: string): string => {
    if (!logoId) {
      return "";
    }

    const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
    if (!baseUrl) {
      console.warn(
        "🏢 [PARTNER DETAIL] EXPO_PUBLIC_API_BASE_URL não está configurado"
      );
      return "";
    }

    return `${baseUrl}/api/storage/${logoId}`;
  }, []);

  // Function to fetch logo image with auth headers
  const fetchLogoWithAuth = useCallback(
    async (imageUrl: string) => {
      try {
        setHasLogoError(false);

        // Extract the endpoint from the full URL
        const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
        if (!baseUrl || !imageUrl.startsWith(baseUrl)) {
          // If it's not our API URL, use it directly
          setLogoImageUri(imageUrl);
          return;
        }

        const endpoint = imageUrl.replace(baseUrl, "");

        // Use apiClient to fetch with auth headers
        const response = await firstValueFrom(
          apiClient.request<ArrayBuffer>("GET", endpoint, undefined, {
            responseType: "arraybuffer"
          })
        );

        // Convert ArrayBuffer to base64
        const base64 = btoa(
          new Uint8Array(response).reduce(
            (data, byte) => data + String.fromCharCode(byte),
            ""
          )
        );

        // Create data URI
        const dataUri = `data:image/webp;base64,${base64}`;
        setLogoImageUri(dataUri);

        // Salva no cache global se temos o logoId do parceiro
        if (partner?.logoId) {
          imageCache.set(partner.logoId, dataUri);
        }
      } catch (error) {
        setHasLogoError(true);
        // Fallback to direct URL
        setLogoImageUri(imageUrl);
      }
    },
    [partner?.logoId]
  );

  // Effect to load logo when partner data changes
  useEffect(() => {
    if (partner) {
      // Se já tem logoUrl pronta, usa diretamente sem fazer nova chamada
      if (partner.logoUrl && partner.logoUrl.trim() !== "") {
        setLogoImageUri(partner.logoUrl);
        setHasLogoError(false);
      }
      // Senão, se tem logoId, verifica cache global primeiro
      else if (partner.logoId) {
        // Verifica se já temos a imagem no cache global
        const cachedImage = imageCache.get(partner.logoId);
        if (cachedImage) {
          setLogoImageUri(cachedImage);
          setHasLogoError(false);
        } else {
          const logoUrl = getLogoUrl(partner.logoId);
          if (logoUrl && logoUrl.trim() !== "") {
            fetchLogoWithAuth(logoUrl);
          } else {
            setLogoImageUri(undefined);
          }
        }
      } else {
        setLogoImageUri(undefined);
      }
    }
  }, [partner, getLogoUrl, fetchLogoWithAuth]);

  const FacebookIcon: React.FC = () => (
    <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
      <Defs>
        <LinearGradient
          id="paint0_linear"
          x1="10"
          y1="1.25"
          x2="10"
          y2="18.6981"
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#18ACFE" />
          <Stop offset="1" stopColor="#0163E0" />
        </LinearGradient>
      </Defs>

      <Circle cx="10" cy="10" r="8.75" fill="url(#paint0_linear)" />
      <Path
        d="M13.2586 12.676L13.6472 10.2063H11.2158V8.60437C11.2158 7.92856 11.5548 7.26942 12.6438 7.26942H13.75V5.16687C13.75 5.16687 12.7466 5 11.7877 5C9.78427 5 8.47604 6.18309 8.47604 8.32403V10.2063H6.25V12.676H8.47604V18.6466C8.92294 18.715 9.38015 18.75 9.8459 18.75C10.3117 18.75 10.7689 18.715 11.2158 18.6466V12.676H13.2586Z"
        fill="#DFE9F0"
      />
    </Svg>
  );

  const InstagramIcon: React.FC = () => (
    <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
      <Defs>
        <RadialGradient
          id="paint0_radial"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(7.5 14.375) rotate(-55.3758) scale(15.9498)"
        >
          <Stop stopColor="#B13589" />
          <Stop offset="0.79309" stopColor="#C62F94" />
          <Stop offset="1" stopColor="#8A3AC8" />
        </RadialGradient>

        <RadialGradient
          id="paint1_radial"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(6.875 19.375) rotate(-65.1363) scale(14.1214)"
        >
          <Stop stopColor="#E0E8B7" />
          <Stop offset="0.444662" stopColor="#FB8A2E" />
          <Stop offset="0.71474" stopColor="#E2425C" />
          <Stop offset="1" stopColor="#E2425C" stopOpacity="0" />
        </RadialGradient>

        <RadialGradient
          id="paint2_radial"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(0.312501 1.875) rotate(-8.1301) scale(24.3068 5.19897)"
        >
          <Stop offset="0.156701" stopColor="#406ADC" />
          <Stop offset="0.467799" stopColor="#6A45BE" />
          <Stop offset="1" stopColor="#6A45BE" stopOpacity="0" />
        </RadialGradient>
      </Defs>

      <Rect
        x="1.25"
        y="1.25"
        width="17.5"
        height="17.5"
        rx="6"
        fill="url(#paint0_radial)"
      />
      <Rect
        x="1.25"
        y="1.25"
        width="17.5"
        height="17.5"
        rx="6"
        fill="url(#paint1_radial)"
      />
      <Rect
        x="1.25"
        y="1.25"
        width="17.5"
        height="17.5"
        rx="6"
        fill="url(#paint2_radial)"
      />
      <Path
        d="M14.375 6.5625C14.375 7.08027 13.9553 7.5 13.4375 7.5C12.9197 7.5 12.5 7.08027 12.5 6.5625C12.5 6.04473 12.9197 5.625 13.4375 5.625C13.9553 5.625 14.375 6.04473 14.375 6.5625Z"
        fill="#DFE9F0"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 13.125C11.7259 13.125 13.125 11.7259 13.125 10C13.125 8.27411 11.7259 6.875 10 6.875C8.27411 6.875 6.875 8.27411 6.875 10C6.875 11.7259 8.27411 13.125 10 13.125ZM10 11.875C11.0355 11.875 11.875 11.0355 11.875 10C11.875 8.96447 11.0355 8.125 10 8.125C8.96447 8.125 8.125 8.96447 8.125 10C8.125 11.0355 8.96447 11.875 10 11.875Z"
        fill="#DFE9F0"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.75 9.75C3.75 7.6498 3.75 6.5997 4.15873 5.79754C4.51825 5.09193 5.09193 4.51825 5.79754 4.15873C6.5997 3.75 7.6498 3.75 9.75 3.75H10.25C12.3502 3.75 13.4003 3.75 14.2025 4.15873C14.9081 4.51825 15.4817 5.09193 15.8413 5.79754C16.25 6.5997 16.25 7.6498 16.25 9.75V10.25C16.25 12.3502 16.25 13.4003 15.8413 14.2025C15.4817 14.9081 14.9081 15.4817 14.2025 15.8413C13.4003 16.25 12.3502 16.25 10.25 16.25H9.75C7.6498 16.25 6.5997 16.25 5.79754 15.8413C5.09193 15.4817 4.51825 14.9081 4.15873 14.2025C3.75 13.4003 3.75 12.3502 3.75 10.25V9.75ZM9.75 5H10.25C11.3207 5 12.0486 5.00097 12.6112 5.04694C13.1592 5.09171 13.4395 5.17287 13.635 5.27248C14.1054 5.51217 14.4878 5.89462 14.7275 6.36502C14.8271 6.56052 14.9083 6.84078 14.9531 7.3888C14.999 7.95141 15 8.67928 15 9.75V10.25C15 11.3207 14.999 12.0486 14.9531 12.6112C14.9083 13.1592 14.8271 13.4395 14.7275 13.635C14.4878 14.1054 14.1054 14.4878 13.635 14.7275C13.4395 14.8271 13.1592 14.9083 12.6112 14.9531C12.0486 14.999 11.3207 15 10.25 15H9.75C8.67928 15 7.95141 14.999 7.3888 14.9531C6.84078 14.9083 6.56052 14.8271 6.36502 14.7275C5.89462 14.4878 5.51217 14.1054 5.27248 13.635C5.17287 13.4395 5.09171 13.1592 5.04694 12.6112C5.00097 12.0486 5 11.3207 5 10.25V9.75C5 8.67928 5.00097 7.95141 5.04694 7.3888C5.09171 6.84078 5.17287 6.56052 5.27248 6.36502C5.51217 5.89462 5.89462 5.51217 6.36502 5.27248C6.56052 5.17287 6.84078 5.09171 7.3888 5.04694C7.95141 5.00097 8.67928 5 9.75 5Z"
        fill="#DFE9F0"
      />
    </Svg>
  );

  const WhatsApp: React.FC = () => (
    <Svg width="29" height="30" viewBox="0 0 29 30" fill="none">
      <Path
        d="M1.55675 13.9447C1.55614 16.1463 2.13405 18.296 3.23292 20.1907L1.45166 26.6645L8.10736 24.9274C9.94825 25.9249 12.0108 26.4476 14.1068 26.4478H14.1123C21.0316 26.4478 26.664 20.8433 26.667 13.9546C26.6683 10.6165 25.3635 7.47765 22.9929 5.11612C20.6227 2.75479 17.4705 1.45367 14.1118 1.45215C7.19174 1.45215 1.55971 7.05637 1.55685 13.9447"
        fill="url(#paint0_linear_600_35464)"
      />
      <Path
        d="M1.10917 13.9407C1.10846 16.2215 1.70708 18.448 2.84513 20.4106L1 27.1165L7.89435 25.3171C9.79397 26.3481 11.9328 26.8916 14.1091 26.8924H14.1147C21.2822 26.8924 27.1169 21.0863 27.12 13.951C27.1212 10.493 25.7695 7.24132 23.3142 4.79519C20.8586 2.34936 17.5936 1.00142 14.1147 1C6.94597 1 1.11203 6.80531 1.10917 13.9407ZM5.21501 20.0726L4.95759 19.6659C3.87544 17.9531 3.30427 15.9739 3.30509 13.9415C3.30733 8.01076 8.15627 3.18563 14.1188 3.18563C17.0063 3.18684 19.7199 4.30728 21.7609 6.34016C23.8018 8.37323 24.9249 11.0758 24.9242 13.9502C24.9215 19.881 20.0725 24.7067 14.1147 24.7067H14.1104C12.1705 24.7057 10.2679 24.1871 8.60867 23.2071L8.21381 22.9741L4.12256 24.0418L5.21501 20.0726Z"
        fill="url(#paint1_linear_600_35464)"
      />
      <Path
        d="M10.8637 8.53029C10.6203 7.99171 10.3641 7.98084 10.1326 7.97139C9.94298 7.96327 9.72626 7.96388 9.50975 7.96388C9.29304 7.96388 8.94093 8.04503 8.6433 8.3685C8.34537 8.69228 7.50586 9.47472 7.50586 11.0661C7.50586 12.6575 8.67034 14.1956 8.83267 14.4116C8.99521 14.6272 11.0807 17.9974 14.3837 19.294C17.1287 20.3715 17.6874 20.1572 18.2831 20.1031C18.879 20.0493 20.2058 19.3209 20.4765 18.5656C20.7474 17.8104 20.7474 17.163 20.6662 17.0277C20.5849 16.8929 20.3682 16.812 20.0433 16.6503C19.7183 16.4886 18.1206 15.706 17.8227 15.598C17.5248 15.4902 17.3082 15.4363 17.0915 15.7602C16.8748 16.0836 16.2525 16.812 16.0628 17.0277C15.8733 17.2439 15.6837 17.2709 15.3588 17.1091C15.0336 16.9468 13.9871 16.6056 12.7455 15.5038C11.7794 14.6464 11.1273 13.5876 10.9377 13.2637C10.7481 12.9403 10.9174 12.765 11.0803 12.6039C11.2263 12.4589 11.4054 12.2262 11.568 12.0373C11.7301 11.8484 11.7841 11.7137 11.8925 11.4979C12.0009 11.282 11.9467 11.0931 11.8656 10.9313C11.7841 10.7695 11.1527 9.16983 10.8637 8.53029Z"
        fill="white"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_600_35464"
          x1="1262.22"
          y1="2522.69"
          x2="1262.22"
          y2="1.45215"
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#1FAF38" />
          <Stop offset="1" stopColor="#60D669" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_600_35464"
          x1="1307"
          y1="2612.65"
          x2="1307"
          y2="1"
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#F9F9F9" />
          <Stop offset="1" stopColor="white" />
        </LinearGradient>
      </Defs>
    </Svg>
  );

  const benefits = benefitsData?.data || [];
  const isLoading = isLoadingPartner || isLoadingBenefits;

  // Truncate description for preview
  const truncatedDescription = useMemo(() => {
    if (!partner?.description) return "";
    const description = partner.description;
    return description.length > 50
      ? description.substring(0, 150) + "..."
      : description;
  }, [partner?.description]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  const handleOpenTermsModal = () => {
    setIsTermsModalVisible(true);
  };

  const handleCloseTermsModal = () => {
    setIsTermsModalVisible(false);
  };

  const handleContact = () => {
    // Implement contact logic here
    return (
      <View style={{marginTop: 24}}>
        <View style={styles.iconLine}>
          <View style={styles.iconBox}>
            <FacebookIcon />
          </View>
          <View style={styles.iconTextContainer}>
            <Text style={styles.iconTitle}>Siga a gente no Facebook</Text>
            <Text style={styles.iconText}>facebook.com/club7</Text>
          </View>
          <ReferalIcon width={25} height={25} />
        </View>
        <View style={styles.iconLine}>
          <View style={styles.iconBox}>
            <InstagramIcon />
          </View>
          <View style={styles.iconTextContainer}>
            <Text style={styles.iconTitle}>Siga a gente no Facebook</Text>
            <Text style={styles.iconText}>facebook.com/club7</Text>
          </View>
          <ReferalIcon width={25} height={25} />
        </View>
        <View style={styles.iconLine}>
          <View style={styles.iconBox}>
            <WhatsApp />
          </View>
          <View style={styles.iconTextContainer}>
            <Text style={styles.iconTitle}>Fale conosco via WhatsApp</Text>
            <Text style={styles.iconText}>+55 11 98765-4321</Text>
          </View>
          <ReferalIcon width={25} height={25} />
        </View>
      </View>
    );
  };

  const handleRetry = () => {
    refetchPartner();
  };

  if (isLoading) {
    return (
      <ScreenWithHeader screenTitle={t("partners.partnerDetails")} backButton>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>{t("common.loading")}</Text>
        </View>
      </ScreenWithHeader>
    );
  }

  if (partnerError || !partner) {
    return (
      <ScreenWithHeader screenTitle={t("partners.partnerDetails")} backButton>
        <View style={styles.errorContainer}>
          <ErrorMessage
            message={t("partners.errorLoadingPartner")}
            onRetry={handleRetry}
          />
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle="Benefícios"
      backButton={true}
      disablePadding={true}
    >
      <Image
        style={styles.backgroundImage}
        source={require("../../../assets/textures/rotated-pattern.png")}
      />
      <View style={styles.container}>
        {/* Product Section */}
        <View>
          <View style={{alignItems: "center", marginTop: -40, zIndex: 1}}>
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 8,
                backgroundColor: "#4A90E2",
                justifyContent: "center",
                alignItems: "center",
                marginBottom: 16,
                zIndex: 1,
                overflow: "hidden"
              }}
            >
              {logoImageUri && !hasLogoError ? (
                <Image
                  source={{uri: logoImageUri}}
                  style={{width: 60, height: 60}}
                  contentFit="contain"
                />
              ) : (
                <BookIcon width={40} height={40} stroke="#FFFFFF" />
              )}
            </View>
            <Text style={{fontSize: 18, fontWeight: "100", color: "#FFFFFF"}}>
              Beneficios {partner?.name}
            </Text>
          </View>

          <View>
            <View style={{marginBottom: 20}}>
              <Text
                style={{
                  fontSize: 24,
                  fontWeight: "700",
                  color: "#FFFFFF",
                  textAlign: "center",
                  marginBottom: 12
                }}
              ></Text>
              <View style={{marginBottom: 0, top: -25}}>
                <Text
                  style={{
                    fontSize: 16,
                    color: "#CCCCCC",
                    lineHeight: 24,
                    textAlign: "center"
                  }}
                >
                  {isExpanded ? partner?.description : truncatedDescription}
                  <Text
                    onPress={handleSeeMore}
                    style={{
                      color: "#ADA843",
                      fontWeight: "500"
                    }}
                  >
                    {isExpanded
                      ? ` ${t("productPage.seeLess")}`
                      : ` ${t("productPage.seeMore")}`}
                  </Text>
                </Text>
              </View>

              <View style={{marginBottom: 0}}>
                {/* removido por não ter pagamento ate o momento */}
                {/* <AcquireButton
                  productId={productQuery.data?.id || 0}
                  productName={productQuery.data?.name || ""}
                  productDescription={productQuery.data?.description || ""}
                  tag={
                    <Pill
                      text="POR TEMPO LIMITADO!"
                      textColor="#B93815"
                      backgroundColor="#FEF6EE"
                      borderColor={"#B93815"}
                    />
                  }
                  price={500000}
                /> */}
              </View>
              <View style={{flexDirection: "row", justifyContent: "center"}}>
                <Text
                  style={{fontSize: 14, fontWeight: "100", color: "#FFFFFF"}}
                >
                  Esse beneficio irá terminar em:
                </Text>
                <Text
                  style={{fontSize: 14, fontWeight: "700", color: "#FFFFFF"}}
                >
                  {partner ? " 08h34m49s" : "Carregando..."}
                </Text>
              </View>
            </View>

            <DetailsCard
              product={{
                id: partner?.id || 0,
                name: partner?.name || "",
                description: partner?.description || "",
                category: Number(partner?.category) || 0,
                // Mapear outros campos necessários do Partner para Product
                periodicity: "monthly" as any,
                isActive: partner?.status === 1,
                value: 0,
                termId: null,
                createdAt: partner?.createdAt || ""
              }}
              onOpenTermsModal={handleOpenTermsModal}
            />
          </View>
          {/* removido por não ter pagamento ate o momento */}
          {/* <View style={{marginTop: 24}}>
            <PaymentMethods product={productQuery.data} />
          </View> */}
          <View style={{marginTop: 24, paddingBottom: 24}}>
            <Text style={styles.titleSocial}>
              Contato e redes sociais {partner?.name || "do parceiro"}
            </Text>
            {handleContact()}
          </View>
        </View>
      </View>

      {/* Terms Modal */}
      <TermsModal
        visible={isTermsModalVisible}
        onClose={handleCloseTermsModal}
        partnerName={partner?.name || "parceiro"}
      />
    </ScreenWithHeader>
  );
};

export default PartnerDetailPage;
