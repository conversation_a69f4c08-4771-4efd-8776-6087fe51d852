/**
 * Terms Service for ClubM
 * Handles all terms and conditions related API operations
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {
  Term,
  ApiTerm,
  TermAcceptance,
  TermsListParams,
  TermsListResponse,
  TermDetailsResponse,
  TermAcceptanceResponse,
  AcceptTermRequest,
  TermStatus,
  TermType,
  getActiveTerms,
  getTermsByType,
  getLatestVersion
} from "@/models/api/terms.models";

export class TermsService {
  private static readonly BASE_PATH = "/api/app/terms";

  /**
   * Buscar lista de termos com filtros e paginação
   */
  static async getTerms(params?: TermsListParams): Promise<TermsListResponse> {
    try {
      ApiLogger.info("Buscando lista de termos", params);

      const response = await firstValueFrom(
        apiClient.get<TermsListResponse>(this.BASE_PATH, {
          params: {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || "",
            Type: params?.type,
            Status: params?.status,
            CreatedAtStart: params?.createdAtStart,
            CreatedAtEnd: params?.createdAtEnd
          }
        })
      );

      ApiLogger.info(`Encontrados ${response.data.length} termos`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar termos", error as Error);
      throw error;
    }
  }

  /**
   * Buscar detalhes de um termo específico (formato ApiTerm)
   */
  static async getTermById(id: number): Promise<ApiTerm> {
    try {
      ApiLogger.info("Buscando detalhes do termo", {id});

      const response = await firstValueFrom(
        apiClient.get<ApiTerm>(`${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info(`Termo ${id} encontrado`, {
        name: response.name,
        lastVersion: response.lastVersion,
        versionsCount: response.versions?.length || 0
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar termo", error as Error);
      throw error;
    }
  }

  /**
   * Aceitar uma versão específica de um termo
   */
  static async acceptTermVersion(
    termId: number,
    version: number
  ): Promise<TermAcceptanceResponse> {
    try {
      ApiLogger.info("Aceitando versão do termo", {termId, version});

      const response = await firstValueFrom(
        apiClient.post<TermAcceptance>(
          `${this.BASE_PATH}/${termId}/versions/${version}/accept`,
          {} // Body vazio conforme API
        )
      );

      ApiLogger.info(`Termo ${termId} versão ${version} aceito com sucesso`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      ApiLogger.error("Erro ao aceitar termo", error as Error);
      throw error;
    }
  }

  /**
   * Aceitar uma versão específica de um termo com tratamento gracioso para 409
   * Usado em fluxos de pagamento onde o termo já pode ter sido aceito
   */
  static async acceptTermVersionGraceful(
    termId: number,
    version: number
  ): Promise<TermAcceptanceResponse> {
    try {
      ApiLogger.info("Aceitando versão do termo (graceful)", {termId, version});

      const response = await firstValueFrom(
        apiClient.post<TermAcceptance>(
          `${this.BASE_PATH}/${termId}/versions/${version}/accept`,
          {} // Body vazio conforme API
        )
      );

      ApiLogger.info(`Termo ${termId} versão ${version} aceito com sucesso`);
      return {
        success: true,
        data: response
      };
    } catch (error: any) {
      // Verificar se é erro 409 (termo já aceito)
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as any;
        if (apiError.response?.status === 409) {
          const errorMessage = apiError.response?.data?.message || "";
          ApiLogger.info(
            `Termo ${termId} versão ${version} já foi aceito anteriormente (409): ${errorMessage}`
          );
          return {
            success: true,
            data: null,
            alreadyAccepted: true
          };
        }
      }

      ApiLogger.error("Erro ao aceitar termo", error as Error);
      throw error;
    }
  }

  /**
   * Buscar termos ativos para pagamento
   * Retorna os termos de pagamento ativos na versão mais recente
   */
  static async getActivePaymentTerms(): Promise<Term | null> {
    try {
      ApiLogger.info("Buscando termos de pagamento ativos");

      const response = await this.getTerms({
        type: TermType.PAYMENT_TERMS,
        status: TermStatus.ACTIVE,
        pageSize: 50 // Buscar mais para garantir que pegamos todas as versões
      });

      const activeTerms = getActiveTerms(response.data);
      const paymentTerms = getTermsByType(activeTerms, TermType.PAYMENT_TERMS);
      const latestTerm = getLatestVersion(paymentTerms);

      if (latestTerm) {
        ApiLogger.info(
          `Termo de pagamento ativo encontrado: ${latestTerm.id} v${latestTerm.version}`
        );
      } else {
        ApiLogger.warn("Nenhum termo de pagamento ativo encontrado");
      }

      return latestTerm || null;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar termos de pagamento ativos",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Buscar termos gerais ativos
   * Retorna os termos gerais ativos na versão mais recente
   */
  static async getActiveGeneralTerms(): Promise<Term | null> {
    try {
      ApiLogger.info("Buscando termos gerais ativos");

      const response = await this.getTerms({
        type: TermType.GENERAL,
        status: TermStatus.ACTIVE,
        pageSize: 50
      });

      const activeTerms = getActiveTerms(response.data);
      const generalTerms = getTermsByType(activeTerms, TermType.GENERAL);
      const latestTerm = getLatestVersion(generalTerms);

      if (latestTerm) {
        ApiLogger.info(
          `Termo geral ativo encontrado: ${latestTerm.id} v${latestTerm.version}`
        );
      } else {
        ApiLogger.warn("Nenhum termo geral ativo encontrado");
      }

      return latestTerm || null;
    } catch (error) {
      ApiLogger.error("Erro ao buscar termos gerais ativos", error as Error);
      throw error;
    }
  }

  /**
   * Buscar termos de uso ativos
   * Retorna os termos de uso ativos na versão mais recente
   */
  static async getActiveTermsOfUse(): Promise<Term | null> {
    try {
      ApiLogger.info("Buscando termos de uso ativos");

      const response = await this.getTerms({
        type: TermType.TERMS_OF_USE,
        status: TermStatus.ACTIVE,
        pageSize: 50
      });

      const activeTerms = getActiveTerms(response.data);
      const termsOfUse = getTermsByType(activeTerms, TermType.TERMS_OF_USE);
      const latestTerm = getLatestVersion(termsOfUse);

      if (latestTerm) {
        ApiLogger.info(
          `Termo de uso ativo encontrado: ${latestTerm.id} v${latestTerm.version}`
        );
      } else {
        ApiLogger.warn("Nenhum termo de uso ativo encontrado");
      }

      return latestTerm || null;
    } catch (error) {
      ApiLogger.error("Erro ao buscar termos de uso ativos", error as Error);
      throw error;
    }
  }
}

export default TermsService;
