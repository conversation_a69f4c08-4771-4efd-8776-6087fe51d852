import React, {useState, useCallback} from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import CheckIcon from "@/components/icons/check-icon";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import {PaymentType} from "@/models/api/payments.models";
import styles from "@/styles/registration/payment-method-selection.style";

interface PaymentMethodOption {
  type: PaymentType;
  name: string;
  description: string;
  icon: React.ReactNode;
  processingTime: string;
}

const PaymentMethodSelection: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();

  const [selectedMethod, setSelectedMethod] = useState<PaymentType | null>(
    null
  );

  const paymentMethods: PaymentMethodOption[] = [
    {
      type: PaymentType.Pix,
      name: t("paymentMethodSelection.pix.name", "PIX"),
      description: t(
        "paymentMethodSelection.pix.description",
        "Pagamento instantâneo"
      ),
      icon: <Text style={styles.pixIcon}>PIX</Text>,
      processingTime: t(
        "paymentMethodSelection.pix.processingTime",
        "Instantâneo"
      )
    },
    {
      type: PaymentType.Boleto,
      name: t("paymentMethodSelection.boleto.name", "Boleto"),
      description: t(
        "paymentMethodSelection.boleto.description",
        "Pagamento via código de barras"
      ),
      icon: <Text style={styles.boletoIcon}>📄</Text>,
      processingTime: t(
        "paymentMethodSelection.boleto.processingTime",
        "1-3 dias úteis"
      )
    },
    {
      type: PaymentType.CreditCard,
      name: t("paymentMethodSelection.creditCard.name", "Cartão de Crédito"),
      description: t(
        "paymentMethodSelection.creditCard.description",
        "Pagamento com cartão"
      ),
      icon: <CreditCardIcon width={24} height={24} />,
      processingTime: t(
        "paymentMethodSelection.creditCard.processingTime",
        "Instantâneo"
      )
    }
  ];

  const handleMethodSelect = useCallback((method: PaymentType) => {
    setSelectedMethod(method);
  }, []);

  const handleNext = useCallback(() => {
    console.log("🔍 [PAYMENT-METHOD] handleNext chamado");
    console.log("🔍 [PAYMENT-METHOD] selectedMethod:", selectedMethod);
    console.log("🔍 [PAYMENT-METHOD] params recebidos:", params);

    if (selectedMethod === null) {
      console.log("❌ [PAYMENT-METHOD] Nenhum método selecionado");
      Alert.alert(
        t("paymentMethodSelection.error.title", "Erro de validação"),
        t(
          "paymentMethodSelection.error.selectMethod",
          "Você deve selecionar um método de pagamento para continuar"
        )
      );
      return;
    }

    // Navegar para a tela específica do método de pagamento
    const baseParams = {
      ...params,
      paymentType: selectedMethod.toString()
    };

    console.log("🚀 [PAYMENT-METHOD] Navegando com baseParams:", baseParams);

    switch (selectedMethod) {
      case PaymentType.Pix:
        console.log("🚀 [PAYMENT-METHOD] Navegando para PIX");
        router.push({
          pathname: "/(registration)/pix-payment",
          params: baseParams
        });
        break;
      case PaymentType.Boleto:
        console.log("🚀 [PAYMENT-METHOD] Navegando para Boleto");
        router.push({
          pathname: "/(registration)/boleto-payment",
          params: baseParams
        });
        break;
      case PaymentType.CreditCard:
        console.log("🚀 [PAYMENT-METHOD] Navegando para Cartão de Crédito");
        router.push({
          pathname: "/(registration)/credit-card-payment",
          params: baseParams
        });
        break;
      default:
        console.log("❌ [PAYMENT-METHOD] Método inválido:", selectedMethod);
        Alert.alert(
          t("paymentMethodSelection.error.title", "Erro"),
          t(
            "paymentMethodSelection.error.invalidMethod",
            "Método de pagamento inválido"
          )
        );
    }
  }, [selectedMethod, params, router, t]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  const formatCurrency = useCallback((value: string) => {
    const numValue = parseFloat(value) / 100;
    return `R$ ${numValue.toFixed(2).replace(".", ",")}`;
  }, []);

  const renderPaymentMethod = useCallback(
    (method: PaymentMethodOption) => {
      const isSelected = selectedMethod === method.type;

      return (
        <TouchableOpacity
          key={method.type}
          style={[styles.methodCard, isSelected && styles.methodCardSelected]}
          onPress={() => handleMethodSelect(method.type)}
          activeOpacity={0.8}
        >
          {isSelected && (
            <View style={styles.greenSection}>
              <View style={styles.checkIcon}>
                <CheckIcon width={24} height={24} replaceColor="#FFFFFF" />
              </View>
            </View>
          )}

          <View
            style={[
              styles.methodContent,
              isSelected && styles.methodContentSelected
            ]}
          >
            <View style={styles.methodHeader}>
              <View style={styles.methodIcon}>{method.icon}</View>
              <View style={styles.methodInfo}>
                <Text style={styles.methodName}>{method.name}</Text>
                <Text style={styles.methodDescription}>
                  {method.description}
                </Text>
              </View>
            </View>
            <Text style={styles.processingTime}>
              {t(
                "paymentMethodSelection.processingTime",
                "Processamento: {{time}}",
                {
                  time: method.processingTime
                }
              )}
            </Text>
          </View>
        </TouchableOpacity>
      );
    },
    [selectedMethod, handleMethodSelect, t]
  );

  return (
    <Screen>
      <View style={styles.container}>
        <BackButton />

        <View style={styles.progressContainer}>
          <Text style={styles.progressTitle}>
            {t("paymentMethodSelection.createAccount", "Criar conta")}
          </Text>
          <Text style={styles.progressStep}>
            {t(
              "paymentMethodSelection.stepProgress",
              "3 / 7 Método de pagamento"
            )}
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, {width: "42%"}]} />
          </View>
        </View>

        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t("paymentMethodSelection.title", "Método de Pagamento")}
          </Text>
          <Text style={styles.subtitle}>
            {t(
              "paymentMethodSelection.description",
              "Escolha como deseja pagar o título de adesão."
            )}
          </Text>
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>
            {t("paymentMethodSelection.summary", "Resumo do pagamento")}
          </Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("paymentMethodSelection.installments", "Parcelamento:")}
            </Text>
            <Text style={styles.summaryValue}>
              {params.selectedInstallments === "1"
                ? t("paymentMethodSelection.singlePayment", "À vista")
                : t("paymentMethodSelection.installmentsCount", "{{count}}x", {
                    count: params.selectedInstallments
                  })}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("paymentMethodSelection.total", "Total:")}
            </Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(params.totalValue as string)}
            </Text>
          </View>
        </View>

        <ScrollView
          style={styles.methodsContainer}
          showsVerticalScrollIndicator={false}
        >
          {paymentMethods.map(renderPaymentMethod)}
        </ScrollView>

        <View style={styles.buttonContainer}>
          <View style={styles.backButton}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={styles.nextButton}>
            <FullSizeButton
              text={t("common.next", "Avançar")}
              onPress={handleNext}
              disabled={selectedMethod === null}
            />
          </View>
        </View>
      </View>
    </Screen>
  );
};

export default PaymentMethodSelection;
