/**
 * Regulation Terms Service
 * Endpoint: GET /api/app/system-config/regulation
 * Returns the regulation term (ApiTerm)
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiTerm} from "@/models/api/terms.models";

export class RegulationTermsService {
  private static readonly BASE_PATH = "/api/app/system-config/regulation";

  /**
   * Fetch Regulation term
   */
  static async getRegulationTerm(): Promise<ApiTerm | null> {
    try {
      ApiLogger.info("Buscando regulamento do sistema");
      const response = await firstValueFrom(
        apiClient.get<ApiTerm | null>(this.BASE_PATH)
      );

      if (!response) {
        ApiLogger.warn("Nenhum regulamento retornado");
        return null;
      }

      ApiLogger.info("Regulamento encontrado", {
        id: (response as any)?.id,
        lastVersion: (response as any)?.lastVersion,
        versionsCount: (response as any)?.versions?.length || 0
      });

      return response as ApiTerm;
    } catch (error) {
      ApiLogger.error("Erro ao buscar regulamento", error as Error);
      throw error;
    }
  }
}

export default RegulationTermsService;

