import React, {useState, useCallback} from "react";
import {useTranslation} from "react-i18next";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView
} from "react-native";
import {
  useCreateObjective,
  useUpdateObjective,
  useDeleteObjective
} from "@/hooks/api/use-objectives";
import {
  CreateObjectiveRequest,
  Objective
} from "@/models/api/objectives.models";
import styles from "@/styles/components/user/objectives-manager.style";

export interface ObjectivesManagerProps {
  objectives?: Objective[];
  onObjectiveCreated?: (objective: Objective) => void;
  onObjectiveUpdated?: (objective: Objective) => void;
  onObjectiveDeleted?: (objectiveId: string | number) => void;
}

const ObjectivesManager: React.FC<ObjectivesManagerProps> = ({
  objectives = [],
  onObjectiveCreated,
  onObjectiveUpdated,
  onObjectiveDeleted
}) => {
  const {t} = useTranslation();
  const [isCreating, setIsCreating] = useState(false);
  const [editingObjective, setEditingObjective] = useState<Objective | null>(
    null
  );
  const [formData, setFormData] = useState<CreateObjectiveRequest>({
    title: "",
    description: "",
    value: 0,
    type: "personal",
    category: "general"
  });

  // Mutations
  const createObjectiveMutation = useCreateObjective({
    onSuccess: (objective) => {
      setIsCreating(false);
      setFormData({
        title: "",
        description: "",
        value: 0,
        type: "personal",
        category: "general"
      });
      onObjectiveCreated?.(objective);
      Alert.alert(
        t("objectives.success.title", "Sucesso"),
        t("objectives.success.created", "Objetivo criado com sucesso!")
      );
    },
    onError: (error) => {
      Alert.alert(
        t("objectives.error.title", "Erro"),
        error.message ||
          t("objectives.error.createFailed", "Erro ao criar objetivo")
      );
    }
  });

  const updateObjectiveMutation = useUpdateObjective({
    onSuccess: (objective) => {
      setEditingObjective(null);
      setFormData({
        title: "",
        description: "",
        value: 0,
        type: "personal",
        category: "general"
      });
      onObjectiveUpdated?.(objective);
      Alert.alert(
        t("objectives.success.title", "Sucesso"),
        t("objectives.success.updated", "Objetivo atualizado com sucesso!")
      );
    },
    onError: (error) => {
      Alert.alert(
        t("objectives.error.title", "Erro"),
        error.message ||
          t("objectives.error.updateFailed", "Erro ao atualizar objetivo")
      );
    }
  });

  const deleteObjectiveMutation = useDeleteObjective({
    onSuccess: (_, objectiveId) => {
      onObjectiveDeleted?.(objectiveId);
      Alert.alert(
        t("objectives.success.title", "Sucesso"),
        t("objectives.success.deleted", "Objetivo deletado com sucesso!")
      );
    },
    onError: (error) => {
      Alert.alert(
        t("objectives.error.title", "Erro"),
        error.message ||
          t("objectives.error.deleteFailed", "Erro ao deletar objetivo")
      );
    }
  });

  const handleCreateObjective = useCallback(() => {
    if (!formData.title.trim() || !formData.description.trim()) {
      Alert.alert(
        t("objectives.error.title", "Erro"),
        t(
          "objectives.error.fillFields",
          "Preencha todos os campos obrigatórios"
        )
      );
      return;
    }

    createObjectiveMutation.mutate(formData);
  }, [formData, createObjectiveMutation, t]);

  const handleUpdateObjective = useCallback(() => {
    if (
      !editingObjective ||
      !formData.title.trim() ||
      !formData.description.trim()
    ) {
      Alert.alert(
        t("objectives.error.title", "Erro"),
        t(
          "objectives.error.fillFields",
          "Preencha todos os campos obrigatórios"
        )
      );
      return;
    }

    updateObjectiveMutation.mutate({
      id: editingObjective.id,
      data: formData
    });
  }, [editingObjective, formData, updateObjectiveMutation, t]);

  const handleDeleteObjective = useCallback(
    (objective: Objective) => {
      Alert.alert(
        t("objectives.delete.title", "Confirmar Exclusão"),
        t(
          "objectives.delete.message",
          "Tem certeza que deseja deletar este objetivo?"
        ),
        [
          {
            text: t("common.cancel", "Cancelar"),
            style: "cancel"
          },
          {
            text: t("common.delete", "Deletar"),
            style: "destructive",
            onPress: () => deleteObjectiveMutation.mutate(objective.id)
          }
        ]
      );
    },
    [deleteObjectiveMutation, t]
  );

  const handleEditObjective = useCallback((objective: Objective) => {
    setEditingObjective(objective);
    setFormData({
      title: objective.title,
      description: objective.description,
      value: objective.value || 0,
      type: objective.type || "personal",
      category: objective.category || "general"
    });
    setIsCreating(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsCreating(false);
    setEditingObjective(null);
    setFormData({
      title: "",
      description: "",
      value: 0,
      type: "personal",
      category: "general"
    });
  }, []);

  const isLoading =
    createObjectiveMutation.isPending ||
    updateObjectiveMutation.isPending ||
    deleteObjectiveMutation.isPending;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {t("objectives.manager.title", "Gerenciar Objetivos")}
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setIsCreating(true)}
          disabled={isLoading}
        >
          <Text style={styles.addButtonText}>
            {t("objectives.manager.add", "Adicionar")}
          </Text>
        </TouchableOpacity>
      </View>

      {isCreating && (
        <View style={styles.form}>
          <Text style={styles.formTitle}>
            {editingObjective
              ? t("objectives.manager.edit", "Editar Objetivo")
              : t("objectives.manager.create", "Criar Objetivo")}
          </Text>

          <TextInput
            style={styles.input}
            placeholder={t("objectives.form.title", "Título do objetivo")}
            value={formData.title}
            onChangeText={(text) =>
              setFormData((prev) => ({...prev, title: text}))
            }
            editable={!isLoading}
          />

          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={t(
              "objectives.form.description",
              "Descrição do objetivo"
            )}
            value={formData.description}
            onChangeText={(text) =>
              setFormData((prev) => ({...prev, description: text}))
            }
            multiline
            numberOfLines={3}
            editable={!isLoading}
          />

          <TextInput
            style={styles.input}
            placeholder={t("objectives.form.value", "Valor (opcional)")}
            value={formData.value?.toString() || ""}
            onChangeText={(text) =>
              setFormData((prev) => ({
                ...prev,
                value: text ? parseFloat(text) || 0 : 0
              }))
            }
            keyboardType="numeric"
            editable={!isLoading}
          />

          <View style={styles.formActions}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleCancelEdit}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>
                {t("common.cancel", "Cancelar")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={
                editingObjective ? handleUpdateObjective : handleCreateObjective
              }
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>
                  {editingObjective
                    ? t("common.update", "Atualizar")
                    : t("common.create", "Criar")}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}

      <ScrollView style={styles.objectivesList}>
        {objectives.map((objective) => (
          <View key={objective.id} style={styles.objectiveItem}>
            <View style={styles.objectiveContent}>
              <Text style={styles.objectiveTitle}>{objective.title}</Text>
              <Text style={styles.objectiveDescription}>
                {objective.description}
              </Text>
              {objective.value && (
                <Text style={styles.objectiveValue}>
                  Valor: R${" "}
                  {(objective.value / 100).toLocaleString("pt-BR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}
                </Text>
              )}
            </View>

            <View style={styles.objectiveActions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={() => handleEditObjective(objective)}
                disabled={isLoading}
              >
                <Text style={styles.editButtonText}>
                  {t("common.edit", "Editar")}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => handleDeleteObjective(objective)}
                disabled={isLoading}
              >
                <Text style={styles.deleteButtonText}>
                  {t("common.delete", "Deletar")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default ObjectivesManager;
