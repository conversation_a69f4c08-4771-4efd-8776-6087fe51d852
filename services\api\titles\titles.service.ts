/**
 * Serviço de títulos do ClubM
 * Gerencia operações relacionadas aos títulos de adesão
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiResponse} from "@/models/api/common.models";
import {TitleInstallmentOption, TitleInstallmentResponse} from "@/models/api/payments.models";

export class TitlesService {
  private static readonly BASE_PATH = "/api/app/titles";

  /**
   * Busca opções de parcelamento do título
   * GET /api/app/titles/value
   */
  static async getTitleInstallmentOptions(): Promise<ApiResponse<TitleInstallmentOption[]>> {
    try {
      ApiLogger.info("Buscando opções de parcelamento do título");

      const response = await firstValueFrom(
        apiClient.get<TitleInstallmentOption[]>(`${this.BASE_PATH}/value`)
      );

      ApiLogger.info(`Encontradas ${response.length} opções de parcelamento`);
      
      return {
        success: true,
        data: response
      };
    } catch (error) {
      ApiLogger.error("Erro ao buscar opções de parcelamento do título", error as Error);
      throw error;
    }
  }
}
