import React, {useState, useCallback, useEffect} from "react";
import {Text, View, Alert, ActivityIndicator} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import {useCreateUser} from "@/hooks/api/use-users";
import {CreateUserRequest} from "@/services/api/users/users.service";
import {tokenManager} from "@/services/api/auth/token-manager";
import TermsService from "@/services/api/terms/terms.service";

const UserCreation: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isCreating, setIsCreating] = useState(false);
  const [userCreated, setUserCreated] = useState(false);
  const [userToken, setUserToken] = useState<string | null>(null);

  // Hook para criar usuário
  const createUserMutation = useCreateUser({
    onSuccess: async (userData) => {
      console.log("✅ [USER-CREATION] Usuário criado com sucesso:", userData);

      // Armazenar o token JWT para usar nas próximas requisições
      const tokenResp: any = userData as any;
      if (tokenResp?.accessToken) {
        await tokenManager.setTokenData({
          accessToken: tokenResp.accessToken,
          refreshToken: tokenResp.refreshToken || "",
          tokenType: tokenResp.tokenType || "Bearer",
          expiresIn: tokenResp.expiresIn || 3600,
          expiresAt: new Date(Date.now() + (tokenResp.expiresIn || 3600) * 1000)
        });
        setUserToken(tokenResp.accessToken);
      }

      // Aceitar termos recebidos da etapa anterior (sequencialmente)
      try {
        if (params.acceptedTermsList) {
          const list: Array<{id: number; version: number}> = JSON.parse(
            params.acceptedTermsList as string
          );

          for (const item of list) {
            try {
              console.log(
                "📝 [USER-CREATION] Aceitando termo",
                item.id,
                "v",
                item.version
              );
              await TermsService.acceptTermVersion(item.id, item.version);
            } catch (err) {
              console.error("❌ [USER-CREATION] Falha ao aceitar termo:", err);
              // Prosseguir mesmo se um termo falhar
            }
          }
        }
      } catch (err) {
        console.error(
          "❌ [USER-CREATION] Erro ao processar termos aceitos:",
          err
        );
      }

      // Navegar automaticamente para a próxima etapa do fluxo (parcelamento do título)
      // evitando exibir a tela intermediária de "Conta criada com sucesso"
      try {
        router.replace({
          pathname: "/(registration)/payment-selection",
          params: {
            ...params,
            userToken: tokenResp?.accessToken || userToken || ""
          }
        });
      } catch (navErr) {
        console.warn(
          "⚠️ [USER-CREATION] Falha ao navegar automaticamente:",
          navErr
        );
      }

      setUserCreated(true);
      setIsCreating(false);
      console.log(
        "🎉 Cadastro realizado com sucesso! Bem-vindo ao Club M Brasil."
      );
    },
    onError: (error) => {
      console.error("❌ [USER-CREATION] Erro ao criar usuário:", error);

      // Extrair status e mensagem detalhada do erro
      const status: number | undefined = (error as any)?.status;
      let serverMessage: string =
        (error as any)?.details?.message || error?.message || "";

      // Alguns backends retornam a mensagem como JSON string – tentar parsear
      try {
        if (serverMessage && serverMessage.trim().startsWith("{")) {
          const parsed = JSON.parse(serverMessage);
          if (parsed?.message) serverMessage = parsed.message;
        }
      } catch {}

      // Mensagem padrão
      const defaultMsg = t(
        "userCreation.error.message",
        "Ocorreu um erro ao criar sua conta. Tente novamente."
      );

      // Tratar conflito (409) – usuário já existe com telefone/documento
      const isConflict =
        status === 409 || /já existe/i.test(serverMessage || "");
      const alertTitle = isConflict
        ? t("userCreation.error.conflictTitle", "Cadastro já existente")
        : t("userCreation.error.title", "Erro no Cadastro");
      const alertMessage = serverMessage || defaultMsg;

      // Mostrar alerta com opção de voltar para corrigir os dados
      Alert.alert(alertTitle, alertMessage, [
        {
          text: t("common.back", "Voltar"),
          onPress: () => router.back()
        },
        {text: t("common.ok", "OK")}
      ]);

      setIsCreating(false);
    }
  });

  const createUser = useCallback(async () => {
    setIsCreating(true);

    // Preparar dados localmente; qualquer falha aqui mostra erro genérico
    let userData: CreateUserRequest | null = null;
    try {
      const franchiseIdRaw = params.selectedFranchiseId as string;
      const franchiseId = franchiseIdRaw ? parseInt(franchiseIdRaw) : null;

      const planIdRaw = params.selectedPlanId as string;
      const planId = planIdRaw ? parseInt(planIdRaw) : null;

      userData = {
        Name: (params.fullName as string) || "Nome Padrão",
        Document: (params.document as string) || "12345678901",
        BirthDate: (params.birthDate as string) || "1990-01-01T00:00:00.000Z",
        Sex: isNaN(parseInt(params.sex as string))
          ? 1
          : parseInt(params.sex as string),

        FranchiseId: franchiseId && !isNaN(franchiseId) ? franchiseId : 1,
        PlanId: planId && !isNaN(planId) ? planId : 1,

        Profession: (params.profession as string) || "Desenvolvedor",
        CompanyName: (params.companyName as string) || "Empresa Exemplo",
        CompanyRole: (params.companyRole as string) || "Desenvolvedor",
        CompanySegmentId: isNaN(parseInt(params.companySegmentId as string))
          ? 1
          : parseInt(params.companySegmentId as string),

        AddressState: (params.addressState as string) || "SP",
        AddressCity: (params.addressCity as string) || "São Paulo",
        AddressNeighborhood: (params.addressNeighborhood as string) || "Centro",
        AddressStreet: (params.addressStreet as string) || "Rua Exemplo",
        AddressNumber: (params.addressNumber as string) || "123",
        AddressZip: (params.addressZip as string) || "01234567",
        AddressComplement: (params.addressComplement as string) || "",

        Email: (params.email as string) || "<EMAIL>",
        PhoneNumber: (params.phoneNumber as string) || "(11) 99999-9999",
        WhereMet: isNaN(parseInt(params.whereMet as string))
          ? 1
          : parseInt(params.whereMet as string),
        Facebook: (params.facebook as string) || "",
        Instagram: (params.instagram as string) || "",
        LinkedIn: (params.linkedin as string) || "",
        Website: (params.website as string) || ""
      };

      // Adicionar documentos se existirem
      let selectedDocuments: any[] = [];
      try {
        if (params.selectedDocuments) {
          selectedDocuments = JSON.parse(params.selectedDocuments as string);
        }
      } catch {
        selectedDocuments = [];
      }

      if (selectedDocuments.length > 0) {
        (userData as any).documents = selectedDocuments;
      }
    } catch (error) {
      console.error(
        "❌ [USER-CREATION] Erro ao preparar dados do usuário:",
        error
      );
      Alert.alert(
        t("userCreation.error.title", "Erro"),
        t("userCreation.error.prepareData", "Erro ao preparar dados do usuário")
      );
      setIsCreating(false);
      return;
    }

    // Executar a mutation fora do try/catch para deixar o onError tratar a resposta da API
    if (userData) {
      console.log("📤 [USER-CREATION] Criando usuário com dados:", userData);
      createUserMutation.mutate(userData);
    }
  }, [params, createUserMutation, t]);

  const handleNext = useCallback(() => {
    // Navegar para seleção final de pagamento (de acordo com Motiff)
    router.push({
      pathname: "/(registration)/payment-selection",
      params: {
        ...params,
        userToken: userToken
      }
    });
  }, [router, params, userToken]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  // Criar usuário automaticamente quando a tela carrega
  useEffect(() => {
    if (!isCreating && !userCreated) {
      createUser();
    }
  }, []); // Executar apenas uma vez quando a tela carrega

  if (isCreating) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A",
            paddingHorizontal: 20
          }}
        >
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text
            style={{
              marginTop: 16,
              fontSize: 16,
              color: "#FFFFFF",
              textAlign: "center",
              fontFamily: "Ubuntu"
            }}
          >
            {t("userCreation.creating", "Criando sua conta...")}
          </Text>
          <Text
            style={{
              marginTop: 8,
              fontSize: 14,
              color: "#CCCCCC",
              textAlign: "center",
              fontFamily: "Ubuntu"
            }}
          >
            {t("userCreation.pleaseWait", "Por favor, aguarde...")}
          </Text>
        </View>
      </Screen>
    );
  }

  if (createUserMutation.isError) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A",
            paddingHorizontal: 20
          }}
        >
          <Text
            style={{
              fontSize: 16,
              color: "#FF6B6B",
              textAlign: "center",
              marginBottom: 24,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "userCreation.error.failed",
              "Falha ao criar usuário. Tente novamente."
            )}
          </Text>
          <FullSizeButton
            text={t("common.tryAgain", "Tentar novamente")}
            onPress={createUser}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <View
        style={{
          flex: 1,
          backgroundColor: "#1A1A1A",
          paddingHorizontal: 20,
          paddingTop: 60
        }}
      >
        <BackButton />

        <View style={{marginBottom: 32}}>
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8,
              fontFamily: "Ubuntu"
            }}
          >
            {t("userCreation.createAccount", "Criar conta")}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#CCCCCC",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("userCreation.stepProgress", "1 / 7 Conta criada")}
          </Text>
          <View
            style={{height: 4, backgroundColor: "#333333", borderRadius: 2}}
          >
            <View
              style={{
                height: "100%",
                backgroundColor: "#00D4AA",
                borderRadius: 2,
                width: "14%"
              }}
            />
          </View>
        </View>

        <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
          <View
            style={{
              width: 80,
              height: 80,
              backgroundColor: "#00D4AA",
              borderRadius: 40,
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 24
            }}
          >
            <Text style={{fontSize: 32, color: "#FFFFFF"}}>✓</Text>
          </View>

          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              textAlign: "center",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            {t("userCreation.success.title", "Conta criada com sucesso!")}
          </Text>

          <Text
            style={{
              fontSize: 16,
              color: "#CCCCCC",
              textAlign: "center",
              lineHeight: 24,
              marginBottom: 32,
              fontFamily: "Ubuntu"
            }}
          >
            {t(
              "userCreation.success.description",
              "Agora vamos prosseguir com o pagamento do seu título de adesão."
            )}
          </Text>
        </View>

        <View style={{flexDirection: "row", gap: 12, paddingBottom: 20}}>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("userCreation.continue", "Continuar")}
              onPress={handleNext}
            />
          </View>
        </View>
      </View>
    </Screen>
  );
};

export default UserCreation;
