import React, {useCallback, useMemo, useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ImageBackground
} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import styles from "@/styles/registration/payment-selection.style";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import {useTitleInstallmentOptions} from "@/hooks/api/use-titles";
import {usePlansPublic} from "@/hooks/api";

// NOTE: This screen implements the Motiff 7/7 "Pagamento" selection step
// It lets the user choose payment methods for the Title and for the Membership,
// and a due day for the membership. On "Avançar" we move to the card details screen.

enum TitlePaymentMethod {
  Pix = 1,
  Boleto = 2,
  CreditCard = 3
}

const PaymentSelection: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();

  // Fetch title value from API and plan price from plans list
  const {data: titleOptions} = useTitleInstallmentOptions(true);
  const titleValueCents = useMemo(() => {
    const list = titleOptions || [];
    if (!list.length) return 0;
    const one = list.find((o) => o.installments === 1) || list[0];
    return one.value ?? 0;
  }, [titleOptions]);

  const selectedPlanId = params.selectedPlanId
    ? parseInt(params.selectedPlanId as string)
    : 0;
  const {data: plansResponse} = usePlansPublic({page: 1, pageSize: 50});
  const membershipCents = useMemo(() => {
    const plan = plansResponse?.data?.find((p) => p.id === selectedPlanId);
    return typeof plan?.value === "number" ? plan.value : 0;
  }, [plansResponse?.data, selectedPlanId]);

  const formatCurrency = useCallback((cents: number) => {
    const value = cents / 100;
    return `R$ ${value.toFixed(2).replace(".", ",")}`;
  }, []);

  const [titleMethod, setTitleMethod] = useState<TitlePaymentMethod>(
    TitlePaymentMethod.CreditCard
  );
  const [dueDay, setDueDay] = useState<10 | 20 | 30>(10);

  const handleAdvance = useCallback(() => {
    const baseParams = {
      ...params,
      selectedTitlePaymentMethod: String(titleMethod),
      selectedDueDay: String(dueDay),
      totalValue: String(titleValueCents),
      selectedInstallments: "1" // default quando não há seleção explícita
    };

    if (titleMethod === TitlePaymentMethod.CreditCard) {
      router.push({
        pathname: "/(registration)/payment-card-registration",
        params: baseParams
      });
    } else if (titleMethod === TitlePaymentMethod.Pix) {
      router.push({
        pathname: "/(registration)/pix-payment",
        params: baseParams
      });
    } else if (titleMethod === TitlePaymentMethod.Boleto) {
      router.push({
        pathname: "/(registration)/boleto-payment",
        params: baseParams
      });
    }
  }, [router, params, titleMethod, dueDay, membershipCents]);

  return (
    <Screen>
      <ImageBackground
        source={require("@/assets/images/backgroundregistration.png")}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.overlay} />
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          <BackButton />

          {/* Progress */}
          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("paymentSelection.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t("paymentSelection.stepProgress", "7 / 7 Pagamento")}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "100%"}]} />
            </View>
          </View>

          <Text style={styles.pageTitle}>
            {t(
              "paymentSelection.headline",
              "Quase lá! Selecione os métodos de pagamento e finalize o cadastro."
            )}
          </Text>

          {/* Title of adhesion */}
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionHeaderText}>
              {t("paymentSelection.titleOfAdhesion", "Título de adesão")}
            </Text>
            <Text style={styles.sectionHeaderPrice}>
              {formatCurrency(titleValueCents)}
            </Text>
          </View>

          {/* Methods for title */}
          <TouchableOpacity
            activeOpacity={0.9}
            style={[
              styles.methodCard,
              titleMethod === TitlePaymentMethod.Pix &&
                styles.methodCardSelected
            ]}
            onPress={() => setTitleMethod(TitlePaymentMethod.Pix)}
          >
            <View style={styles.methodLeft}>
              <View style={styles.methodIconCircle}>
                <Text style={styles.pixLabel}>PIX</Text>
              </View>
              <View style={styles.methodTextCol}>
                <View style={styles.methodRow}>
                  <Text style={styles.methodTitle}>PIX</Text>
                  <Text style={styles.methodBadge}>(À Vista)</Text>
                </View>
                <Text style={styles.methodSubtitle}>Pagamento imediato</Text>
              </View>
            </View>
            <View
              style={[
                styles.checkbox,
                titleMethod === TitlePaymentMethod.Pix && styles.checkboxChecked
              ]}
            />
          </TouchableOpacity>

          <TouchableOpacity
            activeOpacity={0.9}
            style={[
              styles.methodCard,
              titleMethod === TitlePaymentMethod.Boleto &&
                styles.methodCardSelected
            ]}
            onPress={() => setTitleMethod(TitlePaymentMethod.Boleto)}
          >
            <View style={styles.methodLeft}>
              <View style={styles.methodIconCircle}>
                <Text style={styles.boletoLabel}>📄</Text>
              </View>
              <View style={styles.methodTextCol}>
                <View style={styles.methodRow}>
                  <Text style={styles.methodTitle}>Via Boleto</Text>
                  <Text style={styles.methodBadge}>(À Vista / parcelado)</Text>
                </View>
                <Text style={styles.methodSubtitle}>
                  Aprovação em até 8 horas
                </Text>
              </View>
            </View>
            <View
              style={[
                styles.checkbox,
                titleMethod === TitlePaymentMethod.Boleto &&
                  styles.checkboxChecked
              ]}
            />
          </TouchableOpacity>

          <TouchableOpacity
            activeOpacity={0.9}
            style={[
              styles.methodCard,
              titleMethod === TitlePaymentMethod.CreditCard &&
                styles.methodCardSelected
            ]}
            onPress={() => setTitleMethod(TitlePaymentMethod.CreditCard)}
          >
            <View style={styles.methodLeft}>
              <CreditCardIcon width={24} height={24} />
              <View style={styles.methodTextCol}>
                <View style={styles.methodRow}>
                  <Text style={styles.methodTitle}>
                    {t("paymentSelection.creditCard", "Cartão de Crédito")}
                  </Text>
                  <Text style={styles.methodBadge}>(À vista / parcelado)</Text>
                </View>
                <Text style={styles.methodSubtitle}>Pagamento imediato</Text>
              </View>
            </View>
            <View
              style={[
                styles.checkbox,
                titleMethod === TitlePaymentMethod.CreditCard &&
                  styles.checkboxChecked
              ]}
            />
          </TouchableOpacity>

          {/* Membership */}
          <View style={styles.sectionHeaderRow}>
            <Text style={styles.sectionHeaderText}>
              {t("paymentSelection.memberMonthly", "Mensalidade de membro")}
            </Text>
            <Text style={styles.sectionHeaderPrice}>
              <Text style={styles.sectionHeaderPrice}>
                {formatCurrency(membershipCents)}
              </Text>
              <Text style={styles.perMonth}>/mês</Text>
            </Text>
          </View>

          <View style={[styles.methodCard, styles.methodCardSelected]}>
            <View style={styles.methodLeft}>
              <CreditCardIcon width={24} height={24} />
              <View style={styles.methodTextCol}>
                <View style={styles.methodRow}>
                  <Text style={styles.methodTitle}>
                    {t("paymentSelection.creditCard", "Cartão de Crédito")}
                  </Text>
                  <Text style={styles.methodBadge}>(À vista)</Text>
                </View>
                <Text style={styles.methodSubtitle}>Pagamento imediato</Text>
              </View>
            </View>
            <View style={[styles.checkbox, styles.checkboxChecked]} />
          </View>

          <Text style={styles.smallHeader}>
            {t(
              "paymentSelection.selectDueDay",
              "Selecione o melhor dia de vencimento"
            )}
          </Text>
          <View style={styles.dueDaysRow}>
            {[10, 20, 30].map((day) => (
              <TouchableOpacity
                key={day}
                style={styles.radioItem}
                onPress={() => setDueDay(day as 10 | 20 | 30)}
              >
                <View
                  style={[styles.radio, dueDay === day && styles.radioChecked]}
                />
                <Text
                  style={[
                    styles.radioLabel,
                    dueDay !== day && styles.radioLabelMuted
                  ]}
                >{`Dia ${day}`}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <Text style={styles.noteText}>
            {t(
              "paymentSelection.note1",
              "Obs.: Somente cartões físicos são aceitos para a efetuação do pagamento."
            )}
          </Text>
          <Text style={styles.noteText}>
            {t(
              "paymentSelection.note2",
              "Os valores acima serão cobrados apenas quando você for aprovado como membro."
            )}
          </Text>
          <Text style={styles.noteText}>
            {t(
              "paymentSelection.note3",
              "Os dados de pagamento não serão armazenados pelo Club M Brasil."
            )}
          </Text>

          {/* Buttons */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={() => router.back()}
            >
              <Text style={styles.secondaryButtonText}>
                {t("common.back", "Voltar")}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={handleAdvance}
            >
              <Text style={styles.primaryButtonText}>
                {t("common.next", "Avançar")}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ImageBackground>
    </Screen>
  );
};

export default PaymentSelection;
