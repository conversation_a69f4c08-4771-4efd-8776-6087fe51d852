import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import HandshakeIcon from "@/components/icons/handshake-icon";
import MedalIcon from "@/components/icons/medal-icon";
import UserIcon from "@/components/icons/user-icon";
import {useGuestUser} from "@/contexts/guest-user-context";
import styles from "@/styles/components/user/guest-profile-empty-states.style";

interface GuestEmptyStateProps {
  onRegisterPress?: () => void;
}

export const GuestOpportunitiesEmptyState: React.FC<GuestEmptyStateProps> = ({
  onRegisterPress
}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    if (onRegisterPress) {
      onRegisterPress();
    } else {
      // Clear guest user data (logout from upsell) and navigate to login
      await clearGuestUser();
      router.push("/(auth)/login");
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <HandshakeIcon width={48} height={48} stroke="#666" />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.title}>Suas oportunidades aparecem aqui!</Text>
        <Text style={styles.description}>
          Cadastre-se para criar e gerenciar suas oportunidades de negócio.
          Conecte-se com outros membros e faça networking!
        </Text>
      </View>

      <TouchableOpacity
        style={styles.registerButton}
        onPress={handleRegisterPress}
        activeOpacity={0.8}
      >
        <Text style={styles.registerButtonText}>Cadastre-se agora!</Text>
      </TouchableOpacity>
    </View>
  );
};

export const GuestSealsEmptyState: React.FC<GuestEmptyStateProps> = ({
  onRegisterPress
}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    if (onRegisterPress) {
      onRegisterPress();
    } else {
      // Clear guest user data (logout from upsell) and navigate to login
      await clearGuestUser();
      router.push("/(auth)/login");
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <MedalIcon width={48} height={48} stroke="#666" />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.title}>Conquiste seus selos e objetivos!</Text>
        <Text style={styles.description}>
          Faça seu cadastro e comece a conquistar selos exclusivos. Defina seus
          objetivos profissionais e acompanhe seu progresso!
        </Text>
      </View>

      <TouchableOpacity
        style={styles.registerButton}
        onPress={handleRegisterPress}
        activeOpacity={0.8}
      >
        <Text style={styles.registerButtonText}>Cadastre-se agora!</Text>
      </TouchableOpacity>
    </View>
  );
};

export const GuestAboutMeEmptyState: React.FC<GuestEmptyStateProps> = ({
  onRegisterPress
}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {clearGuestUser} = useGuestUser();

  const handleRegisterPress = async () => {
    if (onRegisterPress) {
      onRegisterPress();
    } else {
      // Clear guest user data (logout from upsell) and navigate to login
      await clearGuestUser();
      router.push("/(auth)/login");
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <UserIcon width={48} height={48} stroke="#666" />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.title}>Complete seu perfil profissional!</Text>
        <Text style={styles.description}>
          Cadastre-se e crie um perfil completo com suas especializações, áreas
          de interesse e contatos. Mostre quem você é!
        </Text>
      </View>

      <TouchableOpacity
        style={styles.registerButton}
        onPress={handleRegisterPress}
        activeOpacity={0.8}
      >
        <Text style={styles.registerButtonText}>Cadastre-se agora!</Text>
      </TouchableOpacity>
    </View>
  );
};
