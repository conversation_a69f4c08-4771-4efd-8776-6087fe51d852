import {useQuery} from "@tanstack/react-query";
import TitlesTermsService from "@/services/api/terms/titles-terms.service";
import {ApiTerm} from "@/models/api/terms.models";

export const TITLE_TERMS_KEYS = {
  all: ["title-terms"] as const,
  list: () => [...TITLE_TERMS_KEYS.all, "list"] as const
};

export const useTitleTerms = (enabled: boolean = true) => {
  return useQuery<ApiTerm[], Error>({
    queryKey: TITLE_TERMS_KEYS.list(),
    queryFn: () => TitlesTermsService.getTitleTerms(),
    enabled,
    staleTime: 5 * 60 * 1000,
    retry: 2
  });
};

