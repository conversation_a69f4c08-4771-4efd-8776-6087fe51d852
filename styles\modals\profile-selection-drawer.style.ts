import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828",
    paddingTop: 50
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 4
  },
  backButton: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  title: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  clearButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  clearText: {
    color: "#FCFCFD",
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18
  },
  description: {
    color: "#EAECF0",
    fontSize: 12,
    lineHeight: 18,
    marginHorizontal: 24,
    marginBottom: 14
  },
  content: {
    flex: 1,
    paddingHorizontal: 24
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40
  },
  loadingText: {
    color: "#DFE9F0",
    fontSize: 14,
    marginTop: 12
  },
  itemsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12
  },
  itemWrapper: {
    marginBottom: 10
  },
  itemBadge: {
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 2,
    borderWidth: 1,
    minHeight: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  itemBadgeSelected: {
    backgroundColor: "#F9FAFB",
    borderColor: "#FFFFFF"
  },
  itemBadgeUnselected: {
    backgroundColor: "transparent",
    borderColor: "#DFE9F0"
  },
  itemBadgeText: {
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    textAlign: "center"
  },
  itemBadgeTextSelected: {
    color: "#1D2939"
  },
  itemBadgeTextUnselected: {
    color: "#DFE9F0"
  },
  addNewSection: {
    marginTop: 18,
    marginBottom: 20
  },
  addNewButton: {
    backgroundColor: "#FFFFFF",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    alignSelf: "flex-start",
    shadowColor: "#101828",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  addNewText: {
    color: "#1D2939",
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  addInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  addInput: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    paddingHorizontal: 12,
    paddingVertical: 8,
    color: "#1D2939",
    fontSize: 14,
    fontWeight: "400"
  },
  addConfirmButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minWidth: 80,
    alignItems: "center",
    justifyContent: "center"
  },
  addConfirmText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "700"
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 34,
    gap: 4
  }
});

export default styles;
