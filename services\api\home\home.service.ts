/**
 * Home Service for Club M
 * Handles operations for home feed and search
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";

// Types for Home Feed based on real API response
export interface HomeFeedItem {
  id: number;
  type: "opportunity" | "poll" | "event" | "announcement";
  title: string;
  description: string;
  imageUrl?: string;
  createdAt: string;
  updatedAt?: string;
  user?: {
    id: number;
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

// Raw API response structure
export interface RawHomeFeedItem {
  franchise: any;
  opportunity: any;
  poll: any;
  user: any;
}

export interface HomeFeedResponse {
  data: HomeFeedItem[];
  votes?: UserVoteData[]; // Array of user votes for polls
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// User vote data structure from home feed
export interface UserVoteData {
  pollId: number;
  optionId: number;
  votedAt: string;
}

// Raw API response structure
export interface RawHomeFeedResponse {
  data: RawHomeFeedItem[];
  votes?: UserVoteData[]; // Array of user votes
  totalItems: number;
  page: number;
  pageCount: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface HomeFeedParams {
  page?: number;
  pageSize?: number;
}

export interface HomeSearchParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface HomeSearchResult {
  id: string;
  type: "opportunity" | "event" | "user" | "product" | "magazine";
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl?: string;
  metadata?: Record<string, any>;
}

export interface HomeSearchResponse {
  data: HomeSearchResult[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export class HomeService {
  private static readonly BASE_PATH = "/api/app/home";

  /**
   * Get home feed with pagination
   */
  static async getHomeFeed(params?: HomeFeedParams): Promise<HomeFeedResponse> {
    console.log("🚀 [HOME-SERVICE] getHomeFeed called with params:", params);

    try {
      console.log("🔄 [HOME-SERVICE] Starting API call...");
      ApiLogger.info("Fetching home feed", params);

      const rawResponse = await firstValueFrom(
        apiClient.get<RawHomeFeedResponse>(`${this.BASE_PATH}/feed`, {
          Page: params?.page || 1,
          PageSize: params?.pageSize || 10
        })
      );

      // Debug log to understand the raw response
      console.log("🔍 [HOME-SERVICE] Raw home feed response:", {
        totalItems: rawResponse.totalItems,
        dataLength: rawResponse.data?.length,
        votesLength: rawResponse.votes?.length || 0,
        firstItem: rawResponse.data?.[0],
        votes: rawResponse.votes,
        rawResponseKeys: Object.keys(rawResponse)
      });

      // Log detailed structure of first item to find pollOptionId
      if (rawResponse.data?.[0]) {
        const firstItem = rawResponse.data[0];
        console.log("🔍 [HOME-SERVICE] First item detailed structure:", {
          itemKeys: Object.keys(firstItem),
          pollOptionId: firstItem.pollOptionId,
          hasPollOptionId: !!firstItem.pollOptionId,
          pollData: firstItem.poll,
          pollKeys: firstItem.poll ? Object.keys(firstItem.poll) : null,
          pollVotes: firstItem.poll?.votes,
          pollVotesStructure: firstItem.poll?.votes?.[0] || null
        });
      }

      // Transform raw response to expected format
      const transformedData = rawResponse.data
        .map((item, index) => {
          console.log(`🔄 [HOME-SERVICE] Transforming item ${index}:`, item);
          const transformed = this.transformHomeFeedItem(item, index);
          console.log(
            `✅ [HOME-SERVICE] Transformed result ${index}:`,
            transformed
          );
          return transformed;
        })
        .filter(Boolean) as HomeFeedItem[];

      const response: HomeFeedResponse = {
        data: transformedData,
        votes: rawResponse.votes || [], // Include user votes data
        totalItems: rawResponse.totalItems,
        totalPages: rawResponse.pageCount,
        currentPage: rawResponse.page,
        pageSize: rawResponse.pageSize,
        hasNextPage: rawResponse.hasNextPage,
        hasPreviousPage: rawResponse.hasPreviousPage
      };

      ApiLogger.info("Home feed fetched successfully", {
        totalItems: response.totalItems,
        currentPage: response.currentPage
      });

      // Log status da request no console
      console.log(`✅ API Status: GET ${this.BASE_PATH}/feed - 200 OK`);
      console.log(`🏠 Home Feed Items: ${response.totalItems} items found`);

      console.log("✅ [HOME-SERVICE] getHomeFeed completed successfully");
      return response;
    } catch (error) {
      console.error("❌ [HOME-SERVICE] Error in getHomeFeed:", error);
      ApiLogger.error("Error fetching home feed", error as Error);
      throw error;
    }
  }

  /**
   * Search content in home
   */
  static async searchHome(
    params?: HomeSearchParams
  ): Promise<HomeSearchResponse> {
    try {
      ApiLogger.info("Searching home content", params);

      const response = await firstValueFrom(
        apiClient.get<HomeSearchResponse>(`${this.BASE_PATH}/search`, {
          Search: params?.search || "",
          Page: params?.page || 1,
          PageSize: params?.pageSize || 10
        })
      );

      ApiLogger.info("Home search completed successfully", {
        totalItems: response.totalItems,
        currentPage: response.currentPage,
        searchTerm: params?.search
      });

      // Log status da request no console
      console.log(`✅ API Status: GET ${this.BASE_PATH}/search - 200 OK`);
      console.log(
        `🔍 Search Results: ${response.totalItems} items found for "${params?.search}"`
      );

      return response;
    } catch (error) {
      ApiLogger.error("Error searching home content", error as Error);
      throw error;
    }
  }

  /**
   * Transform raw home feed item to expected format
   */
  private static transformHomeFeedItem(
    rawItem: RawHomeFeedItem,
    index: number
  ): HomeFeedItem | null {
    try {
      // Determine the type and extract data based on what's available
      // PRIORITIZE POLLS - Check for poll first since they can coexist with opportunities
      if (rawItem.poll) {
        // Log poll votes data for debugging
        console.log(`🗳️ [HOME-SERVICE] Poll ${rawItem.poll.id} votes data:`, {
          pollId: rawItem.poll.id,
          votes: rawItem.poll.votes,
          votesLength: rawItem.poll.votes?.length || 0,
          votesStructure: rawItem.poll.votes?.[0] || null
        });

        return {
          id: rawItem.poll.id || `poll-${index}`,
          type: "poll",
          title: rawItem.poll.title || rawItem.poll.question || "Enquete",
          description: rawItem.poll.description || rawItem.poll.question || "",
          imageUrl: rawItem.poll.imageUrl,
          createdAt: rawItem.poll.createdAt || new Date().toISOString(),
          updatedAt: rawItem.poll.updatedAt,
          user: rawItem.poll.user
            ? {
                id: rawItem.poll.user.id,
                name: rawItem.poll.user.name,
                avatar: rawItem.poll.user.avatar || rawItem.poll.user.avatarId
              }
            : rawItem.user
            ? {
                id: rawItem.user.id,
                name: rawItem.user.name,
                avatar: rawItem.user.avatar || rawItem.user.avatarId
              }
            : undefined,
          metadata: {
            options: rawItem.poll.options,
            totalVotes: rawItem.poll.totalVotes,
            votes: rawItem.poll.votes, // Include votes data from poll
            franchise: rawItem.franchise // Include franchise data for author display
          }
        };
      }

      // Process opportunities if no poll exists
      if (rawItem.opportunity) {
        // Debug log to understand the data structure
        console.log("🔍 [HOME-SERVICE] Processing opportunity:", {
          opportunityId: rawItem.opportunity.id,
          hasOpportunityUser: !!rawItem.opportunity.user,
          hasTopLevelUser: !!rawItem.user,
          opportunityUserData: rawItem.opportunity.user,
          topLevelUserData: rawItem.user
        });

        return {
          id: rawItem.opportunity.id || `opportunity-${index}`,
          type: "opportunity",
          title: rawItem.opportunity.title || "Oportunidade",
          description: rawItem.opportunity.description || "",
          imageUrl: rawItem.opportunity.imageUrl,
          createdAt: rawItem.opportunity.createdAt || new Date().toISOString(),
          updatedAt: rawItem.opportunity.updatedAt,
          user: rawItem.opportunity.user
            ? {
                id: rawItem.opportunity.user.id,
                name: rawItem.opportunity.user.name,
                avatar:
                  rawItem.opportunity.user.avatar ||
                  rawItem.opportunity.user.avatarId
              }
            : rawItem.user
            ? {
                id: rawItem.user.id,
                name: rawItem.user.name,
                avatar: rawItem.user.avatar || rawItem.user.avatarId
              }
            : undefined,
          metadata: {
            value: rawItem.opportunity.value,
            segment: rawItem.opportunity.segment,
            currentStage: rawItem.opportunity.currentStage
          }
        };
      }

      // Check for event data
      if (rawItem.franchise?.event || (rawItem as any).event) {
        const eventData = rawItem.franchise?.event || (rawItem as any).event;
        return {
          id: eventData.id || `event-${index}`,
          type: "event",
          title: eventData.title || eventData.name || "Evento",
          description:
            eventData.description || eventData.shortDescription || "",
          imageUrl: eventData.imageUrl || eventData.images?.[0]?.url,
          createdAt:
            eventData.createdAt ||
            eventData.startDate ||
            new Date().toISOString(),
          updatedAt: eventData.updatedAt,
          user: eventData.organizer
            ? {
                id: eventData.organizer.id,
                name: eventData.organizer.name,
                avatar: eventData.organizer.avatar
              }
            : eventData.user
            ? {
                id: eventData.user.id,
                name: eventData.user.name,
                avatar: eventData.user.avatar
              }
            : undefined,
          metadata: {
            location: eventData.location?.name || eventData.location,
            startDate: eventData.startDate,
            endDate: eventData.endDate,
            capacity: eventData.capacity,
            registeredCount: eventData.registeredCount
          }
        };
      }

      // If no recognizable content, return null
      return null;
    } catch (error) {
      console.warn("Error transforming home feed item:", error);
      return null;
    }
  }

  /**
   * Utility functions for home feed
   */
  static formatFeedItemDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = Math.floor(
        (now.getTime() - date.getTime()) / (1000 * 60 * 60)
      );

      if (diffInHours < 1) {
        return "Agora mesmo";
      } else if (diffInHours < 24) {
        return `${diffInHours}h atrás`;
      } else if (diffInHours < 48) {
        return "Ontem";
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} dias atrás`;
      }
    } catch {
      return dateString;
    }
  }

  static getFeedItemTypeName(type: HomeFeedItem["type"]): string {
    const typeNames = {
      opportunity: "Oportunidade",
      event: "Evento",
      announcement: "Anúncio",
      poll: "Enquete"
    };
    return typeNames[type] || type;
  }

  static getSearchResultTypeName(type: HomeSearchResult["type"]): string {
    const typeNames = {
      opportunity: "Oportunidade",
      event: "Evento",
      user: "Usuário",
      product: "Produto",
      magazine: "Revista"
    };
    return typeNames[type] || type;
  }
}
