import {StyleSheet} from "react-native";

export default StyleSheet.create({
  backgroundImage: {
    flex: 1
  },
  backgroundImageStyle: {
    resizeMode: "cover"
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.35)"
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 24
  },
  progressContainer: {
    marginBottom: 16
  },
  progressTitle: {
    fontSize: 12,
    color: "#DFE9F0",
    textAlign: "center"
  },
  progressStep: {
    fontSize: 14,
    color: "#DFE9F0",
    textAlign: "center"
  },
  progressBar: {
    height: 8,
    backgroundColor: "#2A2A2A",
    borderRadius: 4,
    marginTop: 8
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#E36A1B",
    borderRadius: 4
  },
  pageTitle: {
    color: "#DFE9F0",
    fontSize: 18,
    lineHeight: 28,
    textAlign: "left",
    marginVertical: 8
  },
  sectionHeaderRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 8
  },
  sectionHeaderText: {
    color: "#DFE9F0",
    fontSize: 14
  },
  sectionHeaderPrice: {
    color: "#E36A1B",
    fontSize: 14,
    fontWeight: "700"
  },
  perMonth: {
    color: "#E36A1B",
    fontSize: 12,
    fontWeight: "700"
  },
  methodCard: {
    backgroundColor: "#1E1E2D",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 2}
  },
  methodCardSelected: {
    borderColor: "#0D9383"
  },
  methodLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  methodIconCircle: {
    width: 34,
    height: 24,
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#32BCAD"
  },
  pixLabel: {
    color: "#fff",
    fontWeight: "700"
  },
  boletoLabel: {
    color: "#fff"
  },
  methodTextCol: {
    gap: 4
  },
  methodRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6
  },
  methodTitle: {
    color: "#FFFFFF",
    fontSize: 12
  },
  methodBadge: {
    color: "#FFFFFF",
    fontSize: 10
  },
  methodSubtitle: {
    color: "#FFFFFF",
    fontSize: 10,
    opacity: 0.9
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    backgroundColor: "#FFFFFF"
  },
  checkboxChecked: {
    backgroundColor: "#0D9383",
    borderColor: "#0D9383"
  },
  smallHeader: {
    color: "#FFFFFF",
    fontSize: 12,
    marginTop: 8
  },
  dueDaysRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8
  },
  radioItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  radio: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    backgroundColor: "#FFFFFF"
  },
  radioChecked: {
    backgroundColor: "#E36A1B",
    borderColor: "#E36A1B"
  },
  radioLabel: {
    color: "#FCFCFD",
    fontSize: 14
  },
  radioLabelMuted: {
    opacity: 0.7
  },
  noteText: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 20,
    marginTop: 8
  },
  buttonRow: {
    flexDirection: "row",
    gap: 16,
    marginTop: 12
  },
  secondaryButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FCFCFD",
    alignItems: "center",
    justifyContent: "center"
  },
  secondaryButtonText: {
    color: "#FCFCFD",
    fontSize: 16
  },
  primaryButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    backgroundColor: "#0F7C4D",
    alignItems: "center",
    justifyContent: "center"
  },
  primaryButtonText: {
    color: "#FCFCFD",
    fontSize: 16
  }
});

