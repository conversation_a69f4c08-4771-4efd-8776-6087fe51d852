/**
 * Terms Modal Styles
 * Dark theme styling for terms and conditions modal
 */

import {StyleSheet} from "react-native";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A"
  },
  
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: "#1A1A1A",
    borderBottomWidth: 1,
    borderBottomColor: "#333333"
  },
  
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#333333",
    alignItems: "center",
    justifyContent: "center"
  },
  
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    textAlign: "center",
    marginHorizontal: 16
  },
  
  headerSpacer: {
    width: 40
  },
  
  content: {
    flex: 1,
    backgroundColor: "#1A1A1A"
  },
  
  contentContainer: {
    padding: 20,
    paddingBottom: 40
  },
  
  termContent: {
    marginBottom: 24
  },
  
  termText: {
    fontSize: 16,
    lineHeight: 24,
    color: "#E0E0E0",
    textAlign: "justify"
  },
  
  versionInfo: {
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#333333",
    alignItems: "center"
  },
  
  versionText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#B0B0B0",
    marginBottom: 4
  },
  
  dateText: {
    fontSize: 12,
    color: "#808080"
  },
  
  actions: {
    flexDirection: "row",
    padding: 20,
    paddingBottom: 40,
    backgroundColor: "#1A1A1A",
    borderTopWidth: 1,
    borderTopColor: "#333333",
    gap: 12
  },
  
  button: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8
  },
  
  declineButton: {
    backgroundColor: "#333333",
    borderWidth: 1,
    borderColor: "#555555"
  },
  
  acceptButton: {
    backgroundColor: "#007AFF"
  },
  
  declineButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF"
  },
  
  acceptButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF"
  }
});

export default styles;
