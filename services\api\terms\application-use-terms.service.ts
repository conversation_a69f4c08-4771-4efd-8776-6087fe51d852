/**
 * Application Use Terms Service
 * Endpoint: GET /api/app/system-config/application-use
 * Returns the application use term (ApiTerm)
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiTerm} from "@/models/api/terms.models";

export class ApplicationUseTermsService {
  private static readonly BASE_PATH = "/api/app/system-config/application-use";

  /**
   * Fetch Application Use term
   */
  static async getApplicationUseTerm(): Promise<ApiTerm | null> {
    try {
      ApiLogger.info("Buscando termo de uso do aplicativo");
      const response = await firstValueFrom(
        apiClient.get<ApiTerm | null>(this.BASE_PATH)
      );

      if (!response) {
        ApiLogger.warn("Nenhum termo de uso do aplicativo retornado");
        return null;
      }

      ApiLogger.info("Termo de uso do aplicativo encontrado", {
        id: (response as any)?.id,
        lastVersion: (response as any)?.lastVersion,
        versionsCount: (response as any)?.versions?.length || 0
      });

      return response as ApiTerm;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar termo de uso do aplicativo",
        error as Error
      );
      throw error;
    }
  }
}

export default ApplicationUseTermsService;

