/**
 * Serviço de Notificações para ClubM
 * Implementa operações completas para notificações push, in-app e preferências
 */

import {apiClient} from "../base/api-client";
import {
  Notification,
  NotificationPreferences,
  NotificationStats,
  UpdateNotificationPreferencesRequest,
  PushNotificationRegistration,
  NotificationAnalytics,
  NotificationType,
  NotificationStatus,
  NotificationPriority,
  NotificationChannel
} from "@/models/api/notifications.models";
import {PaginationResponse} from "@/models/api/common.models";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";

export interface NotificationsListParams {
  page?: number;
  pageSize?: number;
  type?: NotificationType;
  priority?: NotificationPriority;
  status?: NotificationStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export class NotificationsService {
  private static readonly USER_NOTIFICATIONS_PATH =
    "/api/app/notifications/@me";
  private static readonly NOTIFICATIONS_PATH = "/api/notifications";
  private static readonly APP_NOTIFICATIONS_PATH = "/api/app/notifications";

  /**
   * Buscar lista de notificações do usuário
   */
  static async getNotifications(
    params?: NotificationsListParams
  ): Promise<PaginationResponse<Notification>> {
    try {
      ApiLogger.info("Buscando notificações do usuário", params);

      // Preparar parâmetros da query, incluindo o novo filtro isRead
      const queryParams = params ? {...params} : {};

      console.log("🔍 [API] Parâmetros enviados para API:", queryParams);
      console.log("🔍 [API] URL:", this.USER_NOTIFICATIONS_PATH);

      const response = await firstValueFrom(
        apiClient.get<PaginationResponse<Notification>>(
          this.USER_NOTIFICATIONS_PATH,
          queryParams
        )
      );

      ApiLogger.info(`Encontradas ${response.data.length} notificações`);
      console.log(
        "🔍 [API] Resposta da API:",
        JSON.stringify(response, null, 2)
      );
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar notificações", error as Error);
      throw error;
    }
  }

  /**
   * Buscar notificação específica por ID
   */
  static async getNotificationById(id: string): Promise<Notification> {
    try {
      ApiLogger.info("Buscando notificação por ID", {id});

      const response = await firstValueFrom(
        apiClient.get<Notification>(`${this.APP_NOTIFICATIONS_PATH}/${id}`)
      );

      ApiLogger.info("Notificação encontrada", {id});
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar notificação", error as Error, {id});
      throw error;
    }
  }

  /**
   * Marcar notificação como lida
   */
  static async markAsRead(id: string): Promise<Notification> {
    try {
      ApiLogger.info("Marcando notificação como lida", {id});

      const response = await firstValueFrom(
        apiClient.patch<any>(`${this.APP_NOTIFICATIONS_PATH}/${id}/read`)
      );

      ApiLogger.info("Notificação marcada como lida", {id});
      console.log(
        "📝 [MARK-AS-READ] Response from API:",
        JSON.stringify(response, null, 2)
      );

      // A API retorna uma estrutura diferente, precisamos mapear para nosso modelo
      // Estrutura da API: { id, title, body, status, type, notificationsUsers: [...] }
      const mappedNotification: Notification = {
        id: response.id?.toString() || id,
        userId: response.notificationsUsers?.[0]?.userId?.toString() || "",
        type: this.mapNotificationType(response.type),
        priority: NotificationPriority.NORMAL,
        status: this.mapNotificationStatus(
          response.notificationsUsers?.[0]?.status || response.status
        ),
        title: response.title || "",
        message: response.body || "",
        data: {},
        imageUrl: undefined,
        actionUrl: undefined,
        actionText: undefined,
        channels: [NotificationChannel.IN_APP],
        scheduledAt: response.scheduledFor,
        sentAt: response.notificationsUsers?.[0]?.sentAt,
        readAt:
          response.notificationsUsers?.[0]?.readAt || new Date().toISOString(),
        expiresAt: undefined,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt
      };

      console.log(
        "✅ [MARK-AS-READ] Mapped notification:",
        JSON.stringify(mappedNotification, null, 2)
      );
      return mappedNotification;
    } catch (error) {
      ApiLogger.error("Erro ao marcar notificação como lida", error as Error, {
        id
      });
      throw error;
    }
  }

  /**
   * Mapear tipo numérico para enum
   */
  private static mapNotificationType(typeNumber: number): NotificationType {
    switch (typeNumber) {
      case 1:
        return NotificationType.EVENT;
      case 2:
        return NotificationType.ANNOUNCEMENT;
      case 3:
        return NotificationType.SYSTEM;
      case 4:
        return NotificationType.PROMOTION;
      case 5:
        return NotificationType.REMINDER;
      case 6:
        return NotificationType.CHAT;
      case 7:
        return NotificationType.MESSAGE;
      case 8:
        return NotificationType.PRODUCT;
      default:
        return NotificationType.GENERAL;
    }
  }

  /**
   * Mapear status numérico para enum
   */
  private static mapNotificationStatus(
    statusNumber: number
  ): NotificationStatus {
    switch (statusNumber) {
      case 1:
        return NotificationStatus.UNREAD;
      case 2:
        return NotificationStatus.READ;
      case 3:
        return NotificationStatus.ARCHIVED;
      case 4:
        return NotificationStatus.UNREAD;
      default:
        return NotificationStatus.UNREAD;
    }
  }

  /**
   * Marcar todas as notificações como lidas
   * Como não existe endpoint específico, marca cada notificação individualmente
   */
  static async markAllAsRead(): Promise<{success: boolean; count: number}> {
    try {
      ApiLogger.info("Marcando todas as notificações como lidas");

      // Primeiro, buscar todas as notificações não lidas
      const notificationsResponse = await this.getNotifications({
        page: 1,
        pageSize: 100 // Buscar até 100 notificações
      });

      const unreadNotifications = notificationsResponse.data.filter(
        (notification: any) => {
          // Verificar se a notificação está não lida
          const status =
            notification.notification?.status || notification.status;
          return status === 1; // 1 = UNREAD
        }
      );

      if (unreadNotifications.length === 0) {
        return {success: true, count: 0};
      }

      // Marcar cada notificação como lida
      const markPromises = unreadNotifications.map((item: any) => {
        const notificationId =
          item.id?.toString() ||
          item.notificationId?.toString() ||
          item.notification?.id?.toString();
        return this.markAsRead(notificationId);
      });

      await Promise.all(markPromises);

      ApiLogger.info("Todas as notificações marcadas como lidas", {
        count: unreadNotifications.length
      });

      return {success: true, count: unreadNotifications.length};
    } catch (error) {
      ApiLogger.error(
        "Erro ao marcar todas as notificações como lidas",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Arquivar notificação
   */
  static async archiveNotification(id: string): Promise<Notification> {
    try {
      ApiLogger.info("Arquivando notificação", {id});

      const response = await firstValueFrom(
        apiClient.put<Notification>(
          `${this.APP_NOTIFICATIONS_PATH}/${id}/archive`
        )
      );

      ApiLogger.info("Notificação arquivada", {id});
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao arquivar notificação", error as Error, {id});
      throw error;
    }
  }

  /**
   * Deletar notificação
   */
  static async deleteNotification(id: string): Promise<void> {
    try {
      ApiLogger.info("Deletando notificação", {id});

      await firstValueFrom(
        apiClient.delete(`${this.APP_NOTIFICATIONS_PATH}/${id}`)
      );

      ApiLogger.info("Notificação deletada", {id});
    } catch (error) {
      ApiLogger.error("Erro ao deletar notificação", error as Error, {id});
      throw error;
    }
  }

  /**
   * Buscar estatísticas de notificações
   * NOTA: Endpoint /stats não existe na API, calculando localmente
   */
  static async getNotificationStats(): Promise<NotificationStats> {
    try {
      ApiLogger.info("Calculando estatísticas de notificações localmente");

      // Buscar notificações do usuário para calcular estatísticas
      const notificationsResponse = await this.getNotifications({
        page: 1,
        pageSize: 1000 // Buscar um número grande para ter todas as notificações
      });

      const notifications = notificationsResponse.data || [];

      // Calcular estatísticas
      const totalNotifications = notifications.length;
      const unreadCount = notifications.filter(
        (n) => n.status === NotificationStatus.UNREAD
      ).length;
      const readCount = notifications.filter(
        (n) => n.status === NotificationStatus.READ
      ).length;
      const archivedCount = notifications.filter(
        (n) => n.status === NotificationStatus.ARCHIVED
      ).length;

      // Calcular notificações por tipo
      const byType = notifications.reduce((acc, notification) => {
        const type = notification.type || "unknown";
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Calcular notificações por prioridade
      const byPriority = notifications.reduce((acc, notification) => {
        const priority = notification.priority || "unknown";
        acc[priority] = (acc[priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Encontrar última notificação
      const lastNotificationAt =
        notifications.length > 0
          ? notifications.sort(
              (a, b) =>
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
            )[0].createdAt
          : undefined;

      const stats: NotificationStats = {
        totalCount: totalNotifications,
        totalNotifications,
        unreadCount,
        readCount,
        archivedCount,
        byType,
        byPriority,
        lastNotificationAt
      };

      ApiLogger.info("Estatísticas de notificações calculadas", stats);
      return stats;
    } catch (error) {
      ApiLogger.error(
        "Erro ao calcular estatísticas de notificações",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Buscar preferências de notificação do usuário
   */
  static async getNotificationPreferences(): Promise<NotificationPreferences> {
    try {
      ApiLogger.info("Buscando preferências de notificação");

      const response = await firstValueFrom(
        apiClient.get<NotificationPreferences>(
          `${this.APP_NOTIFICATIONS_PATH}/preferences`
        )
      );

      ApiLogger.info("Preferências de notificação obtidas");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar preferências de notificação",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Atualizar preferências de notificação
   */
  static async updateNotificationPreferences(
    preferences: UpdateNotificationPreferencesRequest
  ): Promise<NotificationPreferences> {
    try {
      ApiLogger.info("Atualizando preferências de notificação", preferences);

      const response = await firstValueFrom(
        apiClient.put<NotificationPreferences>(
          `${this.APP_NOTIFICATIONS_PATH}/preferences`,
          preferences
        )
      );

      ApiLogger.info("Preferências de notificação atualizadas");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao atualizar preferências de notificação",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Registrar dispositivo para push notifications
   */
  static async registerDevice(
    registration: PushNotificationRegistration
  ): Promise<{success: boolean}> {
    try {
      ApiLogger.info("Registrando dispositivo para push notifications", {
        platform: registration.platform,
        deviceModel: registration.deviceModel
      });

      const response = await firstValueFrom(
        apiClient.post<{success: boolean}>(
          `${this.APP_NOTIFICATIONS_PATH}/devices`,
          registration
        )
      );

      ApiLogger.info("Dispositivo registrado para push notifications");
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao registrar dispositivo", error as Error);
      throw error;
    }
  }

  /**
   * Remover registro de dispositivo
   */
  static async unregisterDevice(
    deviceToken: string
  ): Promise<{success: boolean}> {
    try {
      ApiLogger.info("Removendo registro de dispositivo", {
        deviceToken: deviceToken.substring(0, 10) + "..."
      });

      const response = await firstValueFrom(
        apiClient.delete<{success: boolean}>(
          `${this.APP_NOTIFICATIONS_PATH}/devices/${deviceToken}`
        )
      );

      ApiLogger.info("Registro de dispositivo removido");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao remover registro de dispositivo",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Testar envio de notificação push
   */
  static async testPushNotification(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      ApiLogger.info("Enviando notificação push de teste");

      const response = await firstValueFrom(
        apiClient.post<{success: boolean; message: string}>(
          `${this.APP_NOTIFICATIONS_PATH}/test-push`
        )
      );

      ApiLogger.info("Notificação push de teste enviada");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao enviar notificação push de teste",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Buscar analytics de notificações
   */
  static async getNotificationAnalytics(
    period: "day" | "week" | "month" | "year" = "week"
  ): Promise<NotificationAnalytics> {
    try {
      ApiLogger.info("Buscando analytics de notificações", {period});

      const response = await firstValueFrom(
        apiClient.get<NotificationAnalytics>(
          `${this.APP_NOTIFICATIONS_PATH}/analytics`,
          {
            period
          }
        )
      );

      ApiLogger.info("Analytics de notificações obtidas");
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar analytics de notificações",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Buscar notificações não lidas
   */
  static async getUnreadNotifications(): Promise<Notification[]> {
    try {
      ApiLogger.info("Buscando notificações não lidas");

      const response = await firstValueFrom(
        apiClient.get<PaginationResponse<Notification>>(
          this.USER_NOTIFICATIONS_PATH,
          {
            status: NotificationStatus.UNREAD,
            pageSize: 50
          }
        )
      );

      ApiLogger.info(
        `Encontradas ${response.data.length} notificações não lidas`
      );
      return response.data;
    } catch (error: any) {
      // Se for 404, retornar array vazio em vez de erro
      if (error?.status === 404) {
        ApiLogger.warn(
          "Endpoint de notificações não encontrado, retornando array vazio"
        );
        return [];
      }

      ApiLogger.error("Erro ao buscar notificações não lidas", error as Error);
      throw error;
    }
  }

  /**
   * Limpar notificações antigas
   */
  static async clearOldNotifications(
    olderThanDays: number = 30
  ): Promise<{success: boolean; deletedCount: number}> {
    try {
      ApiLogger.info("Limpando notificações antigas", {olderThanDays});

      const response = await firstValueFrom(
        apiClient.delete<{success: boolean; deletedCount: number}>(
          `${this.APP_NOTIFICATIONS_PATH}/cleanup`,
          {
            olderThanDays
          }
        )
      );

      ApiLogger.info("Notificações antigas limpas", {
        deletedCount: response.deletedCount
      });
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao limpar notificações antigas", error as Error);
      throw error;
    }
  }
}

export default NotificationsService;
