/**
 * Utility functions for managing poll vote persistence
 * Handles user vote data from home feed and visual state restoration
 */

import { UserVoteData } from "@/services/api/home/<USER>";

export interface PollVoteState {
  pollId: number;
  selectedOptionId: number;
  hasVoted: boolean;
  votedAt: string;
}

export class PollVotesUtils {
  /**
   * Extract user vote for a specific poll from votes array
   */
  static getUserVoteForPoll(
    pollId: number,
    votes: UserVoteData[]
  ): UserVoteData | null {
    if (!votes || votes.length === 0) return null;
    
    return votes.find(vote => vote.pollId === pollId) || null;
  }

  /**
   * Check if user has voted on a specific poll
   */
  static hasUserVotedOnPoll(
    pollId: number,
    votes: UserVoteData[]
  ): boolean {
    return this.getUserVoteForPoll(pollId, votes) !== null;
  }

  /**
   * Get the option ID that user voted for in a specific poll
   */
  static getUserVotedOptionId(
    pollId: number,
    votes: UserVoteData[]
  ): number | null {
    const userVote = this.getUserVoteForPoll(pollId, votes);
    return userVote?.optionId || null;
  }

  /**
   * Convert votes array to poll vote states for easy component consumption
   */
  static convertVotesToStates(votes: UserVoteData[]): Map<number, PollVoteState> {
    const statesMap = new Map<number, PollVoteState>();
    
    if (!votes || votes.length === 0) return statesMap;

    votes.forEach(vote => {
      statesMap.set(vote.pollId, {
        pollId: vote.pollId,
        selectedOptionId: vote.optionId,
        hasVoted: true,
        votedAt: vote.votedAt
      });
    });

    return statesMap;
  }

  /**
   * Get poll vote state for a specific poll
   */
  static getPollVoteState(
    pollId: number,
    votes: UserVoteData[]
  ): PollVoteState | null {
    const userVote = this.getUserVoteForPoll(pollId, votes);
    
    if (!userVote) return null;

    return {
      pollId: userVote.pollId,
      selectedOptionId: userVote.optionId,
      hasVoted: true,
      votedAt: userVote.votedAt
    };
  }

  /**
   * Log vote data for debugging
   */
  static logVoteData(pollId: number, votes: UserVoteData[]): void {
    const userVote = this.getUserVoteForPoll(pollId, votes);
    
    console.log(`🗳️ [POLL-VOTES] Vote data for poll ${pollId}:`, {
      pollId,
      totalVotes: votes?.length || 0,
      userVote,
      hasVoted: !!userVote,
      selectedOptionId: userVote?.optionId || null,
      votedAt: userVote?.votedAt || null
    });
  }

  /**
   * Validate vote data structure
   */
  static isValidVoteData(vote: any): vote is UserVoteData {
    return (
      vote &&
      typeof vote.pollId === 'number' &&
      typeof vote.optionId === 'number' &&
      typeof vote.votedAt === 'string'
    );
  }

  /**
   * Filter and validate votes array
   */
  static sanitizeVotes(votes: any[]): UserVoteData[] {
    if (!Array.isArray(votes)) return [];
    
    return votes.filter(this.isValidVoteData);
  }
}
