import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: stylesConstants.colors.primary,
    borderTopWidth: 1,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24
  },
  backgroundImage: {
    width: "100%",
    height: 150,
    marginTop: -60,
    transform: [
      {
        scale: 1.5
      }
    ],
    opacity: 0.2
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24
  },
  loadingText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    marginTop: 16,
    textAlign: "center"
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.border
  },
  logoContainer: {
    marginRight: 16
  },
  logo: {
    width: 64,
    height: 64,
    borderRadius: 8
  },
  logoPlaceholder: {
    width: 64,
    height: 64,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },
  headerInfo: {
    flex: 1
  },
  partnerName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "700",
    lineHeight: 28,
    marginBottom: 4
  },
  partnerCategory: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 2
  },
  partnerWebsite: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  descriptionSection: {
    paddingHorizontal: 24,
    paddingVertical: 20
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24,
    marginBottom: 12
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  seeMoreText: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    marginTop: 8
  },
  benefitsSection: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.border
  },
  benefitsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  viewAllText: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20
  },
  benefitsList: {
    gap: 12
  },
  benefitCard: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },
  benefitIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  benefitInfo: {
    flex: 1,
    marginRight: 8
  },
  benefitTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    marginBottom: 4
  },
  benefitDescription: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  noBenefitsContainer: {
    alignItems: "center",
    paddingVertical: 32
  },
  noBenefitsText: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 22,
    textAlign: "center"
  },
  actionSection: {
    paddingHorizontal: 24,
    paddingVertical: 20
  },
  viewAllBenefitsButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: stylesConstants.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24
  },
  viewAllBenefitsButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    marginRight: 8
  },
  iconLine: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 12
  },
  iconText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  iconTextContainer: {
    flex: 1,
    flexDirection: "column"
  },
  iconBox: {
    width: 40,
    height: 40,
    borderRadius: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border,
    backgroundColor: "#8A8F98",
    justifyContent: "center",
    alignItems: "center"
  },
  iconTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "100",
    lineHeight: 20
  },

  titleSocial: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 20
  }
});

export default styles;
