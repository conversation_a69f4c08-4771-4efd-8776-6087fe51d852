# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec cmake_object_order_depends_target_react_codegen_pagerview cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rnpdf cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/C_/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\C_\Users\HJbeu\OneDrive\Documentos\Projeto\club-m-app\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\C_\Users\HJbeu\OneDrive\Documentos\Projeto\club-m-app\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libappmodules.so

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/ReactNativeBlobUtilSpec-generated.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ComponentDescriptors.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/EventEmitters.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/Props.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ReactNativeBlobUtilSpecJSI-generated.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/ShadowNodes.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/States.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o CMakeFiles/appmodules.dir/C_/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so || C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec ReactNativeBlobUtilSpec_autolinked_build/react_codegen_ReactNativeBlobUtilSpec pagerview_autolinked_build/react_codegen_pagerview rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rnpdf_autolinked_build/react_codegen_rnpdf rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so  C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so  C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_ReactNativeBlobUtilSpec


#############################################
# Order-only phony target for react_codegen_ReactNativeBlobUtilSpec

build cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec: phony || ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/ReactNativeBlobUtilSpec-generated.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/ReactNativeBlobUtilSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\ReactNativeBlobUtilSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\react\renderer\components\ReactNativeBlobUtilSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\react\renderer\components\ReactNativeBlobUtilSpec

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/Props.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ReactNativeBlobUtilSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/ReactNativeBlobUtilSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\react\renderer\components\ReactNativeBlobUtilSpec\ReactNativeBlobUtilSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\react\renderer\components\ReactNativeBlobUtilSpec

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/States.cpp.o: CXX_COMPILER__react_codegen_ReactNativeBlobUtilSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec/States.cpp || cmake_object_order_depends_target_react_codegen_ReactNativeBlobUtilSpec
  DEP_FILE = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/react/renderer/components/ReactNativeBlobUtilSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir
  OBJECT_FILE_DIR = ReactNativeBlobUtilSpec_autolinked_build\CMakeFiles\react_codegen_ReactNativeBlobUtilSpec.dir\2361107e616b8ab90a9b526b4425e420



#############################################
# Object library react_codegen_ReactNativeBlobUtilSpec

build ReactNativeBlobUtilSpec_autolinked_build/react_codegen_ReactNativeBlobUtilSpec: phony ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/ReactNativeBlobUtilSpec-generated.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ComponentDescriptors.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/EventEmitters.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/Props.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/react/renderer/components/ReactNativeBlobUtilSpec/ReactNativeBlobUtilSpecJSI-generated.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/ShadowNodes.cpp.o ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/react_codegen_ReactNativeBlobUtilSpec.dir/2361107e616b8ab90a9b526b4425e420/States.cpp.o


#############################################
# Utility command for edit_cache

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\ReactNativeBlobUtilSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build ReactNativeBlobUtilSpec_autolinked_build/edit_cache: phony ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\ReactNativeBlobUtilSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build ReactNativeBlobUtilSpec_autolinked_build/rebuild_cache: phony ReactNativeBlobUtilSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_pagerview


#############################################
# Order-only phony target for react_codegen_pagerview

build cmake_object_order_depends_target_react_codegen_pagerview: phony || pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/pagerview-generated.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\pagerview-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/Props.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/States.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview

build pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o: CXX_COMPILER__react_codegen_pagerview_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/pagerviewJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_pagerview
  DEP_FILE = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview\pagerviewJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir
  OBJECT_FILE_DIR = pagerview_autolinked_build\CMakeFiles\react_codegen_pagerview.dir\react\renderer\components\pagerview



#############################################
# Object library react_codegen_pagerview

build pagerview_autolinked_build/react_codegen_pagerview: phony pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o


#############################################
# Utility command for edit_cache

build pagerview_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\pagerview_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build pagerview_autolinked_build/edit_cache: phony pagerview_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build pagerview_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\pagerview_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build pagerview_autolinked_build/rebuild_cache: phony pagerview_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnpdf


#############################################
# Order-only phony target for react_codegen_rnpdf

build cmake_object_order_depends_target_react_codegen_rnpdf: phony || rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/Props.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/States.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\rnpdfJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o: CXX_COMPILER__react_codegen_rnpdf_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/rnpdf-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\rnpdf-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir



#############################################
# Object library react_codegen_rnpdf

build rnpdf_autolinked_build/react_codegen_rnpdf: phony rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnpdf_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnpdf_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnpdf_autolinked_build/edit_cache: phony rnpdf_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnpdf_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnpdf_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnpdf_autolinked_build/rebuild_cache: phony rnpdf_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e44745060bc3d6dfe473b785e4d9ac\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e44745060bc3d6dfe473b785e4d9ac

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e44745060bc3d6dfe473b785e4d9ac\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e44745060bc3d6dfe473b785e4d9ac

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b3e4510636d7fc7d80894c30ddaeab1d\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b3e4510636d7fc7d80894c30ddaeab1d

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\028e3213fe8881349ad29c9b097e59d4\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\028e3213fe8881349ad29c9b097e59d4\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\81652923ecfc9bdd5f0e3b73b09a03ba\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\81652923ecfc9bdd5f0e3b73b09a03ba\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\028e3213fe8881349ad29c9b097e59d4\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\028e3213fe8881349ad29c9b097e59d4\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\81652923ecfc9bdd5f0e3b73b09a03ba\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\81652923ecfc9bdd5f0e3b73b09a03ba\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b3e4510636d7fc7d80894c30ddaeab1d\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b3e4510636d7fc7d80894c30ddaeab1d

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/16d00a0261cac9ff43ce46900d57e7db/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\16d00a0261cac9ff43ce46900d57e7db\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\16d00a0261cac9ff43ce46900d57e7db\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_safeareacontext.so

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/16d00a0261cac9ff43ce46900d57e7db/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b1e29d414343da1e7d3f8ac8e36d3da3\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b1e29d414343da1e7d3f8ac8e36d3da3\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b1e29d414343da1e7d3f8ac8e36d3da3\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b1e29d414343da1e7d3f8ac8e36d3da3\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\31d41a69d6af3f8a771be753aad03dfb\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\31d41a69d6af3f8a771be753aad03dfb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\31d41a69d6af3f8a771be753aad03dfb\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\31d41a69d6af3f8a771be753aad03dfb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\beec13cd9beffcff163006cda4d1844b\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ea49dc9aacd966da88f6745ee0bf23b3/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ea49dc9aacd966da88f6745ee0bf23b3\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ea49dc9aacd966da88f6745ee0bf23b3\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c3b00cb14507cd5ef58b02bd72d3c86f\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c3b00cb14507cd5ef58b02bd72d3c86f\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f6d8a8a947747c6af374051b5140e013\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f6d8a8a947747c6af374051b5140e013\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9d75e665e83f82cca7c094b1da6862b9/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9d75e665e83f82cca7c094b1da6862b9\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9d75e665e83f82cca7c094b1da6862b9\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f6d8a8a947747c6af374051b5140e013\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f6d8a8a947747c6af374051b5140e013\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/bdf5ea98b8ef82e55b7165b42279078c/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\bdf5ea98b8ef82e55b7165b42279078c\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\bdf5ea98b8ef82e55b7165b42279078c\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c3b00cb14507cd5ef58b02bd72d3c86f\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c3b00cb14507cd5ef58b02bd72d3c86f\components\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnscreens.so

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ea49dc9aacd966da88f6745ee0bf23b3/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9d75e665e83f82cca7c094b1da6862b9/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/bdf5ea98b8ef82e55b7165b42279078c/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e819f92720ac3814b602db3a20c332a5/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\e819f92720ac3814b602db3a20c332a5\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\e819f92720ac3814b602db3a20c332a5\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1332f687fcd373fa10d1d54ed92a6c7a\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1332f687fcd373fa10d1d54ed92a6c7a\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2a355fff8e3d0c5ecd97bae98b9b323b/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2a355fff8e3d0c5ecd97bae98b9b323b\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\2a355fff8e3d0c5ecd97bae98b9b323b\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1332f687fcd373fa10d1d54ed92a6c7a\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1332f687fcd373fa10d1d54ed92a6c7a\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\d8e3648d0d1b9e8d231233f37e48042e\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\d8e3648d0d1b9e8d231233f37e48042e\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\40587413c60aab511d08e5a093fd1bb2\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\40587413c60aab511d08e5a093fd1bb2\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88e5b67d0f50da1584fc050fbef06bb5\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88e5b67d0f50da1584fc050fbef06bb5\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\40587413c60aab511d08e5a093fd1bb2\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\40587413c60aab511d08e5a093fd1bb2\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88e5b67d0f50da1584fc050fbef06bb5\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88e5b67d0f50da1584fc050fbef06bb5\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\d8e3648d0d1b9e8d231233f37e48042e\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\d8e3648d0d1b9e8d231233f37e48042e\react\renderer\components\rnsvg


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnsvg.so

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e819f92720ac3814b602db3a20c332a5/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2a355fff8e3d0c5ecd97bae98b9b323b/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnsvg.so
  TARGET_PDB = react_codegen_rnsvg.so.dbg


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d6e46b19b602d3371982eebc7ed690ed/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/a89efe01639eb7dad7f1852c2dc2010d/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libappmodules.so

build libreact_codegen_rnscreens.so: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_ReactNativeBlobUtilSpec: phony ReactNativeBlobUtilSpec_autolinked_build/react_codegen_ReactNativeBlobUtilSpec

build react_codegen_pagerview: phony pagerview_autolinked_build/react_codegen_pagerview

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rnpdf: phony rnpdf_autolinked_build/react_codegen_rnpdf

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a

build all: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libappmodules.so rnasyncstorage_autolinked_build/all ReactNativeBlobUtilSpec_autolinked_build/all pagerview_autolinked_build/all rnpdf_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all RNCWebViewSpec_autolinked_build/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/ReactNativeBlobUtilSpec_autolinked_build

build ReactNativeBlobUtilSpec_autolinked_build/all: phony ReactNativeBlobUtilSpec_autolinked_build/react_codegen_ReactNativeBlobUtilSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/pagerview_autolinked_build

build pagerview_autolinked_build/all: phony pagerview_autolinked_build/react_codegen_pagerview

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/rnpdf_autolinked_build

build rnpdf_autolinked_build/all: phony rnpdf_autolinked_build/react_codegen_rnpdf

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/intermediates/cxx/Debug/4u256x23/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
