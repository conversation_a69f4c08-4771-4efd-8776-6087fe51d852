import React, {useState, useCallback} from "react";
import {Text, View, ScrollView, Alert, ActivityIndicator} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import PaymentInputField from "@/components/payment-input-field";
import UserEditIcon from "@/components/icons/user-edit-icon";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import CreditCardShieldIcon from "@/components/icons/credit-card-shield-icon";
import {useCreatePayment} from "@/hooks/api/use-payments";
import {
  PaymentEntity,
  PaymentType,
  CreatePaymentRequest
} from "@/models/api/payments.models";
import {useCurrentUser} from "@/hooks/api/use-auth";

interface CreditCardForm {
  holderName: string;
  number: string;
  expiryMonth: string;
  expiryYear: string;
  ccv: string;
  cpfCnpj: string;
  postalCode: string;
  addressNumber: string;
  addressComplement: string;
  phoneNumber: string;
}

const CreditCardPayment: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isProcessing, setIsProcessing] = useState(false);
  const [formData, setFormData] = useState<CreditCardForm>({
    holderName: "",
    number: "",
    expiryMonth: "",
    expiryYear: "",
    ccv: "",
    cpfCnpj: "",
    postalCode: "",
    addressNumber: "",
    addressComplement: "",
    phoneNumber: ""
  });

  const {data: currentUser} = useCurrentUser();
  const createPaymentMutation = useCreatePayment();

  const formatCurrency = useCallback((value: string) => {
    const numValue = parseFloat(value) / 100;
    return `R$ ${numValue.toFixed(2).replace(".", ",")}`;
  }, []);

  const handleInputChange = useCallback(
    (field: keyof CreditCardForm) => (value: string) => {
      let processedValue = value;

      // Aplicar máscaras
      if (field === "number") {
        processedValue = value
          .replace(/\D/g, "")
          .slice(0, 16)
          .replace(/(\d{4})(?=\d)/g, "$1 ");
      } else if (field === "expiryMonth" || field === "expiryYear") {
        processedValue = value
          .replace(/\D/g, "")
          .slice(0, field === "expiryMonth" ? 2 : 4);
      } else if (field === "ccv") {
        processedValue = value.replace(/\D/g, "").slice(0, 4);
      } else if (field === "cpfCnpj") {
        processedValue = value.replace(/\D/g, "").slice(0, 14);
      } else if (field === "postalCode") {
        processedValue = value.replace(/\D/g, "").slice(0, 8);
      } else if (field === "phoneNumber") {
        processedValue = value.replace(/\D/g, "").slice(0, 11);
      }

      setFormData((prev) => ({...prev, [field]: processedValue}));
    },
    []
  );

  const validateForm = useCallback(() => {
    const errors: string[] = [];

    // Nome do titular: precisa conter pelo menos 2 palavras
    const holderWords = formData.holderName.trim().split(/\s+/).filter(Boolean);
    if (holderWords.length < 2)
      errors.push("Nome do titular (informe nome e sobrenome)");
    if (
      !formData.number.replace(/\s/g, "") ||
      formData.number.replace(/\s/g, "").length < 13
    )
      errors.push("Número do cartão");
    if (
      !formData.expiryMonth ||
      parseInt(formData.expiryMonth) < 1 ||
      parseInt(formData.expiryMonth) > 12
    )
      errors.push("Mês de validade");
    if (!formData.expiryYear || formData.expiryYear.length !== 4)
      errors.push("Ano de validade");
    if (!formData.ccv || formData.ccv.length < 3) errors.push("CVV");
    if (!formData.cpfCnpj || formData.cpfCnpj.length < 11)
      errors.push("CPF/CNPJ");
    if (!formData.postalCode || formData.postalCode.length !== 8)
      errors.push("CEP");
    if (!formData.addressNumber.trim()) errors.push("Número do endereço");
    if (!formData.phoneNumber || formData.phoneNumber.length < 10)
      errors.push("Telefone");

    if (errors.length > 0) {
      Alert.alert(
        t("creditCardPayment.error.title", "Campos obrigatórios"),
        t(
          "creditCardPayment.error.missingFields",
          "Preencha os seguintes campos: {{fields}}",
          {
            fields: errors.join(", ")
          }
        )
      );
      return false;
    }

    return true;
  }, [formData, t]);

  const handlePayment = useCallback(async () => {
    if (!validateForm()) return;
    if (!currentUser) {
      Alert.alert(
        t("creditCardPayment.error.title", "Erro"),
        t("creditCardPayment.error.userNotFound", "Usuário não encontrado")
      );
      return;
    }

    setIsProcessing(true);

    try {
      const paymentRequest: CreatePaymentRequest = {
        entity: PaymentEntity.Title,
        entityId: currentUser.id, // usar o ID do usuário atual
        type: PaymentType.CreditCard,
        creditCard: {
          installmentCount:
            parseInt(params.selectedInstallments as string) || 1,
          creditCard: {
            holderName: formData.holderName,
            number: formData.number.replace(/\s/g, ""),
            expiryMonth: formData.expiryMonth,
            expiryYear: formData.expiryYear,
            ccv: formData.ccv,
            name: formData.holderName,
            cpfCnpj: formData.cpfCnpj,
            postalCode: formData.postalCode,
            addressNumber: formData.addressNumber,
            addressComplement: formData.addressComplement,
            phoneNumber: formData.phoneNumber
          }
        }
      };

      const response = await createPaymentMutation.mutateAsync(paymentRequest);

      // Navegar para próxima etapa
      router.push({
        pathname: "/(registration)/plan-payment-selection",
        params: {
          ...params,
          titlePaymentId: response.id
        }
      });
    } catch (error) {
      console.error(
        "❌ [CREDIT-CARD-PAYMENT] Erro ao processar pagamento:",
        error
      );
      Alert.alert(
        t("creditCardPayment.error.title", "Erro"),
        t(
          "creditCardPayment.error.processPayment",
          "Erro ao processar pagamento. Verifique os dados e tente novamente."
        )
      );
    } finally {
      setIsProcessing(false);
    }
  }, [
    validateForm,
    currentUser,
    formData,
    params,
    createPaymentMutation,
    router,
    t
  ]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  if (isProcessing) {
    return (
      <Screen>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#1A1A1A"
          }}
        >
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text
            style={{
              marginTop: 16,
              fontSize: 16,
              color: "#FFFFFF",
              textAlign: "center"
            }}
          >
            {t("creditCardPayment.processing", "Processando pagamento...")}
          </Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen disableScrollView>
      <ScrollView
        style={{flex: 1, backgroundColor: "#1A1A1A"}}
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 60,
          paddingBottom: 20
        }}
      >
        <BackButton />

        <View style={{marginBottom: 32}}>
          <Text
            style={{
              fontSize: 24,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8
            }}
          >
            {t("creditCardPayment.createAccount", "Criar conta")}
          </Text>
          <Text style={{fontSize: 14, color: "#CCCCCC", marginBottom: 16}}>
            {t("creditCardPayment.stepProgress", "4 / 7 Pagamento do título")}
          </Text>
          <View
            style={{height: 4, backgroundColor: "#333333", borderRadius: 2}}
          >
            <View
              style={{
                height: "100%",
                backgroundColor: "#00D4AA",
                borderRadius: 2,
                width: "56%"
              }}
            />
          </View>
        </View>

        <View style={{marginBottom: 24}}>
          <Text
            style={{
              fontSize: 20,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 8
            }}
          >
            {t("creditCardPayment.title", "Pagamento Cartão de Crédito")}
          </Text>
          <Text style={{fontSize: 16, color: "#CCCCCC", lineHeight: 24}}>
            {t(
              "creditCardPayment.description",
              "Preencha os dados do cartão para efetuar o pagamento."
            )}
          </Text>
        </View>

        <View
          style={{
            backgroundColor: "#2A2A2A",
            borderRadius: 12,
            padding: 16,
            marginBottom: 24
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: "bold",
              color: "#FFFFFF",
              marginBottom: 12
            }}
          >
            {t("creditCardPayment.summary", "Resumo do pagamento")}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text style={{fontSize: 14, color: "#CCCCCC"}}>
              {t("creditCardPayment.item", "Item:")}
            </Text>
            <Text style={{fontSize: 14, fontWeight: "bold", color: "#FFFFFF"}}>
              {t("creditCardPayment.titleAdhesion", "Título de Adesão")}
            </Text>
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 8
            }}
          >
            <Text style={{fontSize: 14, color: "#CCCCCC"}}>
              {t("creditCardPayment.installments", "Parcelamento:")}
            </Text>
            <Text style={{fontSize: 14, fontWeight: "bold", color: "#FFFFFF"}}>
              {params.selectedInstallments === "1"
                ? t("creditCardPayment.singlePayment", "À vista")
                : t(
                    "creditCardPayment.installmentsCount",
                    "{{count}}x de {{value}}",
                    {
                      count: params.selectedInstallments,
                      value: formatCurrency(params.installmentValue as string)
                    }
                  )}
            </Text>
          </View>
          <View style={{flexDirection: "row", justifyContent: "space-between"}}>
            <Text style={{fontSize: 14, color: "#CCCCCC"}}>
              {t("creditCardPayment.total", "Total:")}
            </Text>
            <Text style={{fontSize: 16, fontWeight: "bold", color: "#00D4AA"}}>
              {formatCurrency(params.totalValue as string)}
            </Text>
          </View>
        </View>

        {/* Formulário do cartão */}
        <View style={{marginBottom: 24}}>
          <PaymentInputField
            placeholder={t("creditCardPayment.holderName", "Nome do titular")}
            value={formData.holderName}
            onChangeText={handleInputChange("holderName")}
            icon={<UserEditIcon />}
            style={{marginBottom: 16}}
          />

          <PaymentInputField
            placeholder={t("creditCardPayment.cardNumber", "Número do cartão")}
            value={formData.number}
            onChangeText={handleInputChange("number")}
            icon={<CreditCardIcon />}
            inputMode="numeric"
            maxLength={19}
            style={{marginBottom: 16}}
          />

          <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
            <View style={{flex: 1}}>
              <PaymentInputField
                placeholder={t("creditCardPayment.expiryMonth", "Mês")}
                value={formData.expiryMonth}
                onChangeText={handleInputChange("expiryMonth")}
                icon={<CreditCardIcon />}
                inputMode="numeric"
                maxLength={2}
              />
            </View>
            <View style={{flex: 1}}>
              <PaymentInputField
                placeholder={t("creditCardPayment.expiryYear", "Ano")}
                value={formData.expiryYear}
                onChangeText={handleInputChange("expiryYear")}
                icon={<CreditCardIcon />}
                inputMode="numeric"
                maxLength={4}
              />
            </View>
            <View style={{flex: 1}}>
              <PaymentInputField
                placeholder={t("creditCardPayment.ccv", "CVV")}
                value={formData.ccv}
                onChangeText={handleInputChange("ccv")}
                icon={<CreditCardShieldIcon />}
                inputMode="numeric"
                maxLength={4}
              />
            </View>
          </View>

          <PaymentInputField
            placeholder={t("creditCardPayment.cpfCnpj", "CPF/CNPJ")}
            value={formData.cpfCnpj}
            onChangeText={handleInputChange("cpfCnpj")}
            inputMode="numeric"
            style={{marginBottom: 16}}
          />

          <View style={{flexDirection: "row", gap: 12, marginBottom: 16}}>
            <View style={{flex: 2}}>
              <PaymentInputField
                placeholder={t("creditCardPayment.postalCode", "CEP")}
                value={formData.postalCode}
                onChangeText={handleInputChange("postalCode")}
                inputMode="numeric"
                maxLength={8}
              />
            </View>
            <View style={{flex: 1}}>
              <PaymentInputField
                placeholder={t("creditCardPayment.addressNumber", "Número")}
                value={formData.addressNumber}
                onChangeText={handleInputChange("addressNumber")}
              />
            </View>
          </View>

          <PaymentInputField
            placeholder={t(
              "creditCardPayment.addressComplement",
              "Complemento (opcional)"
            )}
            value={formData.addressComplement}
            onChangeText={handleInputChange("addressComplement")}
            style={{marginBottom: 16}}
          />

          <PaymentInputField
            placeholder={t("creditCardPayment.phoneNumber", "Telefone")}
            value={formData.phoneNumber}
            onChangeText={handleInputChange("phoneNumber")}
            inputMode="numeric"
          />
        </View>

        <View style={{flexDirection: "row", gap: 12, paddingTop: 20}}>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={{flex: 1}}>
            <FullSizeButton
              text={t("creditCardPayment.pay", "Pagar")}
              onPress={handlePayment}
              loading={isProcessing}
              disabled={isProcessing}
            />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
};

export default CreditCardPayment;
