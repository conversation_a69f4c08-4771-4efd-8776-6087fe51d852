/**
 * Hooks React Query para gerenciamento de QR codes
 */

import {useQuery, UseQueryOptions, useQueryClient} from "@tanstack/react-query";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";
import {QRCodeService} from "@/services/api/qr-codes/qr-codes.service";

/**
 * Query keys para QR codes
 */
export const qrCodesKeys = {
  all: ["qr-codes"] as const,
  tickets: () => [...qrCodesKeys.all, "tickets"] as const,
  ticket: (ticketId: string) => [...qrCodesKeys.tickets(), ticketId] as const,
  payments: () => [...qrCodesKeys.all, "payments"] as const,
  payment: (paymentId: string) =>
    [...qrCodesKeys.payments(), paymentId] as const
};

/**
 * Hook para buscar QR code de um ticket específico
 */
export const useTicketQRCode = (
  ticketId: string,
  eventId?: number,
  userId?: number,
  options?: UseQueryOptions<string | null, BaseApiError>
) => {
  return useQuery({
    queryKey: qrCodesKeys.ticket(ticketId),
    queryFn: () => QRCodeService.getTicketQRCode(ticketId, eventId, userId),
    enabled: !!ticketId,
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    // Configurações para evitar refetch excessivo
    refetchOnMount: false, // Não refetch ao montar componente
    refetchOnWindowFocus: false, // Não refetch ao focar janela
    refetchOnReconnect: true, // Apenas refetch ao reconectar
    retry: 2,
    ...options
  });
};

/**
 * Hook para buscar QR code de um pagamento específico
 */
export const usePaymentQRCode = (
  paymentId: string,
  options?: UseQueryOptions<string | null, BaseApiError>
) => {
  return useQuery({
    queryKey: qrCodesKeys.payment(paymentId),
    queryFn: () => QRCodeService.getPaymentQRCode(paymentId),
    enabled: !!paymentId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
    // Configurações para evitar refetch excessivo
    refetchOnMount: false, // Não refetch ao montar componente
    refetchOnWindowFocus: false, // Não refetch ao focar janela
    refetchOnReconnect: true, // Apenas refetch ao reconectar
    retry: 2,
    ...options
  });
};

/**
 * Hook para invalidar cache de QR codes
 */
export const useInvalidateQRCodes = () => {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    queryClient.invalidateQueries({queryKey: qrCodesKeys.all});
    ApiLogger.info("Cache de QR codes invalidado completamente");
  };

  const invalidateTicketQRCode = (ticketId: string) => {
    queryClient.invalidateQueries({queryKey: qrCodesKeys.ticket(ticketId)});
    ApiLogger.info("Cache de QR code do ticket invalidado", {ticketId});
  };

  const invalidatePaymentQRCode = (paymentId: string) => {
    queryClient.invalidateQueries({queryKey: qrCodesKeys.payment(paymentId)});
    ApiLogger.info("Cache de QR code do pagamento invalidado", {paymentId});
  };

  return {
    invalidateAll,
    invalidateTicketQRCode,
    invalidatePaymentQRCode
  };
};

/**
 * Hook para prefetch de QR codes
 */
export const usePrefetchQRCodes = () => {
  const queryClient = useQueryClient();

  const prefetchTicketQRCode = (
    ticketId: string,
    eventId?: number,
    userId?: number
  ) => {
    queryClient.prefetchQuery({
      queryKey: qrCodesKeys.ticket(ticketId),
      queryFn: () => QRCodeService.getTicketQRCode(ticketId, eventId, userId),
      staleTime: 10 * 60 * 1000
    });
  };

  const prefetchPaymentQRCode = (paymentId: string) => {
    queryClient.prefetchQuery({
      queryKey: qrCodesKeys.payment(paymentId),
      queryFn: () => QRCodeService.getPaymentQRCode(paymentId),
      staleTime: 5 * 60 * 1000
    });
  };

  return {
    prefetchTicketQRCode,
    prefetchPaymentQRCode
  };
};
