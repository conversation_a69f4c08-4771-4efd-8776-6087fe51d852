/**
 * Serviço de Especializações para ClubM
 * Implementa operações para gerenciamento de especializações
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {PaginationResponse} from "@/models/api/common.models";

// Interfaces para especializações
export interface Specialization {
  id: number;
  name: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateSpecializationRequest {
  name: string;
  description?: string;
}

export interface SpecializationsListParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export class SpecializationsService {
  private static readonly BASE_PATH = "/api/app/specializations";

  /**
   * Buscar lista de especializações disponíveis
   */
  static async getSpecializations(
    params?: SpecializationsListParams
  ): Promise<PaginationResponse<Specialization>> {
    try {
      ApiLogger.info("Buscando lista de especializações", params);

      const response = await firstValueFrom(
        apiClient.get<PaginationResponse<Specialization>>(this.BASE_PATH, {
          params: {
            Search: params?.search,
            Page: params?.page || 1,
            PageSize: params?.pageSize || 50
          }
        })
      );

      ApiLogger.info(`Encontradas ${response.data.length} especializações`);
      console.log(`✅ API Status: GET ${this.BASE_PATH} - 200 OK`);
      console.log(`📊 Specializations Data: ${response.data.length} especializações encontradas`);

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar especializações", error as Error);
      console.log(`❌ API Status: GET ${this.BASE_PATH} - ${(error as any)?.status || "Unknown"} Error`);
      throw error;
    }
  }

  /**
   * Criar nova especialização
   */
  static async createSpecialization(
    data: CreateSpecializationRequest
  ): Promise<Specialization> {
    try {
      ApiLogger.info("Criando nova especialização", data);

      const response = await firstValueFrom(
        apiClient.post<Specialization>(this.BASE_PATH, data)
      );

      ApiLogger.info("Especialização criada com sucesso", {
        id: response.id,
        name: response.name
      });
      console.log(`✅ API Status: POST ${this.BASE_PATH} - 200 OK`);
      console.log(`📊 Specialization Created: ${response.name} (ID: ${response.id})`);

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao criar especialização", error as Error);
      console.log(`❌ API Status: POST ${this.BASE_PATH} - ${(error as any)?.status || "Unknown"} Error`);
      throw error;
    }
  }
}

export default SpecializationsService;
