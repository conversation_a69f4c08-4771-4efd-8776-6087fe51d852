import React, {useState, useMemo, useCallback} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator
} from "react-native";
import ScreenWithHeader from "../../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import SearchIcon from "../../../components/icons/search-icon";
import DiscountIcon from "../../../components/icons/discount-icon";
import GiftIcon from "../../../components/icons/gift-icon";
import VipAccessIcon from "../../../components/icons/vip-access-icon";
import GastronomyIcon from "../../../components/icons/gastronomy-icon";
import HealthIcon from "../../../components/icons/health-icon";
import BusinessIcon from "../../../components/icons/business-icon";
import {usePartners} from "@/hooks/api/use-partners";
import {imageCache} from "@/services/cache/image-cache.service";
import {apiClient} from "@/services/api/base/api-client";
import {firstValueFrom} from "rxjs";
import styles from "@/styles/partners/index.style";

interface Partner {
  id: number;
  name: string;
  logoId?: string;
  logoUrl?: string;
  description?: string;
  category?: string; // Para compatibilidade com filtros locais
}

const PartnersScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debounce search term
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch partners from API
  const {
    data: partnersResponse,
    isLoading,
    error
  } = usePartners({
    page: 1,
    pageSize: 50,
    search: debouncedSearchTerm || undefined
  });

  // Helper function to assign categories (for demo purposes)
  const getCategoryForPartner = useCallback((name: string): string => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes("club") || lowerName.includes("desconto"))
      return "descontos";
    if (lowerName.includes("sheep") || lowerName.includes("brinde"))
      return "brindes";
    if (lowerName.includes("vip") || lowerName.includes("acesso"))
      return "acessos-vip";
    if (
      lowerName.includes("gastro") ||
      lowerName.includes("food") ||
      lowerName.includes("restaurante")
    )
      return "gastronomia";
    if (
      lowerName.includes("saude") ||
      lowerName.includes("health") ||
      lowerName.includes("medic")
    )
      return "saude";
    return "descontos"; // default category
  }, []);

  const categories = [
    {
      id: "descontos",
      name: "Descontos",
      icon: <DiscountIcon width={25} height={25} />
    },
    {
      id: "brindes",
      name: "Brindes",
      icon: <GiftIcon width={25} height={25} />
    },
    {
      id: "acessos-vip",
      name: "Acessos VIP",
      icon: <VipAccessIcon width={25} height={25} />
    },
    {
      id: "gastronomia",
      name: "Gastronomia",
      icon: <GastronomyIcon width={25} height={25} />
    },
    {
      id: "saude",
      name: "Saúde",
      icon: <HealthIcon width={25} height={25} />
    }
  ];

  // Get partners from API response
  const apiPartners: Partner[] = useMemo(() => {
    if (!partnersResponse?.data) return [];

    return partnersResponse.data.map((partner: any) => ({
      id: partner.id,
      name: partner.name,
      logoId: partner.logoId,
      logoUrl: partner.logoUrl,
      description: partner.description,
      // Assign categories based on partner name for demo purposes
      // In a real app, this would come from the API
      category: getCategoryForPartner(partner.name)
    }));
  }, [partnersResponse, getCategoryForPartner]);

  const filteredPartners = useMemo(() => {
    return apiPartners.filter((partner) => {
      const matchesCategory =
        selectedCategory === null || partner.category === selectedCategory;
      return matchesCategory;
    });
  }, [apiPartners, selectedCategory]);

  const handlePartnerPress = (partnerId: number) => {
    router.push(`/(main)/partners/${partnerId}`);
  };

  // Component to render partner card with image loading
  const PartnerCardComponent: React.FC<{partner: Partner}> = ({partner}) => {
    const [imageUri, setImageUri] = useState<string | undefined>(undefined);
    const [hasImageError, setHasImageError] = useState(false);

    // Function to fetch image with authentication
    const fetchImageWithAuth = useCallback(
      async (imageUrl: string) => {
        try {
          setHasImageError(false);

          // Check cache first if we have logoId
          if (partner.logoId) {
            const cachedImage = imageCache.get(partner.logoId);
            if (cachedImage) {
              setImageUri(cachedImage);
              return;
            }
          }

          // Fetch image with auth
          const response = await firstValueFrom(
            apiClient.get(imageUrl, {
              responseType: "arraybuffer"
            })
          );

          // Convert to base64
          const uint8Array = new Uint8Array(response as ArrayBuffer);
          const base64 = btoa(
            uint8Array.reduce(
              (data, byte) => data + String.fromCharCode(byte),
              ""
            )
          );

          // Create data URI
          const dataUri = `data:image/webp;base64,${base64}`;
          setImageUri(dataUri);

          // Save to cache if we have logoId
          if (partner.logoId) {
            imageCache.set(partner.logoId, dataUri);
          }
        } catch (error) {
          setHasImageError(true);
          // Fallback to direct URL
          setImageUri(imageUrl);
        }
      },
      [partner.logoId]
    );

    // Effect to load image when partner changes
    React.useEffect(() => {
      const imageUrl = partner.logoId
        ? `/api/storage/${partner.logoId}`
        : partner.logoUrl;

      if (imageUrl) {
        fetchImageWithAuth(imageUrl);
      } else {
        setImageUri(undefined);
      }
    }, [partner.logoId, partner.logoUrl, fetchImageWithAuth]);

    const shouldShowPlaceholder = !imageUri || hasImageError;

    return (
      <TouchableOpacity
        key={partner.id}
        style={styles.partnerCard}
        onPress={() => handlePartnerPress(partner.id)}
      >
        <View style={styles.partnerLogoContainer}>
          {shouldShowPlaceholder ? (
            <View
              style={{
                width: "100%",
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "#F8F9FA"
              }}
            >
              <BusinessIcon width={32} height={32} color="#4A90E2" />
            </View>
          ) : (
            <Image
              source={{uri: imageUri}}
              style={styles.partnerLogo}
              resizeMode="contain"
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderPartnerCard = (partner: Partner) => (
    <PartnerCardComponent key={partner.id} partner={partner} />
  );

  return (
    <ScreenWithHeader
      screenTitle={t("partners.title", "Parceiros Club M")}
      backButton
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder={t(
                "partners.searchPlaceholder",
                "Buscar parceiro, benefício, etc..."
              )}
              placeholderTextColor="#F2F4F7"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <Text style={styles.categoriesTitle}>
            {t("partners.categories", "Categorias")}
          </Text>
        </View>

        {/* Category Icons */}
        <View style={styles.categoryIconsContainer}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={styles.categoryIconItem}
              onPress={() =>
                setSelectedCategory(
                  selectedCategory === category.id ? null : category.id
                )
              }
            >
              <View style={styles.categoryIconWrapper}>{category.icon}</View>
              <Text style={styles.categoryIconText}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Partners Grid */}
        <View style={styles.partnersGrid}>
          {isLoading ? (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                paddingVertical: 40
              }}
            >
              <ActivityIndicator size="large" color="#4A90E2" />
              <Text
                style={{
                  color: "#DFE9F0",
                  fontFamily: "OpenSans",
                  fontSize: 14,
                  marginTop: 16
                }}
              >
                Carregando parceiros...
              </Text>
            </View>
          ) : error ? (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                paddingVertical: 40
              }}
            >
              <BusinessIcon width={48} height={48} color="#FF6B6B" />
              <Text
                style={{
                  color: "#DFE9F0",
                  fontFamily: "OpenSans",
                  fontSize: 14,
                  marginTop: 16,
                  textAlign: "center"
                }}
              >
                Erro ao carregar parceiros.{"\n"}Tente novamente mais tarde.
              </Text>
            </View>
          ) : filteredPartners.length === 0 ? (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                paddingVertical: 40
              }}
            >
              <BusinessIcon width={48} height={48} color="#9CA3AF" />
              <Text
                style={{
                  color: "#DFE9F0",
                  fontFamily: "OpenSans",
                  fontSize: 14,
                  marginTop: 16,
                  textAlign: "center"
                }}
              >
                {selectedCategory
                  ? "Nenhum parceiro encontrado nesta categoria."
                  : "Nenhum parceiro encontrado."}
              </Text>
            </View>
          ) : (
            Array.from(
              {length: Math.ceil(filteredPartners.length / 2)},
              (_, rowIndex) => (
                <View key={rowIndex} style={styles.partnersRow}>
                  {filteredPartners
                    .slice(rowIndex * 2, rowIndex * 2 + 2)
                    .map(renderPartnerCard)}
                </View>
              )
            )
          )}
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PartnersScreen;
