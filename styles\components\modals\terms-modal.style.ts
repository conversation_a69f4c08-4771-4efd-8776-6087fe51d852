import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  backdropTouchable: {
    flex: 1
  },
  modalContainer: {
    backgroundColor: stylesConstants.colors.primaryBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 8,
    minHeight: "60%",
    maxHeight: "80%"
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2,
    alignSelf: "center",
    marginBottom: 16
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    paddingTop: 10
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    color: stylesConstants.colors.fullWhite,
    textAlign: "center",
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  scrollView: {
    height: 500,
  },
  scrollContent: {
    paddingTop: 24,
    paddingLeft: 16,
    paddingRight: 16,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12
  },
  termsText: {
    fontSize: 14,
    lineHeight: 22,
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  closeButtonBottom: {
    backgroundColor: stylesConstants.colors.primary,
    borderWidth: 1,
    borderColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans
  },
  alertContainer: {
    marginTop: 10,
    marginBottom: 10
  },
  alertText: {
    fontSize: 17,
    fontWeight: "100",
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  }
});

export default styles;
