import {firstValueFrom} from "rxjs";
import {apiClient} from "@/services/api/base/api-client";
import {ApiLogger} from "@/services/api/base/api-logger";
import axios from "axios";

export interface State {
  id: string;
  name: string;
  uf: string;
}

export interface City {
  id: string;
  name: string;
  stateId: string;
  state?: State;
}

export interface StatesResponse {
  data: State[];
  totalItems: number;
}

export interface CitiesResponse {
  data: City[];
  totalItems: number;
}

export interface BrasilApiCepResponse {
  cep: string;
  state: string;
  city: string;
  neighborhood: string;
  street: string;
  service: string;
}

export interface AddressData {
  street: string;
  neighborhood: string;
  city: string;
  state: string;
}

export class AddressesService {
  private static readonly BASE_PATH = "/api/app/addresses";

  static async getStates(): Promise<State[]> {
    try {
      ApiLogger.info("Buscando lista de estados");

      const response = await firstValueFrom(
        apiClient.get<State[]>(`${this.BASE_PATH}/states`)
      );

      ApiLogger.info(`Encontrados ${response.length} estados`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar estados", error as Error);
      throw error;
    }
  }

  static async getCitiesByState(uf: string): Promise<City[]> {
    try {
      ApiLogger.info("Buscando cidades do estado", {uf});

      const response = await firstValueFrom(
        apiClient.get<City[]>(`${this.BASE_PATH}/states/${uf}/cities`)
      );

      ApiLogger.info(
        `Encontradas ${response.length} cidades para o estado ${uf}`
      );
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar cidades", error as Error, {uf});
      throw error;
    }
  }

  static async getAddressByCep(cep: string): Promise<AddressData> {
    try {
      const cleanCep = cep.replace(/\D/g, "");
      if (cleanCep.length !== 8) {
        throw new Error("CEP deve conter exatamente 8 dígitos");
      }

      ApiLogger.info("Buscando endereço por CEP", {cep: cleanCep});

      const response = await firstValueFrom(
        apiClient.get<any>(`https://viacep.com.br/ws/${cleanCep}/json/`)
      );

      ApiLogger.info("Resposta recebida do ViaCEP", {
        cep: cleanCep,
        response: response
      });

      if (response.erro) {
        ApiLogger.warn("CEP não encontrado", {cep: cleanCep});
        throw new Error("CEP não encontrado");
      }

      const addressData: AddressData = {
        street: response.logradouro || "",
        neighborhood: response.bairro || "",
        city: response.localidade || "",
        state: response.uf || ""
      };

      ApiLogger.info("Endereço encontrado com sucesso", {
        cep: cleanCep,
        street: addressData.street,
        neighborhood: addressData.neighborhood,
        city: addressData.city,
        state: addressData.state
      });

      return addressData;
    } catch (error) {
      if (error instanceof Error) {
        ApiLogger.error("Erro detalhado na busca do CEP", error, {
          cep,
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack
        });

        if (error.name === "AbortError") {
          throw new Error("Timeout na busca do CEP. Tente novamente.");
        } else if (error.message.includes("CEP")) {
          throw error;
        } else if (
          error.message.includes("Network request failed") ||
          error.message.includes("fetch")
        ) {
          throw new Error(
            "Erro de conexão. Verifique sua internet e tente novamente."
          );
        } else {
          throw new Error("Erro ao buscar CEP. Tente novamente.");
        }
      } else {
        ApiLogger.error("Erro inesperado ao buscar CEP", error as Error, {cep});
        throw new Error("Erro ao buscar CEP. Tente novamente.");
      }
    }
  }
}
