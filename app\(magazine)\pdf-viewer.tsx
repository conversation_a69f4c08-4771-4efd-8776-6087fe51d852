import React, {useState, useRef, useEffect} from "react";
import {
  View,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
  Text
} from "react-native";
import {useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Pdf from "react-native-pdf";
import * as FileSystem from "expo-file-system";
import ScreenWithHeader from "@/components/screen-with-header";
import {apiClient} from "@/services/api/base/api-client";
import styles from "@/styles/magazine/pdf-viewer.style";
import stylesConstants from "@/styles/styles-constants";
import ShareIcon from "@/components/icons/share-icon";
import DownloadIcon from "@/components/icons/download-icon";

interface PDFViewerParams {
  fileId: string;
  title: string;
  magazineId?: string;
  editionId?: string;
}

const PDFViewer: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams<PDFViewerParams>();
  const {fileId, title, magazineId, editionId} = params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfSource, setPdfSource] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const pdfRef = useRef<Pdf>(null);

  useEffect(() => {
    if (fileId) {
      loadPDF();
    }
  }, [fileId]);

  const loadPDF = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log("📖 [PDF-VIEWER] Loading PDF:", {
        fileId,
        title,
        magazineId,
        editionId
      });

      // Get authentication token
      const tokenData = await apiClient.getValidToken();
      if (!tokenData?.accessToken) {
        throw new Error("Token de autenticação não disponível");
      }

      // Build file URL
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl) {
        throw new Error("EXPO_PUBLIC_API_BASE_URL não está configurado");
      }
      const fileUrl = `${baseUrl}/api/storage/${fileId}`;

      // Create temporary file name
      const fileName = `pdf-${fileId}-${Date.now()}.pdf`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Download PDF with authentication headers
      const downloadResult = await FileSystem.downloadAsync(fileUrl, fileUri, {
        headers: {
          Authorization: `${tokenData.tokenType} ${tokenData.accessToken}`
        }
      });

      if (downloadResult.status !== 200) {
        throw new Error(`Download falhou com status: ${downloadResult.status}`);
      }

      console.log("📖 [PDF-VIEWER] PDF downloaded successfully:", {
        uri: downloadResult.uri,
        status: downloadResult.status
      });

      setPdfSource(downloadResult.uri);
      setIsLoading(false);
    } catch (error) {
      console.error("📖 [PDF-VIEWER] Error loading PDF:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Erro desconhecido ao carregar PDF"
      );
      setIsLoading(false);
    }
  };

  const handleLoadComplete = (numberOfPages: number, filePath: string) => {
    console.log("📖 [PDF-VIEWER] PDF loaded:", {numberOfPages, filePath});
    setTotalPages(numberOfPages);
    setIsLoading(false);
  };

  const handlePageChanged = (page: number, numberOfPages: number) => {
    console.log("📖 [PDF-VIEWER] Page changed:", {page, numberOfPages});
    setCurrentPage(page);
  };

  const handleError = (error: any) => {
    console.error("📖 [PDF-VIEWER] PDF error:", error);
    setError("Erro ao carregar o PDF. Verifique sua conexão.");
    setIsLoading(false);
  };

  const handleShare = async () => {
    if (!pdfSource) return;

    try {
      const {Sharing} = await import("expo-sharing");
      const isAvailable = await Sharing.isAvailableAsync();

      if (!isAvailable) {
        Alert.alert(
          "Compartilhamento não disponível",
          "O compartilhamento não está disponível neste dispositivo"
        );
        return;
      }

      await Sharing.shareAsync(pdfSource, {
        mimeType: "application/pdf",
        dialogTitle: title || "PDF"
      });
    } catch (error) {
      console.error("📖 [PDF-VIEWER] Error sharing PDF:", error);
      Alert.alert("Erro", "Não foi possível compartilhar o PDF");
    }
  };

  const handleDownload = async () => {
    Alert.alert(
      "Download",
      "O PDF já está disponível no seu dispositivo para visualização.",
      [{text: "OK"}]
    );
  };

  const renderHeaderActions = () => (
    <View style={styles.headerActions}>
      <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
        <ShareIcon
          width={20}
          height={20}
          color={stylesConstants.colors.white}
        />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton} onPress={handleDownload}>
        <DownloadIcon
          width={20}
          height={20}
          color={stylesConstants.colors.white}
        />
      </TouchableOpacity>
    </View>
  );

  if (error) {
    return (
      <ScreenWithHeader
        screenTitle={title || "PDF"}
        backButton
        rightComponent={renderHeaderActions()}
      >
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{error}</Text>
          <TouchableOpacity
            style={{
              marginTop: 16,
              paddingHorizontal: 20,
              paddingVertical: 10,
              backgroundColor: stylesConstants.colors.primary500,
              borderRadius: 8
            }}
            onPress={loadPDF}
          >
            <Text style={{color: stylesConstants.colors.white}}>
              Tentar novamente
            </Text>
          </TouchableOpacity>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle={title || "PDF"}
      backButton
      rightComponent={renderHeaderActions()}
      disablePadding
    >
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={stylesConstants.colors.primary500}
          />
          <Text style={styles.loadingText}>
            {t("magazine.loadingPdf", "Carregando PDF...")}
          </Text>
        </View>
      )}

      {pdfSource && (
        <Pdf
          ref={pdfRef}
          source={{uri: pdfSource, cache: true}}
          onLoadComplete={handleLoadComplete}
          onPageChanged={handlePageChanged}
          onError={handleError}
          style={styles.webview}
          trustAllCerts={false}
          enablePaging={false}
          spacing={10}
          fitWidth={true}
          fitPolicy={2}
          enableDoubleTapZoom={true}
          enableAnnotationRendering={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={true}
          minScale={0.5}
          maxScale={3.0}
        />
      )}

      {/* Page indicator */}
      {totalPages > 0 && !isLoading && (
        <View style={styles.pageIndicator}>
          <Text style={styles.pageIndicatorText}>
            {currentPage} / {totalPages}
          </Text>
        </View>
      )}
    </ScreenWithHeader>
  );
};

export default PDFViewer;
