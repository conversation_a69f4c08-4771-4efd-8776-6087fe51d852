/**
 * Tela de pagamento para publicação de oportunidades
 * Cópia exata do design da tela de pagamento principal
 */

import React, {useCallback, useState, useMemo, useEffect} from "react";
import {
  View,
  ScrollView,
  Alert,
  Text,
  StyleSheet,
  TouchableOpacity
} from "react-native";
import {useTranslation} from "react-i18next";
import {useLocalSearchParams, useRouter} from "expo-router";

import PaymentMethodSelector from "../../components/payment/payment-method-selector";
import CreditCardForm from "../../components/payment/credit-card-form";
import LoadingOverlay from "../../components/loading-overlay";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";

import {
  PaymentType,
  CreditCard,
  PaymentEntity,
  CreatePaymentRequest,
  CreateCreditCardFormData
} from "../../models/api/payments.models";

import {
  useCreatePayment,
  useCreditCards,
  useCreateCreditCard
} from "../../hooks/api/use-payments";

import {InstallmentOption} from "../../services/api/opportunities/opportunities.service";
import {ApiTerm} from "../../models/api/terms.models";
import {useAcceptTerm} from "../../hooks/api/use-terms";
import {useOpportunityTerm} from "../../hooks/api/use-opportunity-terms";
import TermsModal from "../../components/terms/terms-modal";

interface PaymentData {
  opportunityId: number;
  publicationValue: number;
  installmentOptions: InstallmentOption[];
  opportunityTitle: string;
  term?: ApiTerm;
}

const OpportunityPublicationPayment: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {paymentData: paymentDataParam} = useLocalSearchParams();

  // Estados locais - copiados exatamente da tela original
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<PaymentType>();
  const [selectedSavedCard, setSelectedSavedCard] = useState<CreditCard>();
  const [creditCardData, setCreditCardData] =
    useState<CreateCreditCardFormData>();
  const [step, setStep] = useState<"method" | "details" | "summary">("method");
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const [selectedInstallments, setSelectedInstallments] = useState<number>(1);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  // Hooks de API - copiados exatamente da tela original
  const {
    data: creditCards = [],
    isLoading: isLoadingCreditCards,
    error: creditCardsError
  } = useCreditCards();
  const createPaymentMutation = useCreatePayment();
  const createCreditCardMutation = useCreateCreditCard();

  // Hook para buscar termos de oportunidades
  const {data: opportunityTerm, isLoading: isLoadingTerms} =
    useOpportunityTerm();
  const acceptTermMutation = useAcceptTerm();

  console.log(
    "🎯 [OPPORTUNITY PAYMENT] Tela de pagamento de oportunidade iniciada"
  );
  console.log("📋 [OPPORTUNITY PAYMENT] Parâmetros recebidos:", {
    paymentDataParam,
    allParams: paymentDataParam
  });

  // Parse payment data from URL params and add opportunity term
  useEffect(() => {
    if (paymentDataParam && typeof paymentDataParam === "string") {
      try {
        const parsed = JSON.parse(
          decodeURIComponent(paymentDataParam)
        ) as PaymentData;

        // Add opportunity term to payment data
        const paymentDataWithTerm: PaymentData = {
          ...parsed,
          term: opportunityTerm || undefined
        };

        setPaymentData(paymentDataWithTerm);
      } catch (error) {
        console.error("Error parsing payment data:", error);
        Alert.alert("Erro", "Dados de pagamento inválidos.");
        router.back();
      }
    }
  }, [paymentDataParam, router, opportunityTerm]);

  // Debug dos cartões carregados - copiado da tela original
  console.log("💳 [OPPORTUNITY PAYMENT] Estado dos cartões:", {
    isLoading: isLoadingCreditCards,
    hasCards: creditCards.length > 0,
    cardsCount: creditCards.length,
    error: creditCardsError?.message,
    firstCard: creditCards[0]
  });

  // Debug do estado de seleção de cartão - copiado da tela original
  React.useEffect(() => {
    console.log(
      "🔄 [OPPORTUNITY PAYMENT] 🔧 DEBUG ESTADO - Mudança no cartão selecionado:",
      {
        selectedSavedCardId: selectedSavedCard?.id,
        selectedSavedCardBrand: selectedSavedCard?.brand,
        selectedMethod,
        step,
        showNewCardForm
      }
    );
  }, [selectedSavedCard, selectedMethod, step, showNewCardForm]);

  // Dados do item para pagamento - adaptado para oportunidades
  const paymentItem: PaymentSummaryItem = useMemo(() => {
    if (!paymentData) {
      return {
        id: "0",
        title: "Publicação de Oportunidade",
        description: "Publicação da oportunidade",
        imageUrl: "",
        price: 0,
        quantity: 1,
        entity: PaymentEntity.Opportunity
      };
    }

    return {
      id: String(paymentData.opportunityId),
      title: paymentData.opportunityTitle,
      description: "Publicação da oportunidade",
      imageUrl: "",
      price: paymentData.publicationValue / 100, // Converte de centavos para reais
      quantity: 1,
      entity: PaymentEntity.Opportunity
    };
  }, [paymentData]);

  // Verifica se pode prosseguir para próximo passo
  const canProceedToDetails = useMemo(() => {
    return selectedMethod !== undefined;
  }, [selectedMethod]);

  const canProceedToSummary = useMemo(() => {
    // Se é cartão de crédito
    if (selectedMethod === PaymentType.CreditCard) {
      // Se tem cartão salvo selecionado, pode prosseguir para resumo
      if (selectedSavedCard) {
        return true;
      }
      // Se não tem cartão salvo, precisa ter dados do novo cartão
      if (!selectedSavedCard) {
        return creditCardData !== undefined;
      }
    }

    // Para outros métodos (PIX, Boleto)
    return canProceedToDetails;
  }, [selectedMethod, selectedSavedCard, creditCardData, canProceedToDetails]);

  // Handler para seleção de parcelas
  const handleInstallmentSelect = useCallback((installments: number) => {
    console.log(
      "💳 [OPPORTUNITY PAYMENT] Parcelas selecionadas:",
      installments
    );
    setSelectedInstallments(installments);
  }, []);

  // Handlers
  const handleMethodSelect = useCallback(
    (method: PaymentType, preserveSelectedCard = false) => {
      setSelectedMethod(method);

      // Só limpa o cartão selecionado se não for para preservar
      if (!preserveSelectedCard) {
        setSelectedSavedCard(undefined);
      }

      setCreditCardData(undefined);
      setShowNewCardForm(false);

      // Reset parcelas para 1 quando muda método de pagamento
      setSelectedInstallments(1);

      // Se selecionou cartão de crédito e não há cartões salvos, vai direto para formulário
      if (method === PaymentType.CreditCard && creditCards.length === 0) {
        setShowNewCardForm(true);
      }
    },
    [creditCards.length]
  );

  const handleSavedCardSelect = useCallback(
    (card: CreditCard | null) => {
      // Verificação de segurança para evitar erro quando card é null
      if (!card) {
        setSelectedSavedCard(undefined);
        return;
      }

      setSelectedSavedCard(card);
      // Preserva o método atual se for crédito, senão usa crédito como padrão
      if (selectedMethod !== PaymentType.CreditCard) {
        setSelectedMethod(PaymentType.CreditCard);
      }
      setCreditCardData(undefined);
      setShowNewCardForm(false);
    },
    [selectedMethod]
  );

  const handleAddNewCard = useCallback(() => {
    setSelectedSavedCard(undefined);
    setCreditCardData(undefined);
    setShowNewCardForm(true);
    setSelectedMethod(PaymentType.CreditCard);
    setStep("details"); // Navega diretamente para o formulário
  }, []);

  const handleBackToMethodSelection = useCallback(() => {
    setStep("method");
    setShowNewCardForm(false);
    setSelectedSavedCard(undefined);
    setCreditCardData(undefined);
  }, []);

  const handleCreditCardSubmit = useCallback(
    async (data: CreateCreditCardFormData) => {
      try {
        // Cria o cartão primeiro
        const newCardResponse = await createCreditCardMutation.mutateAsync(
          data
        );
        const newCard = newCardResponse.data;

        // Define como cartão selecionado
        setSelectedSavedCard(newCard);
        setCreditCardData(data);
        setStep("summary");
      } catch (error) {
        console.error("Erro ao criar cartão:", error);
        // O erro já é tratado pelo hook
      }
    },
    [createCreditCardMutation]
  );

  const handleNextStep = useCallback(() => {
    if (step === "method") {
      if (selectedMethod === PaymentType.CreditCard) {
        // Se selecionou um cartão salvo, vai direto para resumo
        if (selectedSavedCard) {
          setStep("summary");
          return;
        }

        // Se quer adicionar novo cartão ou não tem cartões salvos
        if (showNewCardForm || creditCards.length === 0) {
          setStep("details");
          return;
        }

        // Se tem cartões salvos mas não selecionou nenhum
        return;
      } else {
        // PIX ou Boleto - vai direto para resumo
        setStep("summary");
      }
    } else if (step === "details" && canProceedToSummary) {
      setStep("summary");
    }
  }, [
    step,
    selectedMethod,
    selectedSavedCard,
    showNewCardForm,
    creditCards.length,
    canProceedToSummary
  ]);

  const handlePreviousStep = useCallback(() => {
    if (step === "summary") {
      if (selectedMethod === PaymentType.CreditCard && !selectedSavedCard) {
        setStep("details");
      } else {
        setStep("method");
      }
    } else if (step === "details") {
      setStep("method");
    }
  }, [step, selectedMethod, selectedSavedCard]);

  // Terms handling functions
  const handleAcceptTerms = useCallback(
    async (termId: number, version: number) => {
      try {
        await acceptTermMutation.mutateAsync({termId, version});
        setTermsAccepted(true);
        setShowTermsModal(false);

        // After accepting terms, proceed with payment
        setTimeout(() => {
          handlePayment();
        }, 100);
      } catch (error) {
        console.error(
          "❌ [OPPORTUNITY-PAYMENT] Erro ao aceitar termos:",
          error
        );

        // Verificar se é erro 409 (termo já aceito)
        if (error && typeof error === "object" && "response" in error) {
          const apiError = error as any;
          if (apiError.response?.status === 409) {
            const errorMessage = apiError.response?.data?.message || "";
            console.log(
              "ℹ️ [OPPORTUNITY-PAYMENT] Termo já foi aceito anteriormente (409):",
              errorMessage
            );
            console.log(
              "✅ [OPPORTUNITY-PAYMENT] Continuando com pagamento..."
            );
            setTermsAccepted(true);
            setShowTermsModal(false);
            // Continua com o pagamento pois o termo já foi aceito
            setTimeout(() => {
              handlePayment();
            }, 100);
            return;
          }
        }

        // Para outros erros, mostrar mensagem de erro
        Alert.alert(t("terms.error.title"), t("terms.error.acceptFailed"));
      }
    },
    [acceptTermMutation, t]
  );

  const handleDeclineTerms = useCallback(() => {
    setShowTermsModal(false);
    router.back();
  }, []);

  const handleCloseTermsModal = useCallback(() => {
    setShowTermsModal(false);
  }, []);

  const handlePayment = useCallback(async () => {
    if (!paymentData) {
      Alert.alert("Erro", "Dados de pagamento incompletos.");
      return;
    }

    if (selectedMethod === undefined || selectedMethod === null) {
      Alert.alert(t("payment.error.title"), t("payment.error.noMethod"), [
        {text: t("common.ok")}
      ]);
      return;
    }

    // Validação específica para cartão de crédito
    if (selectedMethod === PaymentType.CreditCard && !selectedSavedCard) {
      Alert.alert(t("payment.error.title"), t("payment.error.noCard"), [
        {text: t("common.ok")}
      ]);
      return;
    }

    // Check if terms need to be accepted first
    if (opportunityTerm && !termsAccepted) {
      setShowTermsModal(true);
      return;
    }

    try {
      const paymentRequest: CreatePaymentRequest = {
        entity: PaymentEntity.Opportunity,
        entityId: paymentData.opportunityId,
        type: selectedMethod,
        termAccepted: termsAccepted || !opportunityTerm
      };

      // Adiciona dados específicos do método de pagamento
      const method = selectedMethod as PaymentType;
      if (method === PaymentType.CreditCard && selectedSavedCard) {
        // Garante conversão correta do ID do cartão
        const creditCardId =
          typeof selectedSavedCard.id === "string"
            ? parseInt(selectedSavedCard.id)
            : selectedSavedCard.id;

        paymentRequest.creditCard = {
          creditCardId: creditCardId,
          installmentCount: selectedInstallments
        };
      } else if (method === PaymentType.Boleto) {
        paymentRequest.boleto = {
          installmentCount: selectedInstallments
        };
      } else if (method === PaymentType.Pix) {
        paymentRequest.pix = {
          installmentCount: 1 // PIX sempre à vista
        };
      }

      const payment = await createPaymentMutation.mutateAsync(paymentRequest);

      // Redireciona para tela de confirmação com dados completos do pagamento
      router.push({
        pathname: "/(logged-stack)/payment-confirmation",
        params: {
          paymentId: payment.data.id,
          paymentData: JSON.stringify(payment.data), // Passa dados completos
          skipApiCall: "true" // Flag para não fazer chamada da API
        }
      });
    } catch (error) {
      console.error("Erro ao processar pagamento:", error);

      // Tratamento de erro mais específico
      let errorMessage = t("payment.error.message");

      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as any;

        // Personalizar mensagem baseada no status
        if (apiError.response?.status === 400) {
          errorMessage = t("payment.error.invalidData");
        } else if (apiError.response?.status === 401) {
          errorMessage = t("payment.error.unauthorized");
        } else if (apiError.response?.status >= 500) {
          errorMessage = t("payment.error.serverError");
        }
      }

      Alert.alert(t("payment.error.title"), errorMessage, [
        {text: t("common.ok")}
      ]);
    }
  }, [
    paymentData,
    selectedMethod,
    selectedSavedCard,
    createPaymentMutation,
    router,
    t
  ]);

  // Renderiza conteúdo baseado no passo atual - copiado exatamente da tela original
  const renderStepContent = () => {
    switch (step) {
      case "method":
        return (
          <PaymentMethodSelector
            selectedMethod={selectedMethod}
            onMethodSelect={handleMethodSelect}
            savedCreditCards={creditCards}
            onSelectSavedCard={handleSavedCardSelect}
            selectedSavedCard={selectedSavedCard}
            onAddNewCard={handleAddNewCard}
            showNewCardForm={showNewCardForm}
            disablePadding={true}
            amount={paymentItem.price}
            customInstallmentOptions={paymentData?.installmentOptions}
            selectedInstallments={selectedInstallments}
            onInstallmentSelect={handleInstallmentSelect}
          />
        );

      case "details":
        if (selectedMethod === PaymentType.CreditCard) {
          return (
            <CreditCardForm
              onSubmit={handleCreditCardSubmit}
              onBack={handleBackToMethodSelection}
              isLoading={
                createCreditCardMutation.isPending ||
                createPaymentMutation.isPending
              }
            />
          );
        }
        return null;

      case "summary":
        return (
          <PaymentMethodSelector
            selectedMethod={selectedMethod}
            onMethodSelect={handleMethodSelect}
            savedCreditCards={creditCards}
            onSelectSavedCard={handleSavedCardSelect}
            selectedSavedCard={selectedSavedCard}
            onAddNewCard={handleAddNewCard}
            showNewCardForm={showNewCardForm}
            disablePadding={true}
            amount={paymentItem.price}
            customInstallmentOptions={paymentData?.installmentOptions}
            selectedInstallments={selectedInstallments}
            onInstallmentSelect={handleInstallmentSelect}
          />
        );

      default:
        return null;
    }
  };

  if (!paymentData) {
    return (
      <View style={styles.container}>
        {/* Status Bar */}
        <View style={styles.statusBar}>
          <View style={styles.statusBarLeft} />
          <View style={styles.statusBarRight} />
        </View>

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Pagamento da Publicação</Text>
          </View>
          <View style={styles.headerSpacer} />
        </View>

        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>
            Carregando dados do pagamento...
          </Text>
        </View>
      </View>
    );
  }

  // Renderiza resumo de compra na parte inferior - copiado exatamente da tela original
  const renderActionButtons = () => {
    console.log("🔍 [OPPORTUNITY PAYMENT] Renderizando resumo de compra:", {
      step,
      selectedMethod,
      selectedSavedCard: !!selectedSavedCard,
      canProceedToDetails,
      canProceedToSummary
    });

    // Texto do botão baseado no step e método de pagamento - copiado da tela original
    const getButtonText = () => {
      if (step === "summary") {
        switch (selectedMethod) {
          case PaymentType.Pix:
            return "Gerar código de pagamento PIX";
          case PaymentType.CreditCard:
            return "Processar pagamento no cartão de crédito";
          case PaymentType.Boleto:
            return "Gerar boleto bancário";
          default:
            return "Confirmar pagamento";
        }
      }
      return "Selecionar pagamento";
    };

    const isProcessingPayment = createPaymentMutation.isPending;
    const canProceed = selectedMethod !== undefined;

    return (
      <View style={styles.purchaseSummaryContainer}>
        <Text style={styles.purchaseSummaryTitle}>Resumo de compra</Text>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Subtotal (1 item)</Text>
          <Text style={styles.summaryValue}>
            R${paymentItem.price.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Valor total</Text>
          <Text style={styles.totalValue}>
            R$ {paymentItem.price.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        {/* Botão de ação principal - copiado exatamente da tela original */}
        <TouchableOpacity
          style={[
            styles.selectPaymentButton,
            !canProceed && styles.selectPaymentButtonDisabled
          ]}
          onPress={
            canProceed && !isProcessingPayment
              ? step === "summary"
                ? handlePayment
                : handleNextStep
              : undefined
          }
          disabled={!canProceed || isProcessingPayment}
        >
          <Text style={styles.selectPaymentButtonText}>{getButtonText()}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar}>
        <View style={styles.statusBarLeft} />
        <View style={styles.statusBarRight} />
      </View>

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Pagamento da Publicação</Text>
        </View>
        <View style={styles.headerSpacer} />
      </View>

      {/* Payment Methods Section */}
      <View style={styles.paymentMethodsHeader}>
        <Text style={styles.paymentMethodsTitle}>Métodos de pagamento</Text>
        <Text style={styles.addNewMethodText}>Adicionar novo</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderStepContent()}
      </ScrollView>

      {renderActionButtons()}

      {(createPaymentMutation.isPending ||
        createCreditCardMutation.isPending) && <LoadingOverlay />}

      <TermsModal
        visible={showTermsModal}
        term={opportunityTerm || null}
        onAccept={handleAcceptTerms}
        onDecline={handleDeclineTerms}
        onClose={handleCloseTermsModal}
        title={t("payment.terms.title")}
        acceptButtonText={t("payment.terms.acceptAndPay")}
        declineButtonText={t("payment.terms.decline")}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828"
  },

  // Status Bar (iPhone mockup)
  statusBar: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 14.67,
    paddingTop: 18.07,
    paddingBottom: 9.83
  },
  statusBarLeft: {
    width: 28.43,
    height: 11.67,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginLeft: 18.78
  },
  statusBarRight: {
    width: 66.66,
    height: 11.93,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginTop: 0.17
  },

  // Header
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    justifyContent: "space-between"
  },
  backButton: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center"
  },
  headerTitle: {
    color: "#DFE9F0",
    fontSize: 14,
    lineHeight: 20,
    textAlign: "center",
    fontFamily: "Ubuntu",
    fontWeight: "700"
  },
  headerSpacer: {
    width: 24
  },

  // Payment Methods Header
  paymentMethodsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    marginBottom: 16
  },
  paymentMethodsTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    lineHeight: 20,
    fontFamily: "Ubuntu",
    fontWeight: "700"
  },
  addNewMethodText: {
    color: "#FFFFFF",
    fontSize: 12,
    lineHeight: 18,
    fontFamily: "Ubuntu",
    fontWeight: "700"
  },

  scrollView: {
    flex: 1
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20
  },
  loadingText: {
    fontSize: 16,
    color: "#DFE9F0",
    textAlign: "center",
    fontFamily: "Ubuntu"
  },

  // Container para o resumo de compra na parte inferior
  purchaseSummaryContainer: {
    backgroundColor: "#111828", // mainBackground
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    gap: 16
  },

  purchaseSummaryTitle: {
    fontSize: 14,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    fontFamily: "Ubuntu"
  },

  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 4,
    marginTop: 8
  },

  summaryLabel: {
    fontSize: 14,
    fontWeight: "700",
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },

  summaryValue: {
    fontSize: 14,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "right",
    fontFamily: "Ubuntu"
  },

  totalRow: {
    marginTop: 16,
    gap: 16
  },

  totalLabel: {
    fontSize: 16,
    fontWeight: "700",
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },

  totalValue: {
    fontSize: 16,
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "right",
    fontFamily: "Ubuntu"
  },

  selectPaymentButton: {
    backgroundColor: "#0F7C4D", // brand500
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 78,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 48,
    marginTop: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#0F7C4D"
  },

  selectPaymentButtonDisabled: {
    backgroundColor: "#44445A", // borderDefault
    opacity: 0.6,
    shadowOpacity: 0,
    elevation: 0
  },

  selectPaymentButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "700",
    fontFamily: "Ubuntu",
    textAlign: "center"
  }
});

export default OpportunityPublicationPayment;
