import React, {useCallback, useEffect, useMemo, useState} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView
} from "react-native";
import styles from "@/styles/modals/add-multiple-drawer.style";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";

export interface AddMultipleDrawerProps {
  visible: boolean;
  title: string; // ex.: "Adicionar objetivo(s)"
  placeholder: string; // ex.: "Insira o objetivo"
  maxLength?: number; // ex.: 96
  onClose: () => void;
  onBackToSelection?: () => void; // fechar e reabrir o drawer de seleção
  onSubmit: (items: string[]) => Promise<void>;
  submitting?: boolean;
}

const AddMultipleDrawer: React.FC<AddMultipleDrawerProps> = ({
  visible,
  title,
  placeholder,
  maxLength = 96,
  onClose,
  onBackToSelection,
  onSubmit,
  submitting = false
}) => {
  const [text, setText] = useState("");
  const [items, setItems] = useState<string[]>([]);

  const remaining = useMemo(
    () => `${text.length}/${maxLength} caracteres`,
    [text.length, maxLength]
  );

  // Debug visibility changes
  useEffect(() => {
    console.log("[AddMultipleDrawer] visible:", visible, "title:", title);
  }, [visible, title]);

  const handleAddLine = useCallback(() => {
    const value = text.trim();
    if (!value) return;
    // evitar duplicados (case-insensitive)
    const exists = items.some((i) => i.toLowerCase() === value.toLowerCase());
    if (exists) {
      console.log("[AddMultipleDrawer] duplicate ignored:", value);
      setText("");
      return;
    }
    console.log("[AddMultipleDrawer] line added:", value);
    setItems((prev) => [...prev, value]);
    setText("");
  }, [text, items]);

  const handleRemoveIndex = useCallback((idx: number) => {
    setItems((prev) => prev.filter((_, i) => i !== idx));
  }, []);

  const handleSubmit = useCallback(async () => {
    if (items.length === 0) {
      console.log("[AddMultipleDrawer] submit ignored: empty items");
      return;
    }
    console.log("[AddMultipleDrawer] submitting items:", items);
    await onSubmit(items);
    // limpar e fechar - a tela pai fará o refetch
    setItems([]);
    setText("");
    onClose();
  }, [items, onSubmit, onClose]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="overFullScreen"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => {
              console.log("[AddMultipleDrawer] back pressed");
              onClose?.();
              onBackToSelection?.();
            }}
            style={styles.backButton}
          >
            <ChevronLeftIcon />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <View style={{width: 24, height: 24}} />
        </View>

        <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
          <Text style={styles.label}>{title}</Text>

          <View style={styles.inputRow}>
            <TextInput
              style={styles.input}
              placeholder={placeholder}
              placeholderTextColor="#90A0AE"
              value={text}
              onChangeText={(v) => {
                if (v.length <= maxLength) setText(v);
              }}
              onSubmitEditing={handleAddLine}
              returnKeyType="done"
            />
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddLine}
              disabled={!text.trim()}
            >
              <Text style={styles.addButtonText}>+</Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.counter}>{remaining}</Text>

          {items.length > 0 && (
            <View style={styles.addedList}>
              <Text style={styles.addedTitle}>Item(s) adicionado(s)</Text>
              {items.map((it, idx) => (
                <View key={`${it}-${idx}`} style={styles.addedItemRow}>
                  <Text style={styles.addedItemText}>{it}</Text>
                  <TouchableOpacity
                    style={styles.removeChip}
                    onPress={() => handleRemoveIndex(idx)}
                  >
                    <Text style={styles.removeChipText}>Remover</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        </ScrollView>

        <View style={styles.footer}>
          <FullSizeButton
            text="Salvar item(s)"
            onPress={handleSubmit}
            disabled={items.length === 0 || submitting}
            loading={submitting}
          />
          <InvisibleFullSizeButton
            text="Voltar"
            onPress={() => {
              console.log("[AddMultipleDrawer] bottom back pressed");
              onClose?.();
              onBackToSelection?.();
            }}
          />
        </View>
      </View>
    </Modal>
  );
};

export default AddMultipleDrawer;
