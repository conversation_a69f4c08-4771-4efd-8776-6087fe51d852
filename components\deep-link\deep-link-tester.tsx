/**
 * Deep Link Tester Component
 * Development component to test deep linking functionality
 */

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { StyleSheet } from 'react-native';
import { DeepLinkService } from '@/services/deep-link.service';
import { useDeepLink } from '@/contexts/DeepLinkContext';
import { useDeepLinkAuth } from '@/hooks/use-deep-link-auth';
import { useAuth } from '@/contexts/AuthContext';

interface DeepLinkTesterProps {
  visible?: boolean;
}

export function DeepLinkTester({ visible = false }: DeepLinkTesterProps) {
  const [testUrl, setTestUrl] = useState('');
  const { pendingDeepLink, handleDeepLink } = useDeepLink();
  const { 
    hasPendingDeepLink, 
    needsBiometricAuth, 
    needsLogin,
    triggerBiometricAuth,
    cancelPendingDeepLink 
  } = useDeepLinkAuth();
  const { isAuthenticated, requiresBiometricAuth } = useAuth();

  if (!visible || __DEV__ !== true) {
    return null;
  }

  const testUrls = [
    'clubm://products/123',
    'clubm://events/456',
    'clubm://opportunities/789',
    'clubm://profile/101',
    'clubm://chat/202',
    'clubm://wallet',
    'clubm://magazine/303',
    'clubm://referral',
    'clubm://schedule',
    'clubm://home',
    'https://clubm.app/products/123',
    'https://clubm.app/events/456',
    'https://app.clubm.com/opportunities/789'
  ];

  const handleTestUrl = async (url: string) => {
    try {
      const parsed = DeepLinkService.parseDeepLink(url);
      
      if (!parsed.isValid) {
        Alert.alert('Invalid URL', parsed.error || 'Unknown error');
        return;
      }

      Alert.alert(
        'Parsed Deep Link',
        JSON.stringify(parsed.route, null, 2),
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Test Navigation', 
            onPress: () => handleDeepLink(url)
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', `Failed to parse URL: ${error}`);
    }
  };

  const handleCustomUrl = () => {
    if (!testUrl.trim()) {
      Alert.alert('Error', 'Please enter a URL to test');
      return;
    }
    handleTestUrl(testUrl);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Deep Link Tester</Text>
      
      {/* Auth Status */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusTitle}>Auth Status:</Text>
        <Text style={styles.statusText}>
          Authenticated: {isAuthenticated ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.statusText}>
          Needs Biometric: {requiresBiometricAuth ? 'Yes' : 'No'}
        </Text>
      </View>

      {/* Pending Deep Link Status */}
      {hasPendingDeepLink && (
        <View style={styles.pendingContainer}>
          <Text style={styles.pendingTitle}>Pending Deep Link:</Text>
          <Text style={styles.pendingText}>
            Type: {pendingDeepLink?.type}
          </Text>
          <Text style={styles.pendingText}>
            ID: {pendingDeepLink?.id || 'N/A'}
          </Text>
          <Text style={styles.pendingText}>
            Needs Login: {needsLogin ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.pendingText}>
            Needs Biometric: {needsBiometricAuth ? 'Yes' : 'No'}
          </Text>
          
          <View style={styles.buttonRow}>
            {needsBiometricAuth && (
              <TouchableOpacity 
                style={styles.biometricButton} 
                onPress={triggerBiometricAuth}
              >
                <Text style={styles.buttonText}>Trigger Biometric</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity 
              style={styles.cancelButton} 
              onPress={cancelPendingDeepLink}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Custom URL Input */}
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Test Custom URL:</Text>
        <TextInput
          style={styles.textInput}
          value={testUrl}
          onChangeText={setTestUrl}
          placeholder="Enter deep link URL..."
          placeholderTextColor="#666"
        />
        <TouchableOpacity style={styles.testButton} onPress={handleCustomUrl}>
          <Text style={styles.buttonText}>Test URL</Text>
        </TouchableOpacity>
      </View>

      {/* Predefined Test URLs */}
      <ScrollView style={styles.urlList}>
        <Text style={styles.urlListTitle}>Test URLs:</Text>
        {testUrls.map((url, index) => (
          <TouchableOpacity
            key={index}
            style={styles.urlItem}
            onPress={() => handleTestUrl(url)}
          >
            <Text style={styles.urlText}>{url}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
    zIndex: 9999,
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  statusTitle: {
    color: 'white',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
  },
  pendingContainer: {
    backgroundColor: 'rgba(255, 165, 0, 0.2)',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  pendingTitle: {
    color: 'orange',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  pendingText: {
    color: 'white',
    fontSize: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 10,
    gap: 10,
  },
  biometricButton: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 5,
    flex: 1,
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    padding: 8,
    borderRadius: 5,
    flex: 1,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    color: 'white',
    marginBottom: 5,
    fontWeight: 'bold',
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    color: 'white',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  testButton: {
    backgroundColor: '#34C759',
    padding: 10,
    borderRadius: 5,
  },
  urlList: {
    flex: 1,
  },
  urlListTitle: {
    color: 'white',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  urlItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 10,
    borderRadius: 5,
    marginBottom: 5,
  },
  urlText: {
    color: 'white',
    fontSize: 12,
  },
});

export default DeepLinkTester;
