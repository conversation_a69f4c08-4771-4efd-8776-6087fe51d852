import {from, map, Observable} from "rxjs";
import {Platform} from "react-native";
import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import stylesConstants from "../styles/styles-constants";

class PushNotificationService {
  /**
   * Initialize push notifications (Expo notifications)
   * This handles device push tokens for notifications
   */
  public static initPushNotification(): Observable<string | null> {
    return from(this.registerDevice()).pipe(
      map((token) => {
        Notifications.setNotificationHandler({
          handleNotification: async () => ({
            shouldPlaySound: true,
            shouldSetBadge: true,
            shouldShowBanner: true,
            shouldShowList: true
          })
        });

        return token;
      })
    );
  }

  /**
   * Register device for push notifications (Expo)
   */
  private static async registerDevice(): Promise<string | null> {
    console.log("🔔 Verificando se é dispositivo físico...");
    if (!Device.isDevice) {
      console.log(
        "🔔 Não é dispositivo físico - push notifications não suportadas"
      );
      return null;
    }

    console.log("🔔 Configurando canal de notificação para Android...");
    if (Platform.OS == "android") {
      await Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: stylesConstants.colors.brand.primary
      });
    }

    console.log("🔔 Verificando permissões de notificação...");
    const notificationPermissions = await Notifications.getPermissionsAsync();
    let finalStatus = notificationPermissions.status;
    console.log("🔔 Status inicial das permissões:", finalStatus);

    while (finalStatus != "granted") {
      console.log("🔔 Solicitando permissões de notificação...");
      finalStatus = (await Notifications.requestPermissionsAsync()).status;
      console.log("🔔 Novo status das permissões:", finalStatus);
    }

    console.log("🔔 Obtendo token do dispositivo...");
    // const resultToken = await Notifications.getDevicePushTokenAsync();
    const resultToken = await Notifications.getExpoPushTokenAsync();
    console.log("🔔 Token obtido:", !!resultToken.data);

    return resultToken.data;
  }
}

export default PushNotificationService;
