import React from "react";
import {View, Text, ActivityIndicator} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import {useCurrentUser} from "@/hooks/api/use-users";
import styles from "@/styles/wallet/user-profile-card.style";

interface UserProfileCardProps {
  /**
   * Estilo customizado para o container do cartão
   */
  style?: any;
}

const UserProfileCard: React.FC<UserProfileCardProps> = ({style}) => {
  const {t} = useTranslation();
  const {data: user, isLoading, error} = useCurrentUser();

  // Função para formatar o nome do usuário
  const formatUserName = (name?: string) => {
    if (!name) return t("wallet.userCard.defaultName", "Nome do associado");

    // Limitar o nome para evitar overflow no design
    if (name.length > 25) {
      return name.substring(0, 22) + "...";
    }
    return name;
  };

  // Função para determinar o tipo de membro baseado nos dados reais da API
  const getMembershipType = () => {
    // Usar dados reais do usuário se disponível
    if (user?.roles && user.roles.length > 0) {
      // Mapear roles para tipos de membro em português
      const roleMap: {[key: string]: string} = {
        admin: "Administrador",
        user: "Membro",
        premium: "Membro Premium",
        vip: "Membro VIP"
      };

      const primaryRole = user.roles[0];
      return roleMap[primaryRole] || "Membro";
    }

    // Fallback para dados do perfil se disponível
    if (user?.companyName) {
      return "Membro Empresarial";
    }

    return t("wallet.userCard.memberType", "Membro");
  };

  // Função para obter unidade federativa baseada nos dados do usuário
  const getFederationUnit = () => {
    // Se tiver dados de endereço, usar o estado
    if (user?.phone) {
      // Extrair código de área do telefone para determinar região
      const phoneMatch = user.phone.match(/\+55\s?(\d{2})/);
      if (phoneMatch) {
        const areaCode = phoneMatch[1];
        // Mapear alguns códigos de área para estados (exemplo)
        const stateMap: {[key: string]: string} = {
          "11": "São Paulo - SP",
          "21": "Rio de Janeiro - RJ",
          "31": "Minas Gerais - MG",
          "41": "Paraná - PR",
          "51": "Rio Grande do Sul - RS",
          "61": "Distrito Federal - DF",
          "71": "Bahia - BA",
          "85": "Ceará - CE"
        };
        return stateMap[areaCode] || "Brasil";
      }
    }
    return "Brasil";
  };

  // Função para obter unidade de associação
  const getAssociationUnit = () => {
    if (user?.companyName) {
      return user.companyName;
    }
    return "ClubM Digital";
  };

  // Função para obter data de ativação
  const getActiveSince = () => {
    if (user?.createdAt) {
      const date = new Date(user.createdAt);
      return date.toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      });
    }
    return new Date().toLocaleDateString("pt-BR", {
      month: "long",
      year: "numeric"
    });
  };

  // Estado de loading
  if (isLoading) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.logoSection}>
          <Image
            source={{
              uri: "https://static.motiffcontent.com/private/resource/image/19871cc0c856a54-d4162272-0758-4727-a57a-258a5b07f6b9.svg"
            }}
            style={styles.clubLogo}
            contentFit="contain"
          />
        </View>
        <View style={styles.cardSection}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#DFE9F0" />
            <Text style={styles.loadingText}>
              {t("wallet.userCard.loading", "Carregando...")}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  // Estado de erro
  if (error || !user) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.logoSection}>
          <Image
            source={{
              uri: "https://static.motiffcontent.com/private/resource/image/19871cc0c856a54-d4162272-0758-4727-a57a-258a5b07f6b9.svg"
            }}
            style={styles.clubLogo}
            contentFit="contain"
          />
        </View>
        <View style={styles.cardSection}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {t("wallet.userCard.error", "Erro ao carregar dados")}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Seção do logo do ClubM */}
      <View style={styles.logoSection}>
        <Image
          source={{
            uri: "https://static.motiffcontent.com/private/resource/image/19871cc0c856a54-d4162272-0758-4727-a57a-258a5b07f6b9.svg"
          }}
          style={styles.clubLogo}
          contentFit="contain"
        />
      </View>

      {/* Seção principal do cartão */}
      <View style={styles.cardSection}>
        {/* Padrão de fundo do cartão */}
        <Image
          source={require("../../assets/images/associado-card.png")}
          style={styles.cardBackground}
          contentFit="cover"
        />

        {/* Overlay com informações do usuário */}
        <View style={styles.userInfoOverlay}>
          <View style={styles.userInfoContainer}>
            <Text style={styles.userName} numberOfLines={2}>
              {formatUserName(user.name)}
            </Text>
          </View>
          <View style={styles.membershipContainer}>
            <Text style={styles.membershipType} numberOfLines={1}>
              {getMembershipType()}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

// Exportar as funções auxiliares para uso no componente pai
export const getUserCardData = (user: any) => {
  const getFederationUnit = () => {
    if (user?.phone) {
      const phoneRegex = /\+55\s?(\d{2})/;
      const phoneMatch = phoneRegex.exec(user.phone);
      if (phoneMatch) {
        const areaCode = phoneMatch[1];
        const stateMap: {[key: string]: string} = {
          "11": "São Paulo - SP",
          "21": "Rio de Janeiro - RJ",
          "31": "Minas Gerais - MG",
          "41": "Paraná - PR",
          "51": "Rio Grande do Sul - RS",
          "61": "Distrito Federal - DF",
          "71": "Bahia - BA",
          "85": "Ceará - CE"
        };
        return stateMap[areaCode] || "Brasil";
      }
    }
    return "Brasil";
  };

  const getAssociationUnit = () => {
    if (user?.companyName) {
      return user.companyName;
    }
    return "ClubM Digital";
  };

  const getActiveSince = () => {
    if (user?.createdAt) {
      const date = new Date(user.createdAt);
      return date.toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      });
    }
    return new Date().toLocaleDateString("pt-BR", {
      month: "long",
      year: "numeric"
    });
  };

  return {
    federationUnit: getFederationUnit(),
    associationUnit: getAssociationUnit(),
    activeSince: getActiveSince()
  };
};

export default UserProfileCard;
