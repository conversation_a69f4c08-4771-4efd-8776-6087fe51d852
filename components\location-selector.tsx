import React, {useState, useCallback, useMemo} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  SafeAreaView,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import stylesConstants from "@/styles/styles-constants";
import SearchIcon from "@/components/icons/search-icon";
import CloseIcon from "@/components/icons/close-icon";
import CheckIcon from "@/components/icons/check-icon";
import {useStates, useCitiesByState} from "@/hooks/api/use-addresses";

interface LocationSelectorProps {
  visible: boolean;
  selectedCity?: string;
  selectedState?: string;
  onLocationSelect: (city: string, state: string) => void;
  onClose: () => void;
}

type ViewMode = "states" | "cities";

const LocationSelector: React.FC<LocationSelectorProps> = ({
  visible,
  selectedCity,
  selectedState,
  onLocationSelect,
  onClose
}) => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("states");
  const [selectedStateUf, setSelectedStateUf] = useState<string>("");

  // Buscar estados
  const {
    data: states,
    isLoading: isLoadingStates,
    isError: isErrorStates
  } = useStates();

  // Buscar cidades do estado selecionado
  const {
    data: cities,
    isLoading: isLoadingCities,
    isError: isErrorCities
  } = useCitiesByState(selectedStateUf, {
    enabled: viewMode === "cities" && !!selectedStateUf
  });

  // Filtrar estados baseado na busca
  const filteredStates = useMemo(() => {
    if (!states) return [];
    return states.filter(
      (state) =>
        state.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        state.uf.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [states, searchTerm]);

  // Filtrar cidades baseado na busca
  const filteredCities = useMemo(() => {
    if (!cities) return [];
    return cities.filter((city) =>
      city.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [cities, searchTerm]);

  const handleStatePress = useCallback((stateUf: string) => {
    setSelectedStateUf(stateUf);
    setViewMode("cities");
    setSearchTerm(""); // Limpar busca ao mudar de view
  }, []);

  const handleCityPress = useCallback(
    (cityName: string, stateUf: string) => {
      onLocationSelect(cityName, stateUf);
    },
    [onLocationSelect]
  );

  const handleBackToStates = useCallback(() => {
    setViewMode("states");
    setSelectedStateUf("");
    setSearchTerm(""); // Limpar busca ao voltar
  }, []);

  const isStateSelected = useCallback(
    (stateUf: string) => {
      return selectedState === stateUf;
    },
    [selectedState]
  );

  const isCitySelected = useCallback(
    (cityName: string, stateUf: string) => {
      return selectedCity === cityName && selectedState === stateUf;
    },
    [selectedCity, selectedState]
  );

  // Reset view mode when modal opens
  const handleModalOpen = useCallback(() => {
    if (visible) {
      setViewMode("states");
      setSelectedStateUf("");
      setSearchTerm("");
    }
  }, [visible]);

  React.useEffect(() => {
    handleModalOpen();
  }, [handleModalOpen]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)"
        }}
      >
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: stylesConstants.colors.gray900,
            marginTop: 50,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: stylesConstants.colors.gray800
            }}
          >
            <View style={{flexDirection: "row", alignItems: "center"}}>
              {viewMode === "cities" && (
                <TouchableOpacity
                  onPress={handleBackToStates}
                  style={{marginRight: 12}}
                >
                  <Text
                    style={{
                      fontSize: 16,
                      color: stylesConstants.colors.primary,
                      fontWeight: "600"
                    }}
                  >
                    ← {t("common.back", "Voltar")}
                  </Text>
                </TouchableOpacity>
              )}
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "600",
                  color: stylesConstants.colors.white
                }}
              >
                {viewMode === "states"
                  ? t("locationSelector.selectState", "Selecionar estado")
                  : t("locationSelector.selectCity", "Selecionar cidade")}
              </Text>
            </View>
            <TouchableOpacity onPress={onClose}>
              <CloseIcon width={24} height={24} />
            </TouchableOpacity>
          </View>

          {/* Search */}
          <View
            style={{
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: stylesConstants.colors.gray800
            }}
          >
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: stylesConstants.colors.gray800,
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 12
              }}
            >
              <SearchIcon width={20} height={20} />
              <TextInput
                style={{
                  flex: 1,
                  marginLeft: 12,
                  fontSize: 16,
                  color: stylesConstants.colors.white
                }}
                value={searchTerm}
                onChangeText={setSearchTerm}
                placeholder={
                  viewMode === "states"
                    ? t("locationSelector.searchStates", "Buscar estado...")
                    : t("locationSelector.searchCities", "Buscar cidade...")
                }
                placeholderTextColor={stylesConstants.colors.gray25}
                autoCapitalize="words"
              />
            </View>
          </View>

          {/* Content List */}
          <ScrollView style={{flex: 1}}>
            {viewMode === "states" ? (
              // Renderizar Estados
              <>
                {isLoadingStates ? (
                  <View
                    style={{
                      padding: 40,
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <ActivityIndicator
                      size="large"
                      color={stylesConstants.colors.primary}
                    />
                    <Text
                      style={{
                        marginTop: 16,
                        color: stylesConstants.colors.gray25,
                        textAlign: "center"
                      }}
                    >
                      {t("common.loading", "Carregando...")}
                    </Text>
                  </View>
                ) : isErrorStates ? (
                  <View
                    style={{
                      padding: 40,
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <Text
                      style={{
                        color: "#FF3B30",
                        textAlign: "center",
                        marginBottom: 16
                      }}
                    >
                      {t("common.errorLoadingData", "Erro ao carregar dados")}
                    </Text>
                  </View>
                ) : (
                  filteredStates.map((state, index) => (
                    <TouchableOpacity
                      key={`state-${state.uf || state.id || index}-${index}`}
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: 20,
                        borderBottomWidth:
                          index < filteredStates.length - 1 ? 1 : 0,
                        borderBottomColor: stylesConstants.colors.gray800
                      }}
                      onPress={() => handleStatePress(state.uf)}
                    >
                      <View>
                        <Text
                          style={{
                            fontSize: 16,
                            fontWeight: "500",
                            color: stylesConstants.colors.white
                          }}
                        >
                          {state.name}
                        </Text>
                        <Text
                          style={{
                            fontSize: 14,
                            color: stylesConstants.colors.gray25,
                            marginTop: 2
                          }}
                        >
                          {state.uf}
                        </Text>
                      </View>
                      {isStateSelected(state.uf) && (
                        <CheckIcon width={20} height={20} />
                      )}
                    </TouchableOpacity>
                  ))
                )}
              </>
            ) : (
              // Renderizar Cidades
              <>
                {isLoadingCities ? (
                  <View
                    style={{
                      padding: 40,
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <ActivityIndicator
                      size="large"
                      color={stylesConstants.colors.primary}
                    />
                    <Text
                      style={{
                        marginTop: 16,
                        color: stylesConstants.colors.gray25,
                        textAlign: "center"
                      }}
                    >
                      {t("common.loading", "Carregando...")}
                    </Text>
                  </View>
                ) : isErrorCities ? (
                  <View
                    style={{
                      padding: 40,
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <Text
                      style={{
                        color: "#FF3B30",
                        textAlign: "center",
                        marginBottom: 16
                      }}
                    >
                      {t("common.errorLoadingData", "Erro ao carregar dados")}
                    </Text>
                  </View>
                ) : (
                  filteredCities.map((city, index) => (
                    <TouchableOpacity
                      key={`city-${selectedStateUf}-${city.name}-${
                        city.id || index
                      }-${index}`}
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: 20,
                        borderBottomWidth:
                          index < filteredCities.length - 1 ? 1 : 0,
                        borderBottomColor: stylesConstants.colors.gray800
                      }}
                      onPress={() =>
                        handleCityPress(city.name, selectedStateUf)
                      }
                    >
                      <View>
                        <Text
                          style={{
                            fontSize: 16,
                            fontWeight: "500",
                            color: stylesConstants.colors.white
                          }}
                        >
                          {city.name}
                        </Text>
                      </View>
                      {isCitySelected(city.name, selectedStateUf) && (
                        <CheckIcon width={20} height={20} />
                      )}
                    </TouchableOpacity>
                  ))
                )}
              </>
            )}
          </ScrollView>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

export default LocationSelector;
