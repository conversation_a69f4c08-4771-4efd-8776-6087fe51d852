import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../styles-constants";

const {height: screenHeight} = Dimensions.get("window");

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  container: {
    backgroundColor: stylesConstants.colors.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: screenHeight * 0.6,
    maxHeight: screenHeight * 0.8,
    position: "relative",
    overflow: "hidden"
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  textContainer: {
    alignItems: "center",
    marginBottom: 40,
    paddingHorizontal: 16
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 24,
    fontWeight: "bold",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    marginBottom: 16
  },
  description: {
    color: stylesConstants.colors.gray300,
    fontSize: 16,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 24
  },
  buttonsContainer: {
    width: "100%",
    gap: 16
  }
});

export default styles;
