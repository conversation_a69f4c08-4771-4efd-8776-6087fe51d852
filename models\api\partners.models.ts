/**
 * Modelos de dados para Parceiros baseados na API ClubM
 * Baseado na especificação OpenAPI do Swagger
 */

import {z} from "zod";
import {PaginationRequest, PaginationResponse} from "./common.models";

// Enums baseados na API
export enum PartnerStatus {
  Inactive = 0,
  Active = 1
}

// Interface principal para Partner (baseada na API)
export interface Partner {
  id: number;
  name: string;
  description?: string;
  category?: string;
  logoId?: string;
  logoUrl?: string;
  status: PartnerStatus;
  createdAt: string;
  updatedAt?: string;
  // Campos adicionais que podem vir da API
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
}

// Parâmetros para listar parceiros
export interface PartnerListParams extends PaginationRequest {
  search?: string;
  status?: PartnerStatus;
  category?: string;
}

// Resposta da lista de parceiros
export interface PartnerListResponse extends PaginationResponse<Partner> {}

// Resposta de detalhes do parceiro
export interface PartnerDetailsResponse {
  partner: Partner;
  benefitsCount?: number;
  activeOffersCount?: number;
}

// Request para criar parceiro (admin)
export interface CreatePartnerRequest {
  name: string;
  description?: string;
  category?: string;
  logoUrl?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  status?: PartnerStatus;
}

// Request para atualizar parceiro (admin)
export interface UpdatePartnerRequest extends Partial<CreatePartnerRequest> {}

// Schemas de validação com Zod
export const CreatePartnerSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").max(200, "Nome muito longo"),
  description: z.string().max(1000, "Descrição muito longa").optional(),
  category: z.string().max(100, "Categoria muito longa").optional(),
  logoUrl: z.string().url("URL do logo inválida").optional(),
  website: z.string().url("URL do website inválida").optional(),
  email: z.string().email("Email inválido").optional(),
  phone: z.string().max(20, "Telefone muito longo").optional(),
  address: z.string().max(500, "Endereço muito longo").optional(),
  status: z.nativeEnum(PartnerStatus).optional()
});

export const UpdatePartnerSchema = CreatePartnerSchema.partial();

export const PartnerListParamsSchema = z.object({
  page: z.number().min(1, "Página deve ser maior que 0").optional(),
  pageSize: z
    .number()
    .min(1, "Tamanho da página deve ser maior que 0")
    .max(100, "Tamanho da página muito grande")
    .optional(),
  search: z.string().optional(),
  status: z.nativeEnum(PartnerStatus).optional(),
  category: z.string().optional()
});

// Tipos derivados dos schemas
export type CreatePartnerData = z.infer<typeof CreatePartnerSchema>;
export type UpdatePartnerData = z.infer<typeof UpdatePartnerSchema>;
export type PartnerListParamsData = z.infer<typeof PartnerListParamsSchema>;

// Função de validação para criar parceiro
export const validateCreatePartner = (data: any): string[] => {
  const result = CreatePartnerSchema.safeParse(data);
  if (result.success) return [];

  return result.error.errors.map((err) => err.message);
};

// Função de validação para atualizar parceiro
export const validateUpdatePartner = (data: any): string[] => {
  const result = UpdatePartnerSchema.safeParse(data);
  if (result.success) return [];

  return result.error.errors.map((err) => err.message);
};
