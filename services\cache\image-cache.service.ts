/**
 * Serviço de cache global para imagens de parceiros
 * Permite compartilhar imagens carregadas entre diferentes telas
 */

class ImageCacheService {
  private cache: Map<string, string> = new Map();

  /**
   * Obtém uma imagem do cache
   */
  get(logoId: string): string | undefined {
    return this.cache.get(logoId);
  }

  /**
   * Armazena uma imagem no cache
   */
  set(logoId: string, dataUri: string): void {
    this.cache.set(logoId, dataUri);
  }

  /**
   * Verifica se uma imagem está no cache
   */
  has(logoId: string): boolean {
    return this.cache.has(logoId);
  }

  /**
   * Remove uma imagem do cache
   */
  delete(logoId: string): boolean {
    return this.cache.delete(logoId);
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Obtém o tamanho do cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Lista todas as chaves no cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }
}

// Instância singleton do cache
export const imageCache = new ImageCacheService();
