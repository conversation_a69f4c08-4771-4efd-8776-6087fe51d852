/**
 * Serviço de QR codes para ClubM
 * Gerencia operações relacionadas aos QR codes de tickets e pagamentos
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";

export class QRCodeService {
  /**
   * Buscar QR code de um ticket específico
   * Endpoint: GET /api/app/events/check-ins/@me/qr-codes
   *
   * NOTA: Como a API não tem endpoint específico para um ticket,
   * buscamos todos os QR codes e filtramos pelo ticket ID.
   * Se não encontrar, gera um QR code conforme especificação do backend.
   */
  static async getTicketQRCode(
    ticketId: string,
    eventId?: number,
    userId?: number
  ): Promise<string | null> {
    try {
      ApiLogger.info("Buscando QR code do ticket", {ticketId});

      // Buscar todos os QR codes do usuário
      const response = await firstValueFrom(
        apiClient.get<any>("/api/app/events/check-ins/@me/qr-codes", {
          pageSize: 1000 // Buscar todos para encontrar o ticket específico
        })
      );

      // Log da resposta para debug
      console.log(
        "🔍 Resposta da API de QR codes:",
        JSON.stringify(response, null, 2)
      );

      // Verificar se a resposta tem dados
      if (!response?.data || !Array.isArray(response.data)) {
        ApiLogger.warn("Resposta da API não contém dados válidos", response);
        return null;
      }

      // Procurar o ticket específico nos dados retornados
      const ticketQRData = response.data.find((item: any) => {
        // Tentar diferentes formas de identificar o ticket
        return (
          item.id === ticketId ||
          item.ticketId === ticketId ||
          item.eventCheckInId === ticketId ||
          item.checkInId === ticketId
        );
      });

      if (!ticketQRData) {
        ApiLogger.info("QR code não encontrado na API para o ticket", {
          ticketId
        });

        // Se temos eventId e userId, gerar QR code conforme especificação do backend
        if (eventId && userId) {
          const generatedQRCode = this.generateTicketQRValue(
            ticketId,
            eventId,
            userId
          );
          ApiLogger.info("QR code gerado localmente para o ticket", {
            ticketId,
            eventId,
            userId
          });
          return generatedQRCode;
        }

        // Se não temos os dados necessários, retornar null
        ApiLogger.warn(
          "Não foi possível gerar QR code - eventId ou userId ausentes",
          {
            ticketId,
            hasEventId: !!eventId,
            hasUserId: !!userId
          }
        );
        return null;
      }

      // Extrair o QR code dos dados
      // A API pode retornar o QR code em diferentes formatos
      let qrCodeValue = null;

      if (ticketQRData.qrCode) {
        qrCodeValue = ticketQRData.qrCode;
      } else if (ticketQRData.qrCodeData) {
        qrCodeValue = ticketQRData.qrCodeData;
      } else if (ticketQRData.code) {
        qrCodeValue = ticketQRData.code;
      }

      // Se encontrou um QR code válido na API, usar ele
      if (qrCodeValue) {
        ApiLogger.info("QR code do ticket encontrado na API", {
          ticketId,
          qrCodeLength: qrCodeValue.length
        });
        return qrCodeValue;
      }

      // Se não encontrou QR code na API mas temos eventId e userId, gerar conforme especificação
      if (eventId && userId) {
        const generatedQRCode = this.generateTicketQRValue(
          ticketId,
          eventId,
          userId
        );
        ApiLogger.info(
          "QR code gerado localmente para o ticket (API retornou dados mas sem QR code)",
          {
            ticketId,
            eventId,
            userId
          }
        );
        return generatedQRCode;
      }

      // Se não temos os dados necessários, usar ID como último recurso
      if (ticketQRData.id) {
        ApiLogger.warn("Usando ID do ticket como QR code (fallback)", {
          ticketId,
          ticketQRDataId: ticketQRData.id
        });
        return ticketQRData.id;
      }

      ApiLogger.warn("Dados do QR code não encontrados no ticket", {
        ticketId,
        ticketQRData
      });
      return null;
    } catch (error) {
      ApiLogger.error("Erro ao buscar QR code do ticket", error as Error, {
        ticketId
      });
      throw error;
    }
  }

  /**
   * Buscar QR code de um pagamento específico
   * Endpoint: GET /api/app/payments/{id}/qr-code
   */
  static async getPaymentQRCode(paymentId: string): Promise<string | null> {
    try {
      ApiLogger.info("Buscando QR code do pagamento", {paymentId});

      const response = await firstValueFrom(
        apiClient.get<any>(`/api/app/payments/${paymentId}/qr-code`)
      );

      // Log da resposta para debug
      console.log(
        "🔍 Resposta da API de QR code do pagamento:",
        JSON.stringify(response, null, 2)
      );

      // Verificar se a resposta tem dados
      if (!response) {
        ApiLogger.warn("Resposta da API vazia para QR code do pagamento", {
          paymentId
        });
        return null;
      }

      // Extrair o QR code dos dados
      // A API pode retornar o QR code em diferentes formatos
      let qrCodeValue = null;

      if (typeof response === "string") {
        qrCodeValue = response;
      } else if (response.qrCode) {
        qrCodeValue = response.qrCode;
      } else if (response.qrCodeData) {
        qrCodeValue = response.qrCodeData;
      } else if (response.data) {
        qrCodeValue = response.data;
      } else if (response.code) {
        qrCodeValue = response.code;
      }

      if (!qrCodeValue) {
        ApiLogger.warn("QR code não encontrado na resposta do pagamento", {
          paymentId,
          response
        });
        return null;
      }

      ApiLogger.info("QR code do pagamento encontrado", {
        paymentId,
        qrCodeLength: qrCodeValue.length
      });

      return qrCodeValue;
    } catch (error) {
      ApiLogger.error("Erro ao buscar QR code do pagamento", error as Error, {
        paymentId
      });
      throw error;
    }
  }

  /**
   * Gerar valor do QR code para um ticket conforme especificação do backend
   * Cria um objeto JSON com Id, EventId, UserId, converte para string e depois para base64
   */
  static generateTicketQRValue(
    ticketId: string,
    eventId: number,
    userId: number
  ): string {
    try {
      // Criar objeto conforme especificação do backend
      const qrData = {
        Id: ticketId,
        EventId: eventId,
        UserId: userId
      };

      // Converter para JSON string
      const jsonString = JSON.stringify(qrData);

      // Converter para base64
      const base64Value = btoa(jsonString);

      console.log("🔍 QR Code gerado:", {
        ticketId,
        eventId,
        userId,
        jsonString,
        base64Value
      });

      return base64Value;
    } catch (error) {
      console.error("Erro ao gerar QR code:", error);
      // Fallback para valor simples em caso de erro
      return btoa(ticketId);
    }
  }

  /**
   * Validar se um valor é um QR code válido
   */
  static isValidQRCode(value: string): boolean {
    if (!value || typeof value !== "string") {
      return false;
    }

    // QR code deve ter pelo menos alguns caracteres
    if (value.length < 3) {
      return false;
    }

    // Verificar se não é apenas espaços em branco
    if (value.trim().length === 0) {
      return false;
    }

    return true;
  }
}
