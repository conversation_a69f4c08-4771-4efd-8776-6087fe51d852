/**
 * Deep Link Service
 * Handles parsing, validation, and routing of deep links
 */

import { Linking } from 'react-native';
import { router } from 'expo-router';
import { ApiLogger } from '@/services/api/base/api-logger';

export interface DeepLinkRoute {
  type: 'product' | 'event' | 'opportunity' | 'profile' | 'chat' | 'wallet' | 'magazine' | 'referral' | 'schedule' | 'home' | 'partners' | 'business-center';
  id?: string;
  params?: Record<string, string>;
  requiresAuth: boolean;
  path: string;
}

export interface ParsedDeepLink {
  isValid: boolean;
  route?: DeepLinkRoute;
  originalUrl: string;
  error?: string;
}

export class DeepLinkService {
  private static readonly SUPPORTED_SCHEMES = ['clubm', 'https', 'http'];
  private static readonly SUPPORTED_HOSTS = ['clubm.app', 'app.clubm.com', 'www.clubm.app', 'www.app.clubm.com'];

  /**
   * Parse a deep link URL and extract routing information
   */
  static parseDeepLink(url: string): ParsedDeepLink {
    try {
      ApiLogger.info('Parsing deep link', { url });

      const parsedUrl = new URL(url);
      const { protocol, hostname, pathname, searchParams } = parsedUrl;

      // Remove protocol suffix (e.g., 'clubm:' -> 'clubm')
      const scheme = protocol.replace(':', '');

      // Validate scheme
      if (!this.SUPPORTED_SCHEMES.includes(scheme)) {
        return {
          isValid: false,
          originalUrl: url,
          error: `Unsupported scheme: ${scheme}`
        };
      }

      // For custom scheme (clubm://), hostname is the first path segment
      // For https/http, validate hostname
      if (scheme === 'clubm') {
        // clubm://products/123 -> hostname is 'products', pathname is '/123'
        const segments = pathname.split('/').filter(Boolean);
        if (hostname) {
          segments.unshift(hostname);
        }
        return this.parsePathSegments(segments, searchParams, url);
      } else {
        // https://clubm.app/products/123
        if (!this.SUPPORTED_HOSTS.includes(hostname)) {
          return {
            isValid: false,
            originalUrl: url,
            error: `Unsupported host: ${hostname}`
          };
        }
        const segments = pathname.split('/').filter(Boolean);
        return this.parsePathSegments(segments, searchParams, url);
      }
    } catch (error) {
      ApiLogger.error('Error parsing deep link', error as Error);
      return {
        isValid: false,
        originalUrl: url,
        error: `Invalid URL format: ${error}`
      };
    }
  }

  /**
   * Parse path segments and create route information
   */
  private static parsePathSegments(
    segments: string[],
    searchParams: URLSearchParams,
    originalUrl: string
  ): ParsedDeepLink {
    if (segments.length === 0) {
      // Root URL -> home
      return {
        isValid: true,
        originalUrl,
        route: {
          type: 'home',
          requiresAuth: true,
          path: '/(tabs)/home'
        }
      };
    }

    const [firstSegment, secondSegment, ...restSegments] = segments;
    const params: Record<string, string> = {};

    // Convert search params to object
    searchParams.forEach((value, key) => {
      params[key] = value;
    });

    switch (firstSegment.toLowerCase()) {
      case 'products':
      case 'product':
        if (!secondSegment) {
          return { isValid: false, originalUrl, error: 'Product ID is required' };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'product',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: '/(logged-stack)/product-page'
          }
        };

      case 'events':
      case 'event':
        if (!secondSegment) {
          return { isValid: false, originalUrl, error: 'Event ID is required' };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'event',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: '/(events)/event-sale'
          }
        };

      case 'opportunities':
      case 'opportunity':
        if (!secondSegment) {
          // Business center listing
          return {
            isValid: true,
            originalUrl,
            route: {
              type: 'business-center',
              params,
              requiresAuth: true,
              path: '/(business)/business-center'
            }
          };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'opportunity',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: `/(business)/opportunities/${secondSegment}`
          }
        };

      case 'profile':
      case 'user-profile':
      case 'users':
      case 'user':
        if (!secondSegment) {
          return { isValid: false, originalUrl, error: 'User ID is required' };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'profile',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: `/(main)/user-profile/${secondSegment}`
          }
        };

      case 'chat':
      case 'messages':
      case 'message':
        if (!secondSegment) {
          return { isValid: false, originalUrl, error: 'Chat ID is required' };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'chat',
            id: secondSegment,
            params: { ...params, chatId: secondSegment },
            requiresAuth: true,
            path: '/(logged-stack)/chat'
          }
        };

      case 'wallet':
      case 'carteira-digital':
      case 'carteira':
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'wallet',
            params,
            requiresAuth: true,
            path: '/(wallet)/wallet'
          }
        };

      case 'magazine':
      case 'revista':
        if (!secondSegment) {
          // Magazine listing
          return {
            isValid: true,
            originalUrl,
            route: {
              type: 'magazine',
              params,
              requiresAuth: true,
              path: '/(magazine)/magazine-list'
            }
          };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'magazine',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: `/(magazine)/magazine-details/${secondSegment}`
          }
        };

      case 'referral':
      case 'indicacao-amigos':
      case 'indicacao':
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'referral',
            params,
            requiresAuth: true,
            path: '/(main)/referral'
          }
        };

      case 'schedule':
      case 'agenda':
      case 'calendar':
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'schedule',
            params,
            requiresAuth: true,
            path: '/(tabs)/schedule'
          }
        };

      case 'partners':
      case 'partner':
      case 'parceiros':
      case 'parceiro':
        if (!secondSegment) {
          return { isValid: false, originalUrl, error: 'Partner ID is required' };
        }
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'partners',
            id: secondSegment,
            params,
            requiresAuth: true,
            path: `/(main)/partners/${secondSegment}`
          }
        };

      case 'home':
      case 'inicio':
        return {
          isValid: true,
          originalUrl,
          route: {
            type: 'home',
            params,
            requiresAuth: true,
            path: '/(tabs)/home'
          }
        };

      default:
        return {
          isValid: false,
          originalUrl,
          error: `Unsupported route: ${firstSegment}`
        };
    }
  }

  /**
   * Navigate to a parsed deep link route
   */
  static navigateToRoute(route: DeepLinkRoute): void {
    try {
      ApiLogger.info('Navigating to deep link route', { route });

      if (route.id) {
        // Route with ID parameter
        router.push({
          pathname: route.path as any,
          params: { id: route.id, ...route.params }
        });
      } else if (route.params && Object.keys(route.params).length > 0) {
        // Route with query parameters
        const queryString = new URLSearchParams(route.params).toString();
        const pathWithQuery = `${route.path}?${queryString}`;
        router.push(pathWithQuery as any);
      } else {
        // Simple route
        router.push(route.path as any);
      }
    } catch (error) {
      ApiLogger.error('Error navigating to deep link route', error as Error);
      throw error;
    }
  }

  /**
   * Get the initial URL when app is opened via deep link
   */
  static async getInitialURL(): Promise<string | null> {
    try {
      const url = await Linking.getInitialURL();
      if (url) {
        ApiLogger.info('Initial deep link URL detected', { url });
      }
      return url;
    } catch (error) {
      ApiLogger.error('Error getting initial URL', error as Error);
      return null;
    }
  }

  /**
   * Add listener for incoming deep links while app is running
   */
  static addLinkingListener(callback: (url: string) => void): () => void {
    const subscription = Linking.addEventListener('url', ({ url }) => {
      ApiLogger.info('Deep link received while app running', { url });
      callback(url);
    });

    return () => subscription?.remove();
  }
}
