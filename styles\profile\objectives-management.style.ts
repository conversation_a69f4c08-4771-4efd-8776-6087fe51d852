import { StyleSheet } from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1a1a1a"
  },
  
  scrollView: {
    flex: 1
  },
  
  header: {
    padding: 20,
    paddingBottom: 10
  },
  
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 8
  },
  
  subtitle: {
    fontSize: 16,
    color: "#cccccc",
    lineHeight: 22
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20
  },
  
  loadingText: {
    fontSize: 16,
    color: "#cccccc",
    marginTop: 16,
    textAlign: "center"
  },
  
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20
  },
  
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#FF3B30",
    marginBottom: 8,
    textAlign: "center"
  },
  
  errorMessage: {
    fontSize: 16,
    color: "#cccccc",
    textAlign: "center",
    lineHeight: 22
  },
  
  emptyState: {
    padding: 40,
    alignItems: "center"
  },
  
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#ffffff",
    marginBottom: 8,
    textAlign: "center"
  },
  
  emptyMessage: {
    fontSize: 16,
    color: "#cccccc",
    textAlign: "center",
    lineHeight: 22
  }
});
