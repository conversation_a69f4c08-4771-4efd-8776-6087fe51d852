/**
 * Hooks React Query para formulários
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import {
  FormViewModelPaginateViewModel,
  FormAnswersResponse,
  CreateFormAnswersViewModel,
  FormsListParams,
  FormAnswersListParams
} from "@/models/api/forms.models";
import FormsService from "@/services/api/forms/forms.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";
import {showSuccessToast} from "@/utils/error-handler";

// Query keys para cache
export const formsKeys = {
  all: ["forms"] as const,
  lists: () => [...formsKeys.all, "list"] as const,
  list: (params?: FormsListParams) => [...formsKeys.lists(), params] as const,
  details: () => [...formsKeys.all, "detail"] as const,
  detail: (id: number) => [...formsKeys.details(), id] as const,
  answers: (formId: number) => [...formsKeys.all, "answers", formId] as const,
  available: () => [...formsKeys.all, "available"] as const
};

/**
 * Hook para buscar lista de formulários
 */
export const useForms = (
  params?: FormsListParams,
  options?: UseQueryOptions<FormViewModelPaginateViewModel, BaseApiError>
) => {
  const isEnabled = options?.enabled !== false;

  return useQuery({
    queryKey: formsKeys.list(params),
    queryFn: () => FormsService.getForms(params),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    enabled: isEnabled,
    refetchOnMount: isEnabled,
    refetchOnWindowFocus: isEnabled,
    refetchOnReconnect: isEnabled,
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden) ou 404 (Not Found)
      if (error?.status === 403 || error?.status === 404) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para buscar respostas de um formulário
 */
export const useFormAnswers = (
  params: FormAnswersListParams,
  options?: UseQueryOptions<FormAnswersResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: formsKeys.answers(params.formId),
    queryFn: () => FormsService.getFormAnswers(params),
    enabled: !!params.formId,
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: (failureCount, error) => {
      if (error?.status === 403 || error?.status === 404) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para verificar se há formulários disponíveis
 */
export const useAvailableForms = (
  options?: UseQueryOptions<boolean, BaseApiError>
) => {
  const isEnabled = options?.enabled !== false;

  return useQuery({
    queryKey: formsKeys.available(),
    queryFn: () => FormsService.hasAvailableForms(),
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    enabled: isEnabled,
    refetchOnMount: isEnabled,
    refetchOnWindowFocus: isEnabled,
    refetchOnReconnect: isEnabled,
    retry: (failureCount, error) => {
      // Em caso de erro, assumir que não há formulários disponíveis
      return false;
    },
    ...options
  });
};

/**
 * Hook para enviar respostas de formulário
 */
export const useSubmitFormAnswers = (
  options?: UseMutationOptions<
    void,
    BaseApiError,
    {formId: number; answers: CreateFormAnswersViewModel}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({formId, answers}) =>
      FormsService.submitFormAnswers(formId, answers),
    onSuccess: (_, variables) => {
      ApiLogger.info("Respostas do formulário enviadas com sucesso", {
        formId: variables.formId,
        answersCount: variables.answers.answers?.length || 0
      });

      // Invalidar cache de respostas do formulário
      queryClient.invalidateQueries({
        queryKey: formsKeys.answers(variables.formId)
      });

      // Invalidar cache de formulários disponíveis
      queryClient.invalidateQueries({
        queryKey: formsKeys.available()
      });

      // Mostrar toast de sucesso
      showSuccessToast("Formulário enviado com sucesso!");
    },
    onError: (error, variables) => {
      ApiLogger.error("Erro ao enviar respostas do formulário", {
        error,
        formId: variables.formId,
        answersCount: variables.answers.answers?.length || 0
      });
    },
    ...options
  });
};

/**
 * Hook para invalidar cache de formulários
 */
export const useInvalidateForms = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => {
      queryClient.invalidateQueries({queryKey: formsKeys.all});
    },
    invalidateLists: () => {
      queryClient.invalidateQueries({queryKey: formsKeys.lists()});
    },
    invalidateForm: (formId: number) => {
      queryClient.invalidateQueries({queryKey: formsKeys.detail(formId)});
    },
    invalidateAnswers: (formId: number) => {
      queryClient.invalidateQueries({queryKey: formsKeys.answers(formId)});
    },
    invalidateAvailable: () => {
      queryClient.invalidateQueries({queryKey: formsKeys.available()});
    }
  };
};

/**
 * Hook para prefetch de formulários
 */
export const usePrefetchForms = () => {
  const queryClient = useQueryClient();

  return {
    prefetchForms: (params?: FormsListParams) => {
      queryClient.prefetchQuery({
        queryKey: formsKeys.list(params),
        queryFn: () => FormsService.getForms(params),
        staleTime: 5 * 60 * 1000
      });
    },
    prefetchForm: (formId: number) => {
      queryClient.prefetchQuery({
        queryKey: formsKeys.detail(formId),
        queryFn: () => FormsService.getForm(formId),
        staleTime: 5 * 60 * 1000
      });
    },
    prefetchAvailable: () => {
      queryClient.prefetchQuery({
        queryKey: formsKeys.available(),
        queryFn: () => FormsService.hasAvailableForms(),
        staleTime: 2 * 60 * 1000
      });
    }
  };
};
