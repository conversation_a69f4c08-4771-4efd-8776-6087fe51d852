# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 52ms
  generate-prefab-packages
    [gap of 101ms]
    exec-prefab 2181ms
    [gap of 63ms]
  generate-prefab-packages completed in 2345ms
  execute-generate-process
    [gap of 12ms]
    exec-configure 2639ms
    [gap of 111ms]
  execute-generate-process completed in 2762ms
  remove-unexpected-so-files 18ms
  [gap of 83ms]
generate_cxx_metadata completed in 5295ms

