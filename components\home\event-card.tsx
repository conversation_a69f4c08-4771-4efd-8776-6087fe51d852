import React, {useCallback, useMemo} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator
} from "react-native";
import {useRouter} from "expo-router";
import Avatar from "@/components/user/avatar";
import Button from "@/components/button";
import styles from "@/styles/components/home/<USER>";
import {Event} from "@/models/api/events.models";
import MarkerPinIcon from "@/components/icons/marker-pin-icon";
import ClockIcon from "@/components/icons/clock-icon";

export interface EventCardProps {
  event: Event;
  onEventPress?: (eventId: string) => void;
  isLoading?: boolean;
}

const EventCard: React.FC<EventCardProps> = ({
  event,
  onEventPress,
  isLoading = false
}) => {
  const router = useRouter();

  const formattedTime = useMemo(() => {
    try {
      const startDate = new Date(event.startDate);
      const endDate = new Date(event.endDate);
      return `${startDate.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      })} - ${endDate.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      })}`;
    } catch {
      return "Horário não informado";
    }
  }, [event.startDate, event.endDate]);

  const formattedHeaderDate = useMemo(() => {
    try {
      const date = new Date(event.startDate);
      return (
        date.toLocaleDateString("pt-BR", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric"
        }) +
        ", às " +
        date.toLocaleTimeString("pt-BR", {hour: "2-digit", minute: "2-digit"})
      );
    } catch {
      return "Data não informada";
    }
  }, [event.startDate]);

  const formattedPrice = useMemo(() => {
    if (event.price?.isFree) {
      return "Gratuito";
    }
    if (event.price?.amount) {
      // Os valores vêm em centavos da API, então dividimos por 100
      const priceInReais = event.price.amount / 100;
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: event.price.currency || "BRL",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(priceInReais);
    }
    return "Preço não informado";
  }, [event.price]);

  const eventLocation = useMemo(() => {
    if (event.location?.type === "online") {
      return "Evento Online";
    }

    // Construir endereço a partir dos dados da API
    const parts = [];
    if (event.location?.address) parts.push(event.location.address);
    if (event.location?.city) parts.push(event.location.city);
    if (event.location?.state) parts.push(event.location.state);

    if (parts.length > 0) {
      return parts.join(", ");
    }

    return event.location?.name || "Local não informado";
  }, [event.location]);

  const primaryImage = useMemo(() => {
    return (
      event.images?.find((img) => img.isPrimary)?.url || event.images?.[0]?.url
    );
  }, [event.images]);

  const handleEventPress = useCallback(() => {
    if (onEventPress) {
      onEventPress(event.id);
    } else {
      router.push(`/(events)/event-sale?id=${event.id}`);
    }
  }, [event.id, onEventPress, router]);

  const handleOrganizerPress = useCallback(() => {
    const organizerId = event.organizer?.id || (event as any).user?.id;
    if (organizerId) {
      router.push(`/(main)/profile/${organizerId}`);
    }
  }, [event.organizer?.id, (event as any).user?.id, router]);

  if (isLoading) {
    return (
      <View style={[styles.eventCard, styles.loadingCard]}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  return (
    <View style={styles.eventCard}>
      <View style={styles.eventHeader}>
        <TouchableOpacity onPress={handleOrganizerPress}>
          <Avatar
            user={event.organizer || (event as any).user}
            size={40}
            borderSize={2}
          />
        </TouchableOpacity>
        <View style={styles.eventContent}>
          <View style={styles.eventUserInfo}>
            <Text style={styles.organizerName}>
              {event.organizer?.name ||
                (event as any).user?.name ||
                "Organizador"}
            </Text>
            <Text style={styles.eventActionText}>publicou um novo evento</Text>
          </View>
          <View style={styles.eventDateInfo}>
            <Text style={styles.eventDatePrefix}>em</Text>
            <Text style={styles.eventDate}>{formattedHeaderDate}</Text>
          </View>
        </View>
      </View>

      <TouchableOpacity onPress={handleEventPress} activeOpacity={0.8}>
        <Text style={styles.eventTitle}>{event.title}</Text>

        <Text style={styles.eventDescription}>
          {event.shortDescription || event.description}
        </Text>

        {!!primaryImage && (
          <Image
            source={{uri: primaryImage}}
            style={styles.eventImg}
            resizeMode="cover"
          />
        )}

        <View style={styles.eventDetails}>
          <View style={styles.detailRow}>
            <ClockIcon width={16} height={16} stroke="#FFFFFF" />
            <Text style={styles.detailText}>{formattedTime}</Text>
          </View>

          <View style={styles.detailRow}>
            <MarkerPinIcon width={16} height={16} stroke="#FFFFFF" />
            <Text style={styles.detailText}>{eventLocation}</Text>
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.divider} />

      <View style={styles.eventActions}>
        <View style={styles.priceContainer}>
          <Text style={styles.eventText}>Valor</Text>
          <Text style={styles.eventPrice}>{formattedPrice}</Text>
        </View>
        <Button
          text="Ver evento"
          onPress={handleEventPress}
          style={styles.viewEventButton}
          textStyle={styles.viewEventButtonText}
        />
      </View>
    </View>
  );
};

export default EventCard;
