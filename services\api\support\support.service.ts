/**
 * Support Service
 * Implements POST /api/app/support using FormDataClient
 */

import {FormDataClient} from "../base/form-data-client";
import {ApiLogger} from "../base/api-logger";

export interface CreateSupportTicketRequest {
  Type: string; // category/type of the ticket
  Message: string; // user message (may include subject line)
  attachment?: any; // React Native file object { uri, name, type }
}

export interface SupportTicketViewModel {
  id: number;
  type?: string;
  message?: string;
  createdAt?: string;
}

export class SupportService {
  private static readonly BASE_PATH = "/api/app/support";

  /**
   * Create a support ticket
   */
  static async createSupportTicket(
    data: CreateSupportTicketRequest
  ): Promise<SupportTicketViewModel> {
    ApiLogger.info("Criando ticket de suporte", {type: data.Type});

    const formData = new FormData();
    formData.append("Type", data.Type);
    formData.append("Message", data.Message);

    if (data.attachment) {
      formData.append("attachment", data.attachment as any);
    }

    const response = await FormDataClient.post<SupportTicketViewModel>(
      this.BASE_PATH,
      formData
    );

    ApiLogger.info("Ticket de suporte criado com sucesso", {
      id: response.data?.id,
      type: data.Type
    });

    return response.data;
  }
}

export default SupportService;

