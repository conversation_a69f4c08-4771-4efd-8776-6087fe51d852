/**
 * Terms Modal Component
 * Displays terms and conditions content with accept/decline actions
 */

import React, {useState} from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Alert
} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "@/styles/components/terms/terms-modal.style";
import {ApiTerm, getLatestVersionFromApiTerm} from "@/models/api/terms.models";
import ChevronLeftIcon from "@/components/icons/chevron-left-icon";
import CheckIcon from "@/components/icons/check-icon";

export interface TermsModalProps {
  visible: boolean;
  term: ApiTerm | null;
  onAccept: (termId: number, version: number) => void;
  onDecline: () => void;
  onClose: () => void;
  title?: string;
  acceptButtonText?: string;
  declineButtonText?: string;
  isLoading?: boolean;
}

const TermsModal: React.FC<TermsModalProps> = ({
  visible,
  term,
  onAccept,
  onDecline,
  onClose,
  title,
  acceptButtonText,
  declineButtonText,
  isLoading = false
}) => {
  const {t} = useTranslation();
  const [isAccepting, setIsAccepting] = useState(false);

  // Get the latest version of the term
  const latestVersion = term ? getLatestVersionFromApiTerm(term) : null;

  // Função para limpar HTML e entidades
  const cleanHtmlContent = (htmlContent: string): string => {
    return htmlContent
      .replace(/<[^>]*>/g, "") // Remove tags HTML
      .replace(/&nbsp;/g, " ") // Converte &nbsp; para espaço
      .replace(/&amp;/g, "&") // Converte &amp; para &
      .replace(/&lt;/g, "<") // Converte &lt; para <
      .replace(/&gt;/g, ">") // Converte &gt; para >
      .replace(/&quot;/g, '"') // Converte &quot; para "
      .trim(); // Remove espaços extras
  };

  const handleAccept = async () => {
    if (!term || !latestVersion) {
      Alert.alert(t("terms.error.title"), t("terms.error.noTermsFound"));
      return;
    }

    try {
      setIsAccepting(true);
      onAccept(term.id, latestVersion.version);
    } catch (error) {
      console.error("❌ [TERMS-MODAL] Erro ao aceitar termos:", error);
      Alert.alert(t("terms.error.title"), t("terms.error.acceptFailed"));
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDecline = () => {
    Alert.alert(t("terms.decline.title"), t("terms.decline.message"), [
      {
        text: t("common.cancel"),
        style: "cancel"
      },
      {
        text: t("terms.decline.confirm"),
        style: "destructive",
        onPress: onDecline
      }
    ]);
  };

  if (!term || !latestVersion) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            disabled={isLoading || isAccepting}
          >
            <ChevronLeftIcon size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>
            {title || term.name || t("terms.title")}
          </Text>

          <View style={styles.headerSpacer} />
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.termContent}>
            <Text style={styles.termText}>
              {cleanHtmlContent(latestVersion.content)}
            </Text>
          </View>

          {/* Version info */}
          <View style={styles.versionInfo}>
            <Text style={styles.versionText}>
              {t("terms.version", {version: latestVersion.version})}
            </Text>
            <Text style={styles.dateText}>
              {new Date(latestVersion.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.declineButton]}
            onPress={handleDecline}
            disabled={isLoading || isAccepting}
          >
            <Text style={styles.declineButtonText}>
              {declineButtonText || t("terms.decline.button")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.acceptButton]}
            onPress={handleAccept}
            disabled={isLoading || isAccepting}
          >
            {isAccepting ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <CheckIcon size={20} color="#FFFFFF" />
                <Text style={styles.acceptButtonText}>
                  {acceptButtonText || t("terms.accept.button")}
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default TermsModal;
