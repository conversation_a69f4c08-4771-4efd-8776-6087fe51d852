import React, {useState, useCallback, useEffect, useRef} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Clipboard
} from "react-native";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useTranslation} from "react-i18next";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import FullSizeButton from "@/components/full-size-button";
import {useCreatePayment, usePIXDetails} from "@/hooks/api/use-payments";
import {
  PaymentEntity,
  PaymentType,
  CreatePaymentRequest
} from "@/models/api/payments.models";
import {useCurrentUser} from "@/hooks/api/use-auth";
import {tokenManager} from "@/services/api/auth/token-manager";

// Estilos temporários inline
const styles = {
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A"
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#FFFFFF",
    textAlign: "center" as const,
    fontFamily: "Ubuntu"
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 20
  },
  errorText: {
    fontSize: 16,
    color: "#FF6B6B",
    textAlign: "center" as const,
    marginBottom: 24,
    fontFamily: "Ubuntu"
  },
  progressContainer: {
    marginBottom: 32
  },
  progressTitle: {
    fontSize: 24,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  progressStep: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 16,
    fontFamily: "Ubuntu"
  },
  progressBar: {
    height: 4,
    backgroundColor: "#333333",
    borderRadius: 2
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#00D4AA",
    borderRadius: 2
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    fontSize: 20,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  subtitle: {
    fontSize: 16,
    color: "#CCCCCC",
    lineHeight: 24,
    fontFamily: "Ubuntu"
  },
  summaryContainer: {
    backgroundColor: "#2A2A2A",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    marginBottom: 12,
    fontFamily: "Ubuntu"
  },
  summaryRow: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    marginBottom: 8
  },
  summaryLabel: {
    fontSize: 14,
    color: "#CCCCCC",
    fontFamily: "Ubuntu"
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },
  summaryValueHighlight: {
    fontSize: 16,
    fontWeight: "bold" as const,
    color: "#00D4AA",
    fontFamily: "Ubuntu"
  },
  pixContainer: {
    backgroundColor: "#2A2A2A",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24
  },
  qrCodeContainer: {
    alignItems: "center" as const,
    marginBottom: 24
  },
  qrCodeImage: {
    width: 200,
    height: 200,
    backgroundColor: "#FFFFFF",
    borderRadius: 8
  },
  pixCodeContainer: {
    marginBottom: 24
  },
  pixCodeLabel: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  pixCodeBox: {
    backgroundColor: "#333333",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12
  },
  pixCodeText: {
    fontSize: 12,
    color: "#FFFFFF",
    fontFamily: "monospace"
  },
  copyButton: {
    backgroundColor: "#00D4AA",
    borderRadius: 8,
    padding: 12,
    alignItems: "center" as const
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    fontFamily: "Ubuntu"
  },
  expirationContainer: {
    marginBottom: 24
  },
  expirationText: {
    fontSize: 14,
    color: "#FF6B6B",
    textAlign: "center" as const,
    fontFamily: "Ubuntu"
  },
  instructionsContainer: {
    marginBottom: 24
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "bold" as const,
    color: "#FFFFFF",
    marginBottom: 12,
    fontFamily: "Ubuntu"
  },
  instructionText: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 8,
    fontFamily: "Ubuntu"
  },
  buttonContainer: {
    flexDirection: "row" as const,
    gap: 12,
    paddingTop: 20
  },
  backButton: {
    flex: 1
  },
  nextButton: {
    flex: 1
  }
};

const PIXPayment: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [isCreatingPayment, setIsCreatingPayment] = useState(false);
  // Guard to prevent duplicate creation in React Strict Mode (mount -> unmount -> mount)
  const hasCreatedRef = useRef(false);

  console.log("🔍 [PIX-PAYMENT] Componente carregado");
  console.log("🔍 [PIX-PAYMENT] Params iniciais:", params);

  const {
    data: currentUser,
    isLoading: isLoadingUser,
    error: userError
  } = useCurrentUser();
  const createPaymentMutation = useCreatePayment();

  console.log("🔍 [PIX-PAYMENT] Estado do usuário:", {
    currentUser,
    isLoadingUser,
    userError
  });

  // Buscar detalhes do PIX apenas se o pagamento foi criado
  const {
    data: pixDetails,
    isLoading: isLoadingPIX,
    error: pixError
  } = usePIXDetails(paymentId || "", !!paymentId);

  const formatCurrency = useCallback((value: string) => {
    const numValue = parseFloat(value) / 100;
    return `R$ ${numValue.toFixed(2).replace(".", ",")}`;
  }, []);

  const createTitlePayment = useCallback(async () => {
    console.log("🔍 [PIX-PAYMENT] createTitlePayment iniciado");
    console.log("🔍 [PIX-PAYMENT] currentUser:", currentUser);
    console.log("🔍 [PIX-PAYMENT] params recebidos:", params);

    // Verificar token atual
    const currentToken = await tokenManager.getAccessToken();
    console.log(
      "🔍 [PIX-PAYMENT] Token atual:",
      currentToken ? "Presente" : "Ausente"
    );

    // Verificar dados do token
    const tokenData = await tokenManager.getTokenData();
    console.log("🔍 [PIX-PAYMENT] Token data:", tokenData);

    if (!currentUser) {
      console.log("❌ [PIX-PAYMENT] currentUser é null/undefined");
      Alert.alert(
        t("pixPayment.error.title", "Erro"),
        t("pixPayment.error.userNotFound", "Usuário não encontrado")
      );
      return;
    }

    setIsCreatingPayment(true);

    try {
      const paymentRequest: CreatePaymentRequest = {
        entity: PaymentEntity.Title,
        entityId: currentUser.id, // enviar ID do usuário atual
        type: PaymentType.Pix,
        pix: {
          installmentCount: parseInt(params.selectedInstallments as string) || 1
        }
      };

      console.log(
        "🚀 [PIX-PAYMENT] Criando pagamento do título:",
        paymentRequest
      );
      console.log(
        "🚀 [PIX-PAYMENT] PaymentEntity.Title =",
        PaymentEntity.Title
      );
      console.log("🚀 [PIX-PAYMENT] PaymentType.Pix =", PaymentType.Pix);

      const response = await createPaymentMutation.mutateAsync(paymentRequest);

      console.log("✅ [PIX-PAYMENT] Pagamento criado:", response);
      setPaymentId(response.id);
    } catch (error) {
      console.error("❌ [PIX-PAYMENT] Erro ao criar pagamento:", error);
      console.error(
        "❌ [PIX-PAYMENT] Erro detalhado:",
        JSON.stringify(error, null, 2)
      );

      // Verificar se é erro específico de usuário não encontrado
      const errorMessage = error?.message || "";
      if (
        errorMessage.includes("não encontrado") ||
        errorMessage.includes("not found")
      ) {
        Alert.alert(
          t("pixPayment.error.title", "Erro"),
          t(
            "pixPayment.error.userNotFound",
            "Usuário não encontrado. Verifique se o login foi realizado corretamente."
          )
        );
      } else {
        Alert.alert(
          t("pixPayment.error.title", "Erro"),
          t(
            "pixPayment.error.createPayment",
            "Erro ao criar pagamento. Tente novamente."
          )
        );
      }
    } finally {
      setIsCreatingPayment(false);
    }
  }, [currentUser, params, createPaymentMutation, t]);

  const copyPixCode = useCallback(async () => {
    if (pixDetails?.payload) {
      await Clipboard.setString(pixDetails.payload);
      Alert.alert(
        t("pixPayment.success.title", "Sucesso"),
        t(
          "pixPayment.success.copied",
          "Código PIX copiado para a área de transferência"
        )
      );
    }
  }, [pixDetails, t]);

  const handleNext = useCallback(() => {
    // Navegar para a próxima etapa (pagamento do plano)
    router.push({
      pathname: "/(registration)/plan-payment-selection",
      params: {
        ...params,
        titlePaymentId: paymentId
      }
    });
  }, [router, params, paymentId]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  // Criar pagamento automaticamente quando a tela carrega (uma única vez)
  useEffect(() => {
    console.log("🔍 [PIX-PAYMENT] useEffect executado:", {
      paymentId,
      isCreatingPayment,
      currentUser
    });

    if (!hasCreatedRef.current && !paymentId && !isCreatingPayment) {
      hasCreatedRef.current = true;
      createTitlePayment();
    }
  }, [paymentId, isCreatingPayment, createTitlePayment]);

  // Carregar token do storage quando a tela carrega
  useEffect(() => {
    const loadTokenFromStorage = async () => {
      console.log("🔍 [PIX-PAYMENT] Carregando token do storage...");
      const tokenData = await tokenManager.loadTokenFromStorage();
      console.log("🔍 [PIX-PAYMENT] Token carregado do storage:", tokenData);
    };

    loadTokenFromStorage();
  }, []);

  const formatExpirationDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }, []);

  if (isCreatingPayment) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text style={styles.loadingText}>
            {t("pixPayment.creating", "Gerando código PIX...")}
          </Text>
        </View>
      </Screen>
    );
  }

  if (isLoadingPIX) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00D4AA" />
          <Text style={styles.loadingText}>
            {t("pixPayment.loading", "Carregando código PIX...")}
          </Text>
        </View>
      </Screen>
    );
  }

  if (pixError) {
    return (
      <Screen>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t(
              "pixPayment.error.loadPix",
              "Erro ao carregar código PIX. Tente novamente."
            )}
          </Text>
          <FullSizeButton
            text={t("common.tryAgain", "Tentar novamente")}
            onPress={createTitlePayment}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <BackButton />

        <View style={styles.progressContainer}>
          <Text style={styles.progressTitle}>
            {t("pixPayment.createAccount", "Criar conta")}
          </Text>
          <Text style={styles.progressStep}>
            {t("pixPayment.stepProgress", "4 / 7 Pagamento do título")}
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, {width: "56%"}]} />
          </View>
        </View>

        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t("pixPayment.title", "Pagamento PIX")}
          </Text>
          <Text style={styles.subtitle}>
            {t(
              "pixPayment.description",
              "Escaneie o QR Code ou copie o código PIX para efetuar o pagamento."
            )}
          </Text>
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>
            {t("pixPayment.summary", "Resumo do pagamento")}
          </Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("pixPayment.item", "Item:")}
            </Text>
            <Text style={styles.summaryValue}>
              {t("pixPayment.titleAdhesion", "Título de Adesão")}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("pixPayment.installments", "Parcelamento:")}
            </Text>
            <Text style={styles.summaryValue}>
              {params.selectedInstallments === "1"
                ? t("pixPayment.singlePayment", "À vista")
                : t("pixPayment.installmentsCount", "{{count}}x", {
                    count: params.selectedInstallments
                  })}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("pixPayment.total", "Total:")}
            </Text>
            <Text style={styles.summaryValueHighlight}>
              {formatCurrency(params.totalValue as string)}
            </Text>
          </View>
        </View>

        {pixDetails && (
          <View style={styles.pixContainer}>
            <View style={styles.qrCodeContainer}>
              <Image
                source={{
                  uri: `data:image/png;base64,${pixDetails.encodedImage}`
                }}
                style={styles.qrCodeImage}
                resizeMode="contain"
              />
            </View>

            <View style={styles.pixCodeContainer}>
              <Text style={styles.pixCodeLabel}>
                {t("pixPayment.pixCode", "Código PIX:")}
              </Text>
              <View style={styles.pixCodeBox}>
                <Text style={styles.pixCodeText} numberOfLines={3}>
                  {pixDetails.payload}
                </Text>
              </View>
              <TouchableOpacity style={styles.copyButton} onPress={copyPixCode}>
                <Text style={styles.copyButtonText}>
                  {t("pixPayment.copyCode", "Copiar código")}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.expirationContainer}>
              <Text style={styles.expirationText}>
                {t("pixPayment.expiresAt", "Expira em: {{date}}", {
                  date: formatExpirationDate(pixDetails.expirationDate)
                })}
              </Text>
            </View>

            <View style={styles.instructionsContainer}>
              <Text style={styles.instructionsTitle}>
                {t("pixPayment.instructions.title", "Como pagar:")}
              </Text>
              <Text style={styles.instructionText}>
                {t(
                  "pixPayment.instructions.step1",
                  "1. Abra o app do seu banco"
                )}
              </Text>
              <Text style={styles.instructionText}>
                {t("pixPayment.instructions.step2", "2. Escolha a opção PIX")}
              </Text>
              <Text style={styles.instructionText}>
                {t(
                  "pixPayment.instructions.step3",
                  "3. Escaneie o QR Code ou cole o código"
                )}
              </Text>
              <Text style={styles.instructionText}>
                {t("pixPayment.instructions.step4", "4. Confirme o pagamento")}
              </Text>
            </View>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <View style={styles.backButton}>
            <FullSizeButton
              text={t("common.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={styles.nextButton}>
            <FullSizeButton
              text={t("pixPayment.continue", "Continuar")}
              onPress={handleNext}
              disabled={!pixDetails}
            />
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
};

export default PIXPayment;
