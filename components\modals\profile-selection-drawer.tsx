import React, {useState, useCallback} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Alert,
  ListRenderItemInfo,
  ScrollView
} from "react-native";

import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import EraserIcon from "../icons/eraser-icon";
import PlusIcon from "../icons/plus-icon";
import {showSuccessToast} from "@/utils/error-handler";
import styles from "@/styles/modals/profile-selection-drawer.style";

export interface ProfileSelectionItem {
  id: number;
  name: string;
  description?: string;
}

export interface ProfileSelectionDrawerProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  description: string;
  items: ProfileSelectionItem[];
  selectedItems: ProfileSelectionItem[];
  onItemsSelected: (items: ProfileSelectionItem[]) => void;
  onSave: (items: ProfileSelectionItem[]) => Promise<void>;
  onCreateNew?: (name: string) => Promise<ProfileSelectionItem>;
  onOpenAdd?: () => void; // Abre um drawer para adicionar múltiplos itens
  loading?: boolean;
  createLoading?: boolean;
  minSelection?: number;
  hideSelectionBalls?: boolean; // Nova prop para esconder as bolinhas de seleção
}

const ProfileSelectionDrawer: React.FC<ProfileSelectionDrawerProps> = ({
  visible,
  onClose,
  title,
  description,
  items,
  selectedItems,
  onItemsSelected,
  onSave,
  onCreateNew,
  onOpenAdd,
  loading = false,
  createLoading = false,
  minSelection = 3,
  hideSelectionBalls = false
}) => {
  // Mantemos os IDs sempre normalizados como number para evitar divergências
  const normalizeId = (id: number | string) => {
    const n = typeof id === "string" ? parseInt(id, 10) : id;
    return Number.isFinite(n) ? n : NaN;
  };

  const [selectedItemIds, setSelectedItemIds] = useState<Set<number>>(
    new Set(
      (selectedItems || [])
        .map((item) => normalizeId(item.id))
        .filter((n): n is number => Number.isFinite(n))
    )
  );
  const [showAddInput, setShowAddInput] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [saving, setSaving] = useState(false);

  // Atualizar seleção quando selectedItems mudar (ex.: após refetch da API)
  React.useEffect(() => {
    setSelectedItemIds(
      new Set(
        (selectedItems || [])
          .map((item) => normalizeId(item.id))
          .filter((n): n is number => Number.isFinite(n))
      )
    );
  }, [selectedItems]);

  // Sempre que o drawer for aberto, resetar o estado interno para refletir
  // exatamente os itens selecionados vindos da API, descartando alterações não salvas
  React.useEffect(() => {
    if (visible) {
      setSelectedItemIds(
        new Set(
          (selectedItems || [])
            .map((item) => normalizeId(item.id))
            .filter((n): n is number => Number.isFinite(n))
        )
      );
      setShowAddInput(false);
      setNewItemName("");
    }
  }, [visible, selectedItems]);

  const handleItemToggle = useCallback((item: ProfileSelectionItem) => {
    setSelectedItemIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(item.id)) {
        newSet.delete(item.id);
      } else {
        newSet.add(item.id);
      }
      return newSet;
    });
  }, []);

  const handleClearAll = useCallback(() => {
    setSelectedItemIds(new Set());
  }, []);

  const handleAddNew = useCallback(async () => {
    if (!newItemName.trim() || !onCreateNew) return;

    try {
      const newItem = await onCreateNew(newItemName.trim());
      setSelectedItemIds((prev) => new Set([...prev, newItem.id]));
      setNewItemName("");
      setShowAddInput(false);
      showSuccessToast("Sucesso", `${newItemName} adicionado com sucesso!`);
    } catch (error) {
      console.error("[ProfileSelectionDrawer] Erro ao adicionar item:", error);
      Alert.alert(
        "Erro",
        "Não foi possível adicionar o item. Tente novamente."
      );
    }
  }, [newItemName, onCreateNew]);

  const handleSave = useCallback(async () => {
    const selectedItemsList = items.filter((item) => {
      const id = normalizeId(item.id);
      return Number.isFinite(id) ? selectedItemIds.has(id) : false;
    });

    if (selectedItemsList.length < minSelection) {
      Alert.alert(
        "Seleção insuficiente",
        `Selecione pelo menos ${minSelection} itens para continuar.`
      );
      return;
    }

    try {
      setSaving(true);
      await onSave(selectedItemsList);
      onItemsSelected(selectedItemsList);
      showSuccessToast("Sucesso", "Alterações salvas com sucesso!");
      onClose();
    } catch (error) {
      console.error(
        "[ProfileSelectionDrawer] Erro ao salvar alterações:",
        error
      );
      Alert.alert(
        "Erro",
        "Não foi possível salvar as alterações. Tente novamente."
      );
    } finally {
      setSaving(false);
    }
  }, [selectedItemIds, items, onSave, onItemsSelected, onClose, minSelection]);

  const renderItem = useCallback(
    ({item}: ListRenderItemInfo<ProfileSelectionItem>) => {
      const id = normalizeId(item.id);
      const isSelected = Number.isFinite(id) ? selectedItemIds.has(id) : false;
      // Se hideSelectionBalls for true, sempre mostrar como não selecionado visualmente
      const showAsSelected = hideSelectionBalls ? false : isSelected;

      return (
        <TouchableOpacity
          style={[
            styles.itemBadge,
            showAsSelected
              ? styles.itemBadgeSelected
              : styles.itemBadgeUnselected
          ]}
          onPress={() => handleItemToggle(item)}
          activeOpacity={0.7}
        >
          <Text
            style={[
              styles.itemBadgeText,
              showAsSelected
                ? styles.itemBadgeTextSelected
                : styles.itemBadgeTextUnselected
            ]}
          >
            {item.name}
          </Text>
        </TouchableOpacity>
      );
    },
    [selectedItemIds, handleItemToggle, hideSelectionBalls]
  );

  const selectedCount = selectedItemIds.size;
  const canSave = selectedCount >= minSelection;

  console.log("[ProfileSelectionDrawer] visible:", visible, "title:", title);
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="overFullScreen"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.backButton}>
            <ChevronLeftIcon />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <TouchableOpacity onPress={handleClearAll} style={styles.clearButton}>
            <Text style={styles.clearText}>Limpar</Text>
            <EraserIcon />
          </TouchableOpacity>
        </View>

        {/* Description */}
        <Text style={styles.description}>{description}</Text>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#0F7C4D" />
              <Text style={styles.loadingText}>Carregando opções...</Text>
            </View>
          ) : (
            <View style={styles.itemsContainer}>
              {items.map((item) => (
                <View key={item.id} style={styles.itemWrapper}>
                  {renderItem({
                    item,
                    index: 0,
                    separators: {
                      highlight: () => {},
                      unhighlight: () => {},
                      updateProps: () => {}
                    }
                  })}
                </View>
              ))}
            </View>
          )}

          {/* Add New Section */}
          {(onOpenAdd || onCreateNew) && (
            <View style={styles.addNewSection}>
              {(() => {
                if (onOpenAdd) {
                  return (
                    <TouchableOpacity
                      style={styles.addNewButton}
                      onPress={() => {
                        console.log(
                          "[ProfileSelectionDrawer] onOpenAdd pressed for:",
                          title
                        );
                        onOpenAdd?.();
                      }}
                    >
                      <PlusIcon />
                      <Text style={styles.addNewText}>
                        + Adicionar {title.toLowerCase()}(s)
                      </Text>
                    </TouchableOpacity>
                  );
                }
                if (showAddInput && onCreateNew) {
                  return (
                    <View style={styles.addInputContainer}>
                      <TextInput
                        style={styles.addInput}
                        placeholder="Digite o nome..."
                        placeholderTextColor="#90A0AE"
                        value={newItemName}
                        onChangeText={setNewItemName}
                        autoFocus
                      />
                      <TouchableOpacity
                        style={styles.addConfirmButton}
                        onPress={handleAddNew}
                        disabled={!newItemName.trim() || createLoading}
                      >
                        {createLoading ? (
                          <ActivityIndicator size="small" color="#1D2939" />
                        ) : (
                          <Text style={styles.addConfirmText}>Adicionar</Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  );
                }
                return (
                  <TouchableOpacity
                    style={styles.addNewButton}
                    onPress={() => setShowAddInput(true)}
                  >
                    <PlusIcon />
                    <Text style={styles.addNewText}>
                      Adicionar {title.toLowerCase()}
                    </Text>
                  </TouchableOpacity>
                );
              })()}
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <FullSizeButton
            text="Salvar alterações"
            onPress={handleSave}
            disabled={!canSave || saving}
            loading={saving}
          />
          <InvisibleFullSizeButton
            text="Voltar para perfil"
            onPress={onClose}
          />
        </View>
      </View>
    </Modal>
  );
};

export default ProfileSelectionDrawer;
