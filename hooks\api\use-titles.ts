/**
 * Hooks para gerenciamento de títulos
 * Seguindo os padrões estabelecidos no projeto com TanStack Query
 */

import {useQuery} from "@tanstack/react-query";
import {TitlesService} from "@/services/api/titles/titles.service";
import {TitleInstallmentOption} from "@/models/api/payments.models";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys para títulos
export const TITLES_QUERY_KEYS = {
  all: ["titles"] as const,
  installmentOptions: () => [...TITLES_QUERY_KEYS.all, "installment-options"] as const,
};

/**
 * Hook para buscar opções de parcelamento do título
 */
export const useTitleInstallmentOptions = (enabled: boolean = true) => {
  return useQuery({
    queryKey: TITLES_QUERY_KEYS.installmentOptions(),
    queryFn: () => TitlesService.getTitleInstallmentOptions(),
    select: (data) => data.data,
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error) => {
      // Não tentar novamente se for 404 (Not Found) ou 403 (Forbidden)
      if ((error as BaseApiError)?.status === 404 || (error as BaseApiError)?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
  });
};
